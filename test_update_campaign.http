### Test API cập nhật chiến dịch
PUT {{baseUrl}}/api/partner/game-campaign/{{campaignId}}
Authorization: Bearer {{token}}
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="ShopId"

{{shopId}}
--boundary
Content-Disposition: form-data; name="Name"

Tên chiến dịch đã cập nhật
--boundary
Content-Disposition: form-data; name="Description"

Mô tả chiến dịch đã cập nhật
--boundary
Content-Disposition: form-data; name="StartDate"

2025-02-01T00:00:00
--boundary
Content-Disposition: form-data; name="EndDate"

2025-02-28T23:59:59
--boundary
Content-Disposition: form-data; name="Thumbnail"; filename="new-thumbnail.jpg"
Content-Type: image/jpeg

< ./path/to/new-thumbnail.jpg
--boundary--

### Test API cập nhật chiến dịch (chỉ tên)
PUT {{baseUrl}}/api/partner/game-campaign/{{campaignId}}
Authorization: Bearer {{token}}
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="ShopId"

{{shopId}}
--boundary
Content-Disposition: form-data; name="Name"

Tên chiến dịch mới
--boundary--

### Test API lấy danh sách chiến dịch để kiểm tra cập nhật
GET {{baseUrl}}/api/partner/game-campaign?shopId={{shopId}}
Authorization: Bearer {{token}}

### Variables
# @baseUrl = https://your-api-domain.com
# @shopId = your-shop-id
# @campaignId = your-campaign-id
# @token = your-jwt-token
