using App.ECommerce.Repository.Entities;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace App.ECommerce.Resource.Dtos;

public class ShopDto
{
    [Display(Name = "ShopId")]
    [BsonElement("ShopId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string ShopId { get; set; }

    [Display(Name = "PartnerId")]
    [BsonElement("PartnerId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string PartnerId { get; set; }

    [Display(Name = "OaId")]
    [BsonElement("OaId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? OaId { get; set; } = null;

    [Display(Name = "BusinessType")]
    [BsonElement("BusinessType")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? BusinessType { get; set; }

    [Display(Name = "ShopName")]
    [BsonElement("ShopName")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string ShopName { get; set; }

    [Display(Name = "ShopSlogan")]
    [BsonElement("ShopSlogan")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? ShopSlogan { get; set; }

    [Display(Name = "ShopDesc")]
    [BsonElement("ShopDesc")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? ShopDesc { get; set; }

    [Display(Name = "ShopInfo")]
    [BsonElement("ShopInfo")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? ShopInfo { get; set; }

    [Display(Name = "ShopLogo")]
    [BsonElement("ShopLogo")]
    public MediaInfo? ShopLogo { get; set; }

    [Display(Name = "ShopDeeplink")]
    [BsonElement("ShopDeeplink")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? ShopDeeplink { get; set; }

    [Display(Name = "ShopDomain")]
    [BsonElement("ShopDomain")]
    [BsonRepresentation(BsonType.String)]
    [JsonProperty("shopDomain")]
    [DefaultValue("")]
    public string? ShopDomain { get; set; }

    [Display(Name = "ReferralCode")]
    [BsonElement("ReferralCode")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? ReferralCode { get; set; } = null;

    [Display(Name = "PrefixCode")]
    [BsonElement("PrefixCode")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? PrefixCode { get; set; }

    [Display(Name = "StartDate")]
    [BsonElement("StartDate")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("2024-11-15T00:00:00")]
    public DateTime? StartDate { get; set; }

    [Display(Name = "EndDate")]
    [BsonElement("EndDate")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("2025-11-15T00:00:00")]
    public DateTime? EndDate { get; set; }

    [Display(Name = "OpenTime")]
    [BsonElement("OpenTime")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("07:00:00")]
    public string? OpenTime { get; set; }

    [Display(Name = "CloseTime")]
    [BsonElement("CloseTime")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("18:00:00")]
    public string? CloseTime { get; set; }

    [Display(Name = "ShopTheme")]
    [BsonElement("ShopTheme")]
    [DefaultValue(null)]
    public ShopTheme? ShopTheme { get; set; }

    [Display(Name = "ShipCost")]
    [BsonElement("ShipCost")]
    public long ShipCost { get; set; }

    [Display(Name = "ProvinceId")]
    [BsonElement("ProvinceId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("202")]
    public string? ProvinceId { get; set; }

    [Display(Name = "ProvinceName")]
    [BsonElement("ProvinceName")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("Hồ Chí Minh")]
    public string? ProvinceName { get; set; }

    [Display(Name = "DistrictId")]
    [BsonElement("DistrictId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("1448")]
    public string? DistrictId { get; set; }

    [Display(Name = "DistrictName")]
    [BsonElement("DistrictName")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("Quận 6")]
    public string? DistrictName { get; set; }

    [Display(Name = "WardsId")]
    [BsonElement("WardsId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("20604")]
    public string? WardsId { get; set; }

    [Display(Name = "WardsName")]
    [BsonElement("WardsName")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("Phường 4")]
    public string? WardsName { get; set; }

    [Display(Name = "Address")]
    [BsonElement("Address")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("123 Hai Bà Trưng")]
    public string? Address { get; set; }

    [Display(Name = "TransportPrice")]
    [BsonElement("TransportPrice")]
    [DefaultValue(35000)]
    public long? TransportPrice { get; set; } = Constants.TransportPrice;

    [Display(Name = "Longitude")]
    [BsonElement("Longitude")]
    [BsonRepresentation(BsonType.Double)]
    [DefaultValue(0.0)]
    public double? Longitude { get; set; } = 0.0;

    [Display(Name = "Latitude")]
    [BsonElement("Latitude")]
    [BsonRepresentation(BsonType.Double)]
    [DefaultValue(0.0)]
    public double? Latitude { get; set; } = 0.0;

    [Display(Name = "Active")]
    [BsonElement("Active")]
    [BsonRepresentation(BsonType.String)]
    [JsonConverter(typeof(StringEnumConverter))]
    [DefaultValue(TypeActive.Actived)]
    public TypeActive Active { get; set; } = TypeActive.Actived;

    [Display(Name = "Status")]
    [BsonElement("Status")]
    [BsonRepresentation(BsonType.String)] // Mongo
    [JsonConverter(typeof(StringEnumConverter))] // Newtonsoft.Json
    [DefaultValue(TypeStatus.Actived)]
    public TypeStatus Status { get; set; } = TypeStatus.Actived;

    [Display(Name = "Created")]
    [BsonElement("Created")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    public DateTime Created { get; set; } = DateTimes.Now();

    [Display(Name = "Updated")]
    [BsonElement("Updated")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    public DateTime Updated { get; set; } = DateTimes.Now();

    [Display(Name = "Template")]
    [BsonElement("Template")]
    [BsonRepresentation(BsonType.String)]
    public object? Template { get; set; }

    public bool? EnableExpressDelivery { get; set; }
    public bool? EnableInShop { get; set; }
    public decimal? DefaultTaxRate { get; set; }
}