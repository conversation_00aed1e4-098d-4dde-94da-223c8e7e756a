using App.ECommerce.Repository.Entities;

public class SapoOmniConfigDto
{
    public string ShopId { get; set; } = string.Empty;
    public string DomainApi { get; set; } = string.Empty;
    public string? AccessToken { get; set; }
    public string? ClientId { get; set; }
    public string? ClientSecret { get; set; }
    public string? AuthorizationCode { get; set; } 
    public TypeStatus Status { get; set; }
    public DateTime? CreatedDate { get; set; }
    public DateTime? LastSyncAt { get; set; }
}