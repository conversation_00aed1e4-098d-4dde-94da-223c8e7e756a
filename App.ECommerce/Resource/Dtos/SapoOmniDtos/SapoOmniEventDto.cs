using System.Text.Json.Serialization;

using App.ECommerce.Repository.Entities;

namespace App.ECommerce.Resource.Dtos.SapoOmniDtos;

public class SapoOmniEventDto
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("action")]
    public string Action { get; set; } = string.Empty;

    [JsonPropertyName("data")]
    public object? Data { get; set; }

    [JsonPropertyName("created_at")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("domain_api")]
    public string DomainApi { get; set; } = string.Empty;

    // For webhook processing
    public SapoOmniOrderDto? Order { get; set; }
    public SapoOmniConfig? Eshop { get; set; }
}