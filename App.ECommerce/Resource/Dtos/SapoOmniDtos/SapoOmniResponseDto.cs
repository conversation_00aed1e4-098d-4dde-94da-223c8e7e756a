using System.Text.Json.Serialization;

namespace App.ECommerce.Resource.Dtos.SapoOmniDtos;

public class SapoOmniOrdersResponseDto
{
    [JsonPropertyName("orders")]
    public List<SapoOmniOrderDto> Orders { get; set; } = new();

    [JsonPropertyName("metadata")]
    public SapoOmniMetadataDto? Metadata { get; set; }
}

public class SapoOmniProductsResponseDto
{
    [JsonPropertyName("products")]
    public List<SapoOmniProductDto> Products { get; set; } = new();

    [JsonPropertyName("metadata")]
    public SapoOmniMetadataDto? Metadata { get; set; }
}

public class SapoOmniMetadataDto
{
    [JsonPropertyName("total")]
    public int Total { get; set; }

    [JsonPropertyName("page")]
    public int Page { get; set; }

    [JsonPropertyName("limit")]
    public int Limit { get; set; }
}