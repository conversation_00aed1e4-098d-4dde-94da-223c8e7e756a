using Newtonsoft.Json;

namespace App.ECommerce.Resource.Dtos.SapoOmniDtos;

public class SapoOmniProductDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("tenant_id")]
    public long TenantId { get; set; }

    [JsonProperty("created_on")]
    public DateTime CreatedOn { get; set; }

    [JsonProperty("modified_on")]
    public DateTime ModifiedOn { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("brand_id")]
    public long? BrandId { get; set; }

    [JsonProperty("brand")]
    public string Brand { get; set; } = string.Empty;

    [JsonProperty("description")]
    public string Description { get; set; } = string.Empty;

    [JsonProperty("image_path")]
    public string? ImagePath { get; set; }

    [JsonProperty("image_name")]
    public string? ImageName { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("opt1")]
    public string? Opt1 { get; set; }

    [JsonProperty("opt2")]
    public string? Opt2 { get; set; }

    [JsonProperty("opt3")]
    public string? Opt3 { get; set; }

    [JsonProperty("category_id")]
    public long CategoryId { get; set; }

    [JsonProperty("category")]
    public string Category { get; set; } = string.Empty;

    [JsonProperty("category_code")]
    public string CategoryCode { get; set; } = string.Empty;

    [JsonProperty("tags")]
    public string Tags { get; set; } = string.Empty;

    [JsonProperty("medicine")]
    public bool Medicine { get; set; }

    [JsonProperty("product_type")]
    public string ProductType { get; set; } = string.Empty;

    [JsonProperty("variants")]
    public List<SapoOmniVariantDto> Variants { get; set; } = new();

    [JsonProperty("options")]
    public List<SapoOmniProductOptionDto> Options { get; set; } = new();

    [JsonProperty("images")]
    public List<SapoOmniProductImageDto> Images { get; set; } = new();

    [JsonProperty("product_medicines")]
    public object? ProductMedicines { get; set; }
}

public class SapoOmniVariantDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("tenant_id")]
    public long TenantId { get; set; }

    [JsonProperty("location_id")]
    public long LocationId { get; set; }

    [JsonProperty("created_on")]
    public DateTime CreatedOn { get; set; }

    [JsonProperty("modified_on")]
    public DateTime ModifiedOn { get; set; }

    [JsonProperty("category_id")]
    public long CategoryId { get; set; }

    [JsonProperty("brand_id")]
    public long? BrandId { get; set; }

    [JsonProperty("product_id")]
    public long ProductId { get; set; }

    [JsonProperty("composite")]
    public bool Composite { get; set; }

    [JsonProperty("init_price")]
    public decimal InitPrice { get; set; }

    [JsonProperty("init_stock")]
    public decimal InitStock { get; set; }

    [JsonProperty("variant_retail_price")]
    public decimal VariantRetailPrice { get; set; }

    [JsonProperty("variant_whole_price")]
    public decimal VariantWholePrice { get; set; }

    [JsonProperty("variant_import_price")]
    public decimal VariantImportPrice { get; set; }

    [JsonProperty("cost_price")]
    public decimal? CostPrice { get; set; }

    [JsonProperty("image_id")]
    public long? ImageId { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("opt1")]
    public string? Opt1 { get; set; }

    [JsonProperty("opt2")]
    public string? Opt2 { get; set; }

    [JsonProperty("opt3")]
    public string? Opt3 { get; set; }

    [JsonProperty("product_name")]
    public string ProductName { get; set; } = string.Empty;

    [JsonProperty("product_status")]
    public string? ProductStatus { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("sellable")]
    public bool Sellable { get; set; }

    [JsonProperty("sku")]
    public string Sku { get; set; } = string.Empty;

    [JsonProperty("barcode")]
    public string Barcode { get; set; } = string.Empty;

    [JsonProperty("taxable")]
    public bool Taxable { get; set; }

    [JsonProperty("weight_value")]
    public decimal WeightValue { get; set; }

    [JsonProperty("weight_unit")]
    public string WeightUnit { get; set; } = string.Empty;

    [JsonProperty("unit")]
    public string? Unit { get; set; }

    [JsonProperty("packsize")]
    public bool Packsize { get; set; }

    [JsonProperty("packsize_quantity")]
    public decimal? PacksizeQuantity { get; set; }

    [JsonProperty("packsize_root_id")]
    public long? PacksizeRootId { get; set; }

    [JsonProperty("packsize_root_sku")]
    public string? PacksizeRootSku { get; set; }

    [JsonProperty("packsize_root_name")]
    public string? PacksizeRootName { get; set; }

    [JsonProperty("tax_included")]
    public bool TaxIncluded { get; set; }

    [JsonProperty("input_vat_id")]
    public long InputVatId { get; set; }

    [JsonProperty("output_vat_id")]
    public long OutputVatId { get; set; }

    [JsonProperty("input_vat_rate")]
    public decimal? InputVatRate { get; set; }

    [JsonProperty("output_vat_rate")]
    public decimal? OutputVatRate { get; set; }

    [JsonProperty("product_type")]
    public string ProductType { get; set; } = string.Empty;

    [JsonProperty("variant_prices")]
    public List<SapoOmniVariantPriceDto> VariantPrices { get; set; } = new();

    [JsonProperty("inventories")]
    public List<SapoOmniInventoryDto> Inventories { get; set; } = new();

    [JsonProperty("images")]
    public List<SapoOmniVariantImageDto>? Images { get; set; }

    [JsonProperty("composite_items")]
    public object? CompositeItems { get; set; }

    [JsonProperty("warranty")]
    public bool Warranty { get; set; }

    [JsonProperty("warranty_term_id")]
    public long? WarrantyTermId { get; set; }

    [JsonProperty("expiration_alert_time")]
    public DateTime? ExpirationAlertTime { get; set; }

    // Computed properties for easier access
    [JsonIgnore]
    public int? InventoryQuantity => Inventories?.Sum(i => (int)i.Available);

    [JsonIgnore]
    public decimal? RetailPrice => VariantPrices?.FirstOrDefault(p => p.PriceList?.Code == "BANLE")?.Value;

    [JsonIgnore]
    public decimal? WholesalePrice => VariantPrices?.FirstOrDefault(p => p.PriceList?.Code == "BANBUON")?.Value;

    [JsonIgnore]
    public decimal? ImportPrice => VariantPrices?.FirstOrDefault(p => p.PriceList?.Code == "GIANHAP")?.Value;
}

public class SapoOmniVariantPriceDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("value")]
    public decimal Value { get; set; }

    [JsonProperty("included_tax_price")]
    public decimal IncludedTaxPrice { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("price_list_id")]
    public long PriceListId { get; set; }

    [JsonProperty("price_list")]
    public SapoOmniPriceListDto? PriceList { get; set; }
}

public class SapoOmniPriceListDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("tenant_id")]
    public long TenantId { get; set; }

    [JsonProperty("created_on")]
    public DateTime CreatedOn { get; set; }

    [JsonProperty("modified_on")]
    public DateTime ModifiedOn { get; set; }

    [JsonProperty("code")]
    public string Code { get; set; } = string.Empty;

    [JsonProperty("currency_id")]
    public long CurrencyId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("is_cost")]
    public bool IsCost { get; set; }

    [JsonProperty("currency_symbol")]
    public string CurrencySymbol { get; set; } = string.Empty;

    [JsonProperty("currency_iso")]
    public string CurrencyIso { get; set; } = string.Empty;

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("init")]
    public bool Init { get; set; }
}

public class SapoOmniInventoryDto
{
    [JsonProperty("location_id")]
    public long LocationId { get; set; }

    [JsonProperty("variant_id")]
    public long VariantId { get; set; }

    [JsonProperty("mac")]
    public decimal Mac { get; set; }

    [JsonProperty("amount")]
    public int Amount { get; set; }

    [JsonProperty("on_hand")]
    public decimal OnHand { get; set; }

    [JsonProperty("available")]
    public decimal Available { get; set; }

    [JsonProperty("committed")]
    public decimal Committed { get; set; }

    [JsonProperty("incoming")]
    public decimal Incoming { get; set; }

    [JsonProperty("onway")]
    public decimal Onway { get; set; }

    [JsonProperty("min_value")]
    public decimal? MinValue { get; set; }

    [JsonProperty("max_value")]
    public decimal? MaxValue { get; set; }

    [JsonProperty("bin_location")]
    public string? BinLocation { get; set; }

    [JsonProperty("wait_to_pack")]
    public decimal WaitToPack { get; set; }

    [JsonProperty("modified_on")]
    public DateTime? ModifiedOn { get; set; }
}

public class SapoOmniProductOptionDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("position")]
    public int Position { get; set; }

    [JsonProperty("values")]
    public List<string> Values { get; set; } = new();
}

public class SapoOmniProductImageDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("product_id")]
    public long ProductId { get; set; }

    [JsonProperty("position")]
    public int Position { get; set; }

    [JsonProperty("created_on")]
    public DateTime CreatedOn { get; set; }

    [JsonProperty("updated_on")]
    public DateTime UpdatedOn { get; set; }

    [JsonProperty("alt")]
    public string? Alt { get; set; }

    [JsonProperty("width")]
    public int Width { get; set; }

    [JsonProperty("height")]
    public int Height { get; set; }

    [JsonProperty("src")]
    public string Src { get; set; } = string.Empty;
    [JsonProperty("full_path")]
    public string FullPath { get; set; } = string.Empty;
    [JsonProperty("file_name")]
    public string FileName { get; set; } = string.Empty;
}

public class SapoOmniVariantImageDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("variant_id")]
    public long VariantId { get; set; }

    [JsonProperty("src")]
    public string Src { get; set; } = string.Empty;

    [JsonProperty("alt")]
    public string? Alt { get; set; }
}

// Extension methods for easier data access
public static class SapoOmniProductExtensions
{
    public static decimal? GetPriceByCode(this SapoOmniVariantDto variant, string priceCode)
    {
        return variant.VariantPrices?
            .FirstOrDefault(p => p.PriceList?.Code == priceCode)?
            .Value;
    }

    public static decimal GetTotalInventory(this SapoOmniVariantDto variant)
    {
        return variant.Inventories?.Sum(i => i.Available) ?? 0;
    }

    public static decimal GetInventoryByLocation(this SapoOmniVariantDto variant, long locationId)
    {
        return variant.Inventories?
            .FirstOrDefault(i => i.LocationId == locationId)?
            .Available ?? 0;
    }

    public static bool IsInStock(this SapoOmniVariantDto variant)
    {
        return variant.GetTotalInventory() > 0;
    }

    public static string GetDisplayName(this SapoOmniVariantDto variant)
    {
        if (string.IsNullOrEmpty(variant.Opt1))
            return variant.ProductName;

        var options = new List<string>();
        if (!string.IsNullOrEmpty(variant.Opt1)) options.Add(variant.Opt1);
        if (!string.IsNullOrEmpty(variant.Opt2)) options.Add(variant.Opt2);
        if (!string.IsNullOrEmpty(variant.Opt3)) options.Add(variant.Opt3);

        return $"{variant.ProductName} - {string.Join(" / ", options)}";
    }
}