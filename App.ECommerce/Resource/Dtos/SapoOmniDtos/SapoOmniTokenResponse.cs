using Newtonsoft.Json;

namespace App.ECommerce.Resource.Dtos.SapoOmniDtos;

public class SapoOmniTokenResponse
{
    [JsonProperty("access_token")]
    public string AccessToken { get; set; } = string.Empty;
    
    [JsonProperty("token_type")]
    public string TokenType { get; set; } = "Bearer";
    
    [JsonProperty("scope")]
    public string Scope { get; set; } = string.Empty;
    
    [JsonProperty("expires_in")]
    public int? ExpiresIn { get; set; }
    
    [JsonProperty("refresh_token")]
    public string? RefreshToken { get; set; }
    
    [JsonProperty("created_at")]
    public long? CreatedAt { get; set; }
    
    // Additional properties that might be returned
    [JsonProperty("shop_id")]
    public string? ShopId { get; set; }
    
    [JsonProperty("shop_domain")]
    public string? ShopDomain { get; set; }
    
    // Error handling properties
    [JsonProperty("error")]
    public string? Error { get; set; }
    
    [JsonProperty("error_description")]
    public string? ErrorDescription { get; set; }
    
    // Helper properties
    [JsonIgnore]
    public bool IsSuccess => string.IsNullOrEmpty(Error) && !string.IsNullOrEmpty(AccessToken);
}