using Newtonsoft.Json;

namespace App.ECommerce.Resource.Dtos.SapoOmniDtos;

public class SapoOmniProductWebhookDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("action")]
    public string? Action { get; set; } // created, updated, deleted

    [JsonProperty("product")]
    public SapoOmniProductDto? Product { get; set; }
}

public class SapoOmniOrderWebhookDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("action")]
    public string? Action { get; set; } // created, updated, deleted

    [JsonProperty("order")]
    public SapoOmniOrderDto? Order { get; set; }
}

public class SapoOmniCustomerWebhookDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("action")]
    public string? Action { get; set; } // created, updated, deleted

    [JsonProperty("customer")]
    public SapoOmniCustomerDto? Customer { get; set; }
}

public class SapoOmniInventoryWebhookDto
{
    [JsonProperty("inventory_item_id")]
    public long InventoryItemId { get; set; }

    [JsonProperty("location_id")]
    public long LocationId { get; set; }

    [JsonProperty("available")]
    public int Available { get; set; }

    [JsonProperty("variant_id")]
    public long VariantId { get; set; }

    [JsonProperty("product_id")]
    public long ProductId { get; set; }
}

public class SapoOmniWebhookInfo
{
    public long Id { get; set; }
    public string Topic { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class SapoOmniWebhooksResponse
{
    [JsonProperty("webhooks")]
    public List<SapoOmniWebhookData> Webhooks { get; set; } = new();
}

public class SapoOmniWebhookData
{
    [JsonProperty("id")]
    public long Id { get; set; }
    
    [JsonProperty("topic")]
    public string Topic { get; set; } = string.Empty;
    
    [JsonProperty("address")]
    public string Address { get; set; } = string.Empty;
    
    [JsonProperty("format")]
    public string Format { get; set; } = string.Empty;
    
    [JsonProperty("created_at")]
    public DateTime? CreatedAt { get; set; }
    
    [JsonProperty("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}

