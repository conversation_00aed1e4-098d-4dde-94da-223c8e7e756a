using Newtonsoft.Json;
namespace App.ECommerce.Resource.Dtos.SapoOmniDtos;

public class SapoOmniOrderDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("tenant_id")]
    public long TenantId { get; set; }

    [JsonProperty("location_id")]
    public long LocationId { get; set; }

    [JsonProperty("code")]
    public string Code { get; set; } = string.Empty;

    [JsonProperty("created_on")]
    public DateTime CreatedOn { get; set; }

    [JsonProperty("modified_on")]
    public DateTime ModifiedOn { get; set; }

    [JsonProperty("issued_on")]
    public DateTime IssuedOn { get; set; }

    [JsonProperty("ship_on")]
    public DateTime? ShipOn { get; set; }

    [JsonProperty("ship_on_min")]
    public DateTime? ShipOnMin { get; set; }

    [JsonProperty("ship_on_max")]
    public DateTime? ShipOnMax { get; set; }

    [JsonProperty("finalized_on")]
    public DateTime? FinalizedOn { get; set; }

    [JsonProperty("finished_on")]
    public DateTime? FinishedOn { get; set; }

    [JsonProperty("completed_on")]
    public DateTime? CompletedOn { get; set; }

    [JsonProperty("cancelled_on")]
    public DateTime? CancelledOn { get; set; }

    [JsonProperty("account_id")]
    public long? AccountId { get; set; }

    [JsonProperty("assignee_id")]
    public long? AssigneeId { get; set; }

    [JsonProperty("customer_id")]
    public long? CustomerId { get; set; }

    [JsonProperty("customer_data")]
    public SapoOmniCustomerDto? CustomerData { get; set; }

    [JsonProperty("contact_id")]
    public long? ContactId { get; set; }

    [JsonProperty("billing_address")]
    public SapoOmniAddressDto? BillingAddress { get; set; }

    [JsonProperty("shipping_address")]
    public SapoOmniAddressDto? ShippingAddress { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("phone_number")]
    public string? PhoneNumber { get; set; }

    [JsonProperty("reference_number")]
    public string? ReferenceNumber { get; set; }

    [JsonProperty("currency_id")]
    public long CurrencyId { get; set; }

    [JsonProperty("price_list_id")]
    public long? PriceListId { get; set; }

    [JsonProperty("tax_treatment")]
    public string? TaxTreatment { get; set; }

    [JsonProperty("tax_label")]
    public string? TaxLabel { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("packed_status")]
    public string PackedStatus { get; set; } = string.Empty;

    [JsonProperty("fulfillment_status")]
    public string FulfillmentStatus { get; set; } = string.Empty;

    [JsonProperty("received_status")]
    public string ReceivedStatus { get; set; } = string.Empty;

    [JsonProperty("payment_status")]
    public string PaymentStatus { get; set; } = string.Empty;

    [JsonProperty("return_status")]
    public string ReturnStatus { get; set; } = string.Empty;

    [JsonProperty("source_id")]
    public long? SourceId { get; set; }

    [JsonProperty("source_url")]
    public string? SourceUrl { get; set; }

    [JsonProperty("total")]
    public decimal Total { get; set; }

    [JsonProperty("order_discount_rate")]
    public decimal OrderDiscountRate { get; set; }

    [JsonProperty("order_discount_value")]
    public decimal OrderDiscountValue { get; set; }

    [JsonProperty("order_discount_amount")]
    public decimal OrderDiscountAmount { get; set; }

    [JsonProperty("discount_reason")]
    public string? DiscountReason { get; set; }

    [JsonProperty("total_discount")]
    public decimal TotalDiscount { get; set; }

    [JsonProperty("total_tax")]
    public decimal TotalTax { get; set; }

    [JsonProperty("note")]
    public string? Note { get; set; }

    [JsonProperty("tags")]
    public List<string> Tags { get; set; } = new();

    [JsonProperty("delivery_fee")]
    public decimal? DeliveryFee { get; set; }

    [JsonProperty("order_line_items")]
    public List<SapoOmniOrderLineItemDto> OrderLineItems { get; set; } = new();

    [JsonProperty("discount_items")]
    public List<object> DiscountItems { get; set; } = new();

    [JsonProperty("fulfillments")]
    public List<object> Fulfillments { get; set; } = new();

    [JsonProperty("payments")]
    public object? Payments { get; set; }

    [JsonProperty("prepayments")]
    public List<object> Prepayments { get; set; } = new();

    [JsonProperty("order_returns")]
    public List<object> OrderReturns { get; set; } = new();

    [JsonProperty("promotion_redemptions")]
    public List<object> PromotionRedemptions { get; set; } = new();

    [JsonProperty("expected_payment_method_id")]
    public long? ExpectedPaymentMethodId { get; set; }

    [JsonProperty("expected_delivery_type")]
    public string? ExpectedDeliveryType { get; set; }

    [JsonProperty("expected_delivery_provider_id")]
    public long? ExpectedDeliveryProviderId { get; set; }

    [JsonProperty("process_status_id")]
    public long? ProcessStatusId { get; set; }

    [JsonProperty("process_status")]
    public string? ProcessStatus { get; set; }

    [JsonProperty("reason_cancel_id")]
    public long? ReasonCancelId { get; set; }

    [JsonProperty("channel")]
    public string? Channel { get; set; }

    [JsonProperty("from_order_return_id")]
    public long? FromOrderReturnId { get; set; }

    // Legacy fields for backward compatibility
    [JsonProperty("business_version")]
    public int BusinessVersion { get; set; }

    [JsonProperty("reference_url")]
    public string? ReferenceUrl { get; set; }

    [JsonProperty("einvoice_status")]
    public string? EinvoiceStatus { get; set; }
}

public class SapoOmniOrderLineItemDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("created_on")]
    public DateTime CreatedOn { get; set; }

    [JsonProperty("modified_on")]
    public DateTime ModifiedOn { get; set; }

    [JsonProperty("variant_id")]
    public long VariantId { get; set; }

    [JsonProperty("product_id")]
    public long ProductId { get; set; }

    [JsonProperty("product_name")]
    public string ProductName { get; set; } = string.Empty;

    [JsonProperty("variant_name")]
    public string VariantName { get; set; } = string.Empty;

    [JsonProperty("tax_type_id")]
    public long? TaxTypeId { get; set; }

    [JsonProperty("tax_included")]
    public bool TaxIncluded { get; set; }

    [JsonProperty("tax_rate_override")]
    public decimal TaxRateOverride { get; set; }

    [JsonProperty("tax_rate")]
    public decimal TaxRate { get; set; }

    [JsonProperty("tax_amount")]
    public decimal TaxAmount { get; set; }

    [JsonProperty("discount_rate")]
    public decimal DiscountRate { get; set; }

    [JsonProperty("discount_value")]
    public decimal DiscountValue { get; set; }

    [JsonProperty("discount_reason")]
    public string? DiscountReason { get; set; }

    [JsonProperty("discount_amount")]
    public decimal DiscountAmount { get; set; }

    [JsonProperty("note")]
    public string? Note { get; set; }

    [JsonProperty("price")]
    public decimal Price { get; set; }

    [JsonProperty("quantity")]
    public decimal Quantity { get; set; }

    [JsonProperty("is_freeform")]
    public bool IsFreeform { get; set; }

    [JsonProperty("is_composite")]
    public bool IsComposite { get; set; }

    [JsonProperty("line_amount")]
    public decimal LineAmount { get; set; }

    [JsonProperty("sku")]
    public string Sku { get; set; } = string.Empty;

    [JsonProperty("barcode")]
    public string Barcode { get; set; } = string.Empty;

    [JsonProperty("unit")]
    public string? Unit { get; set; }

    [JsonProperty("variant_options")]
    public string? VariantOptions { get; set; }

    [JsonProperty("product_type")]
    public string ProductType { get; set; } = string.Empty;

    [JsonProperty("serials")]
    public object? Serials { get; set; }

    [JsonProperty("lots_dates")]
    public List<object> LotsDate { get; set; } = new();

    // Legacy/additional fields
    [JsonProperty("is_packsize")]
    public bool IsPacksize { get; set; }

    [JsonProperty("pack_size_quantity")]
    public decimal? PackSizeQuantity { get; set; }

    [JsonProperty("pack_size_root_id")]
    public long? PackSizeRootId { get; set; }

    [JsonProperty("warranty")]
    public object? Warranty { get; set; }

    [JsonProperty("distributed_discount_amount")]
    public decimal DistributedDiscountAmount { get; set; }
}

public class SapoOmniCustomerDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("tenant_id")]
    public long TenantId { get; set; }

    [JsonProperty("default_location_id")]
    public long? DefaultLocationId { get; set; }

    [JsonProperty("created_on")]
    public DateTime CreatedOn { get; set; }

    [JsonProperty("modified_on")]
    public DateTime ModifiedOn { get; set; }

    [JsonProperty("code")]
    public string Code { get; set; } = string.Empty;

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("dob")]
    public DateTime? Dob { get; set; }

    [JsonProperty("sex")]
    public string? Sex { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("fax")]
    public string? Fax { get; set; }

    [JsonProperty("phone_number")]
    public string? PhoneNumber { get; set; }

    [JsonProperty("tax_number")]
    public string? TaxNumber { get; set; }

    [JsonProperty("website")]
    public string? Website { get; set; }

    [JsonProperty("customer_group_id")]
    public long? CustomerGroupId { get; set; }

    [JsonProperty("group_name")]
    public string? GroupName { get; set; }

    [JsonProperty("assignee_id")]
    public long? AssigneeId { get; set; }

    [JsonProperty("default_payment_term_id")]
    public long? DefaultPaymentTermId { get; set; }

    [JsonProperty("default_payment_method_id")]
    public long? DefaultPaymentMethodId { get; set; }

    [JsonProperty("default_tax_type_id")]
    public long? DefaultTaxTypeId { get; set; }

    [JsonProperty("default_discount_rate")]
    public decimal? DefaultDiscountRate { get; set; }

    [JsonProperty("default_price_list_id")]
    public long? DefaultPriceListId { get; set; }

    [JsonProperty("tags")]
    public List<string> Tags { get; set; } = new();

    [JsonProperty("addresses")]
    public List<SapoOmniAddressDto> Addresses { get; set; } = new();

    [JsonProperty("contacts")]
    public List<object> Contacts { get; set; } = new();

    [JsonProperty("notes")]
    public List<object> Notes { get; set; } = new();

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("is_default")]
    public bool IsDefault { get; set; }

    [JsonProperty("debt")]
    public decimal Debt { get; set; }

    [JsonProperty("apply_incentives")]
    public object? ApplyIncentives { get; set; }

    [JsonProperty("total_expense")]
    public object? TotalExpense { get; set; }

    [JsonProperty("loyalty_customer")]
    public object? LoyaltyCustomer { get; set; }

    [JsonProperty("sale_order")]
    public object? SaleOrder { get; set; }

    [JsonProperty("social_customers")]
    public List<object> SocialCustomers { get; set; } = new();

    [JsonProperty("customer_group")]
    public SapoOmniCustomerGroupDto? CustomerGroup { get; set; }
}

public class SapoOmniCustomerGroupDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("tenant_id")]
    public long TenantId { get; set; }

    [JsonProperty("created_on")]
    public DateTime CreatedOn { get; set; }

    [JsonProperty("modified_on")]
    public DateTime ModifiedOn { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("name_translate")]
    public string NameTranslate { get; set; } = string.Empty;

    [JsonProperty("code")]
    public string Code { get; set; } = string.Empty;

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("is_default")]
    public bool IsDefault { get; set; }

    [JsonProperty("default_payment_term_id")]
    public long? DefaultPaymentTermId { get; set; }

    [JsonProperty("default_payment_method_id")]
    public long? DefaultPaymentMethodId { get; set; }

    [JsonProperty("default_tax_type_id")]
    public long? DefaultTaxTypeId { get; set; }

    [JsonProperty("default_discount_rate")]
    public decimal DefaultDiscountRate { get; set; }

    [JsonProperty("default_price_list_id")]
    public long? DefaultPriceListId { get; set; }

    [JsonProperty("note")]
    public string? Note { get; set; }
}

public class SapoOmniAddressDto
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("created_on")]
    public DateTime? CreatedOn { get; set; }

    [JsonProperty("modified_on")]
    public DateTime? ModifiedOn { get; set; }

    [JsonProperty("label")]
    public string? Label { get; set; }

    [JsonProperty("first_name")]
    public string? FirstName { get; set; }

    [JsonProperty("last_name")]
    public string? LastName { get; set; }

    [JsonProperty("full_name")]
    public string? FullName { get; set; }

    [JsonProperty("address1")]
    public string? Address1 { get; set; }

    [JsonProperty("address2")]
    public string? Address2 { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("phone_number")]
    public string? PhoneNumber { get; set; }

    [JsonProperty("country")]
    public string? Country { get; set; }

    [JsonProperty("city")]
    public string? City { get; set; }

    [JsonProperty("district")]
    public string? District { get; set; }

    [JsonProperty("ward")]
    public string? Ward { get; set; }

    [JsonProperty("zip_code")]
    public string? ZipCode { get; set; }

    [JsonProperty("full_address")]
    public string? FullAddress { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }
}