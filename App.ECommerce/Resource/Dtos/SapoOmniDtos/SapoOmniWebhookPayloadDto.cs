using System.Text.Json.Serialization;

namespace App.ECommerce.Resource.Dtos.SapoOmniDtos;

public class SapoOmniWebhookPayloadDto
{
    [JsonPropertyName("id")]
    public long Id { get; set; }

    [JsonPropertyName("topic")]
    public string Topic { get; set; } = string.Empty;

    [JsonPropertyName("shop_domain")]
    public string ShopDomain { get; set; } = string.Empty;

    [JsonPropertyName("created_at")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("updated_at")]
    public DateTime UpdatedAt { get; set; }

    [JsonPropertyName("api_version")]
    public string ApiVersion { get; set; } = string.Empty;

    [JsonPropertyName("data")]
    public object? Data { get; set; }
}





