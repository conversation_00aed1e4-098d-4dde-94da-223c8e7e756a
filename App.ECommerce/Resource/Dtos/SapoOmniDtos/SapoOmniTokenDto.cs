public class SapoOmniTokenDto
{
    public string AccessToken { get; set; } = string.Empty;
    public string TokenType { get; set; } = string.Empty;
    public string Scope { get; set; } = string.Empty;
    public List<string> GrantedScopes { get; set; } = new();
    public int? ExpiresIn { get; set; }
    public string ShopId { get; set; } = string.Empty;
    public string DomainApi { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class SapoOmniScopeInfo
{
    public string Scope { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
}