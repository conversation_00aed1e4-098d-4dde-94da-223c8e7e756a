using System.ComponentModel.DataAnnotations;

using App.ECommerce.Units.Enums;

namespace App.ECommerce.Resource.Dtos;

public class SyncServiceConfigDto
{
    [Required]
    public string ShopId { get; set; }

    [Required]
    public SyncServiceEnum SyncService { get; set; }

    public string? AppId { get; set; }
    public string? DomainApi { get; set; }

    public string? SecretKey { get; set; }
    public string? ClientId { get; set; }
    public string? ClientSecret { get; set; }
    public string? AccessToken { get; set; }
    public string? AccessCode { get; set; }
    public string? VerifyToken { get; set; }
    public int? BusinessId { get; set; }
    public string? AdditionalConfig { get; set; }
}

