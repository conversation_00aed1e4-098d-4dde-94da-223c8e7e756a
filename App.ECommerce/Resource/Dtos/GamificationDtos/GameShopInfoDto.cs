namespace App.ECommerce.Resource.Dtos.GamificationDtos
{
    public class GameShopInfoDto
    {
        public string ShopId { get; set; }
        public string GameBrandId { get; set; }
        public string CampaignId { get; set; }
        public bool IsActived { get; set; }
        public string ShopName { get; set; }
        public string ShopAddress { get; set; }
        public string ShopPhone { get; set; }
        public string ShopEmail { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool HasActiveCampaign { get; set; }
        public string CurrentCampaignName { get; set; }
    }
}
