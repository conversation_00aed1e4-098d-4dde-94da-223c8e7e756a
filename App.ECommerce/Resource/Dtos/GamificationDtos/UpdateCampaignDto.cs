using System.ComponentModel.DataAnnotations;

namespace App.ECommerce.Resource.Dtos.GamificationDtos
{
    public class UpdateCampaignDto
    {
        [Required]
        public string CampaignId { get; set; }

        [Required]
        public string ShopId { get; set; }

        public string? Name { get; set; }

        public string? Description { get; set; }

        public IFormFile? Thumbnail { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public bool? IsActive { get; set; }
    }
}
