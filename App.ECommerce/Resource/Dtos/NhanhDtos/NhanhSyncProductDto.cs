﻿namespace App.ECommerce.Resource.Dtos.NhanhDtos;

public class NhanhProductAttributeDto
{
    public string AttributeName { get; set; }
    public int Id { get; set; }
    public string Name { get; set; }
    public string Content { get; set; }
}

public class NhanhProductInventoryDepotDto
{
    public int Remain { get; set; }
    public int Shipping { get; set; }
    public int Holding { get; set; }
    public int Damage { get; set; }
    public int Available { get; set; }
}

public class NhanhProductInventoryDto
{
    public int Remain { get; set; }
    public int Shipping { get; set; }
    public int Holding { get; set; }
    public int Damage { get; set; }
    public int Available { get; set; }
    public Dictionary<string, NhanhProductInventoryDepotDto> Depots { get; set; }
}

public class NhanhProductWebhookDto
{
    public int ProductId { get; set; }
    public string ShopProductId { get; set; }
    public int CategoryId { get; set; }
    public int BrandId { get; set; }
    public int ParentId { get; set; }
    public string Code { get; set; }
    public string Barcode { get; set; }
    public string Name { get; set; }
    public double Price { get; set; }
    public int Vat { get; set; }
    public string Image { get; set; }
    public List<string> Images { get; set; }
    public string Status { get; set; }
    public string Description { get; set; }
    public string Content { get; set; }
    public float? Length { get; set; }
    public float? Width { get; set; }
    public float? Height { get; set; }
    public string CreatedDateTime { get; set; }
    public NhanhProductInventoryDto Inventories { get; set; }
    public List<NhanhProductAttributeDto> Attributes { get; set; }
    public double? Weight { get; set; }
}
public class NhanhProductCategoryDto
{
    public int Id { get; set; }
    public int ParentId { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public int Order { get; set; }
    public string Image { get; set; }
    public string Content { get; set; }
    public int Status { get; set; }
    public List<NhanhProductCategoryDto> Childs { get; set; }
}
