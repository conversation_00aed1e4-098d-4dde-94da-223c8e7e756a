using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

using static App.ECommerce.Controllers.API.UserPartnerController;

namespace App.ECommerce.Resource.Dtos.InputDtos;

public class UpdateUserInfoDto
{
    [Required]
    [DefaultValue("")]
    public string ShopId { get; set; }

    [Required]
    [DefaultValue("")]
    public string UserId { get; set; }

    public UpdateUserInfoAction UpdateAction { get; set; }

    public string? Notes { get; set; }
    public string? ReferralCode { get; set; }
    public string? ReferrerCode { get; set; }
    public List<string>? Tags { get; set; }
    public string? Password { get; set; }
}
