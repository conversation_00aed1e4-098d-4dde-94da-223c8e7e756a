using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using App.ECommerce.Repository.Entities;

namespace App.ECommerce.Resource.Dtos;


public class SapoOmniConfigDto
{
    public string ShopId { get; set; }
    public string DomainApi { get; set; }
    public string AccessToken { get; set; }
    public TypeStatus Status { get; set; } = TypeStatus.Actived;
    public string CliendtId { get; set; }
    public string ClientSecret { get; set; }
    public string? ClientId { get; internal set; }
}

public class UpdateSapoOmniConfigDto
{
    public int Id { get; set; }             
    public string ShopId { get; set; }
    public string DomainApi { get; set; }
    public string AccessToken { get; set; }
}

public class TestSapoOmniConnectionDto
{
    public string ShopId { get; set; }
    public string DomainApi { get; set; }
    public string AccessToken { get; set; }
}

public class SapoOmniConfigSettings
{
    public string ApiUrl { get; set; }
    public string WebhookUrl { get; set; }
    public List<string> Permissions { get; set; }
}