namespace App.ECommerce.Resource.Enums;

public static class SapoOmniConstants
{
    public const string SourceData = "sapo_omni_sync";
    
    public static class OrderStatus
    {
        public const string PlacedOrder = "placed_order";
        public const string CompletedOrder = "completed_order";
        public const string CancelledOrder = "cancelled_order";
        public const string ReturningFulfillment = "returning_fulfillment";
        public const string ReturnedFulfillment = "returned_fulfillment";
        public const string RefundedOrder = "refunded_order";
    }

    public static class SapoOrderStatus
    {
        public const string Draft = "draft";
        public const string Completed = "completed";
        public const string Cancelled = "cancelled";
    }

    public static class ReturnStatus
    {
        public const string Returned = "returned";
        public const string Returning = "returning";
        public const string Paid = "paid";
    }
}

public enum PropertyFormType
{
    Contact = 1,
    Product = 2,
    Deal = 3,
    Variant = 4,
    LineItem = 5
}

public enum PropertyControlType
{
    TextBox = 1,
    Date = 2,
    ComboBox = 3,
    Number = 4,
    Radio = 5,
    ArrayString = 6
}