using App.ECommerce.Units.Abstractions.Entities;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace App.ECommerce.Repository.Entities
{
    public class GamificationShop : EntityAuditBase<Guid>
    {
        [BsonElement("ShopId")]
        public string ShopId { get; set; }

        [BsonElement("GameBrandId")]
        public string GameBrandId { get; set; } //brand id bên hệ thống game ứng với 1 shop

        [BsonElement("CampaignId")]
        public string CampaignId { get; set; } // campaign id bên hệ thống game ứng với 1 shop

        [BsonElement("IsActived")]
        public bool IsActived { get; set; } = true; // trạng thái active/deactive của campaign

    }
}