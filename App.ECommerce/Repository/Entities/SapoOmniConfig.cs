using App.Base.Repository.Entities;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace App.ECommerce.Repository.Entities;

public class SapoOmniConfig
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }

    [BsonElement("ShopId")]
    public string ShopId { get; set; } = string.Empty;

    [BsonElement("DomainApi")]
    public string DomainApi { get; set; } = string.Empty;

    [BsonElement("AccessToken")]
    public string AccessToken { get; set; } = string.Empty;
    [BsonElement("ClientId")]
    public string ClientId { get; set; } = string.Empty;
    [BsonElement("ClientSecret")]
    public string ClientSecret { get; set; } = string.Empty;
    [BsonElement("AuthorizationCode")]
    public string AuthorizationCode { get; set; } = string.Empty;

    [BsonElement("Status")]
    public string Status { get; set; } = "Active";

    [BsonElement("PartnerId")]
    public string? PartnerId { get; set; } = string.Empty;
    [BsonElement("LastSyncAt")]
    public DateTime? LastSyncAt { get; set; }

    [BsonElement("Notes")]
    public string? Notes { get; set; }

    [BsonElement("CreatedDate")]
    public DateTime CreatedDate { get; set; }

    [BsonElement("ModifiedDate")]
    public DateTime? ModifiedDate { get; set; }

    [BsonElement("ModifiedBy")]
    public string? ModifiedBy { get; set; }
}