﻿using App.Base.Repository;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Units;
using AutoMapper;

using log4net;

using MongoDB.Bson;
using MongoDB.Driver;

using Newtonsoft.Json;

namespace App.ECommerce.Repository.Implement;

public class ShopSettingRepository : BaseRepository, IShopSettingRepository
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(ShopSettingRepository));
    private readonly IMongoCollection<ShopSetting> _collectionShopSetting;
    private readonly IMapper _mapper;
    private readonly ICryptoRepository _cryptoRepository;

    public ShopSettingRepository(
        IMapper mapper,
        ICryptoRepository cryptoRepository
    ) : base()
    {
        _mapper = mapper;
        _collectionShopSetting = _database.GetCollection<ShopSetting>($"ShopSetting");

        var indexOptions = new CreateIndexOptions();
        var indexModelItems = new List<CreateIndexModel<ShopSetting>>()
        {
            new CreateIndexModel<ShopSetting>(Builders<ShopSetting>.IndexKeys.Ascending(item => item.ShopId), indexOptions),
            new CreateIndexModel<ShopSetting>(Builders<ShopSetting>.IndexKeys.Ascending(item => item.MiniAppId), indexOptions),
        };
        _collectionShopSetting.Indexes.CreateMany(indexModelItems);

        _cryptoRepository = cryptoRepository;
    }

    public ShopSetting? FindByShopId(string shopId)
    {
        ShopSetting? obj = _collectionShopSetting.Find(x => x.ShopId == shopId).FirstOrDefault();

        if (obj == null)
            return null;
        if (!string.IsNullOrEmpty(obj.ZaloSecretKey))
            obj.ZaloSecretKey = _cryptoRepository.Decrypt(obj.ZaloSecretKey);
        if (!string.IsNullOrEmpty(obj.UserAccessToken))
            obj.UserAccessToken = _cryptoRepository.Decrypt(obj.UserAccessToken);
        if (!string.IsNullOrEmpty(obj.UserRefreshToken))
            obj.UserRefreshToken = _cryptoRepository.Decrypt(obj.UserRefreshToken);

        return obj;
    }

    public ShopSetting? FindShopByAppId(string appId)
    {
        ShopSetting? obj = _collectionShopSetting.Find(x => x.ZaloAppId == appId).FirstOrDefault();

        return obj;
    }

    public List<ShopSetting> FindListOAWithRefreshToken()
    {
        return _collectionShopSetting.Find(x => !string.IsNullOrEmpty(x.ZaloRefreshToken)).ToList();
    }

    public bool ValidateZaloMiniAppId(string miniAppId, string shopId, out string errorMsg)
    {
        errorMsg = string.Empty;
        if (!string.IsNullOrEmpty(miniAppId))
        {
            var miniAppIdExists = _collectionShopSetting.Find(x => x.MiniAppId == miniAppId && x.ShopId != shopId).FirstOrDefault();
            if (miniAppIdExists != null)
            {
                errorMsg = $"ID Ứng dụng mini app đã tồn tại.";
                return false;
            }
        }
        return true;
    }

    public bool ValidateZaloAppId(string zaloAppId, string shopId, out string errorMsg)
    {
        errorMsg = string.Empty;
        if (!string.IsNullOrEmpty(zaloAppId))
        {
            var zaloAppIdExists = _collectionShopSetting.Find(x => x.ZaloAppId == zaloAppId && x.ShopId != shopId).FirstOrDefault();
            if (zaloAppIdExists != null)
            {
                errorMsg = $"ID Ứng dụng gốc đã tồn tại.";
                return false;
            }
        }
        return true;
    }

    public ShopSetting CreateOrUpdateItem(ShopSetting item)
    {
        if (!string.IsNullOrEmpty(item.UserAccessToken))
            item.UserAccessToken = _cryptoRepository.Encrypt(item.UserAccessToken);
        if (!string.IsNullOrEmpty(item.UserRefreshToken))
            item.UserRefreshToken = _cryptoRepository.Encrypt(item.UserRefreshToken);

        var shopFind = _collectionShopSetting.Find(x => x.ShopId == item.ShopId).FirstOrDefault();

        if (shopFind == null)
        {
            ObjectId objectId = ObjectId.GenerateNewId();
            item.Id = objectId.ToString();
            item.ShopSettingId = Guid.NewGuid().ToString();
            item.Created = DateTimes.Now();
            item.Updated = DateTimes.Now();
            _collectionShopSetting.InsertOne(item);
        }
        else
        {
            var updateDefinition = Builders<ShopSetting>.Update
                .Set(x => x.ShopId, item.ShopId)
                .Set(x => x.MiniAppId, item.MiniAppId)
                .Set(x => x.ZaloOAName, item.ZaloOAName)
                .Set(x => x.ZaloAppId, item.ZaloAppId)
                .Set(x => x.OwnerName, item.OwnerName)
                .Set(x => x.ZaloSecretKey, item.ZaloSecretKey)
                .Set(x => x.CheckoutSecretKey, item.CheckoutSecretKey)
                .Set(x => x.Updated, DateTimes.Now())
                .Set(x => x.UserAccessToken, item.UserAccessToken)
                .Set(x => x.UserRefreshToken, item.UserRefreshToken);

            _collectionShopSetting.FindOneAndUpdate(x => x.ShopId == item.ShopId, updateDefinition);
            item.Id = shopFind.Id;
        }

        return item;
    }

    public ShopSetting UpdateToken(string shopId, Zalo_Token obj)
    {
        _log.Info($"UpdateToken ShopId: {shopId} | Zalo_Token: {JsonConvert.SerializeObject(obj)}");

        ShopSetting _item = _collectionShopSetting.Find(x => x.ShopId == shopId).FirstOrDefault();

        if (_item == null) return null;

        if (string.IsNullOrEmpty(obj.Access_Token))
            obj.Access_Token = _item.ZaloAccessToken;

        if (string.IsNullOrEmpty(obj.Refresh_Token))
            obj.Refresh_Token = _item.ZaloRefreshToken;

        var update = Builders<ShopSetting>.Update
            .Set("ZaloAccessToken", obj.Access_Token)
            .Set("ZaloRefreshToken", obj.Refresh_Token)
            .Set("ZaloExpiresIn", obj.ExpiresIn)
            .Set("Updated", DateTimes.Now());

        var filter = Builders<ShopSetting>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<ShopSetting> { IsUpsert = true, ReturnDocument = ReturnDocument.After };

        return _collectionShopSetting.FindOneAndUpdate(filter, update, options);
    }
    public List<ShopSetting> FindListUserWithRefreshToken()
    {
        var list = _collectionShopSetting.Find(x => !string.IsNullOrEmpty(x.UserRefreshToken)).ToList();
        foreach (var obj in list)
        {
            if (!string.IsNullOrEmpty(obj.ZaloSecretKey))
                obj.ZaloSecretKey = _cryptoRepository.Decrypt(obj.ZaloSecretKey);
            if (!string.IsNullOrEmpty(obj.UserAccessToken))
                obj.UserAccessToken = _cryptoRepository.Decrypt(obj.UserAccessToken);
            if (!string.IsNullOrEmpty(obj.UserRefreshToken))
                obj.UserRefreshToken = _cryptoRepository.Decrypt(obj.UserRefreshToken);
        }
        return list;
    }

    public ShopSetting UpdateUserToken(string shopId, string accessToken, string refreshToken)
    {
        var shop = _collectionShopSetting.Find(x => x.ShopId == shopId).FirstOrDefault();
        if (shop == null) return null;
        var encryptedAccessToken = string.IsNullOrEmpty(accessToken) ? null : _cryptoRepository.Encrypt(accessToken);
        var encryptedRefreshToken = string.IsNullOrEmpty(refreshToken) ? null : _cryptoRepository.Encrypt(refreshToken);
        var update = Builders<ShopSetting>.Update
            .Set(x => x.UserAccessToken, encryptedAccessToken)
            .Set(x => x.UserRefreshToken, encryptedRefreshToken)
            .Set(x => x.Updated, DateTimes.Now());
        var filter = Builders<ShopSetting>.Filter.Eq(x => x.Id, shop.Id);
        var options = new FindOneAndUpdateOptions<ShopSetting> { ReturnDocument = ReturnDocument.After };
        var updated = _collectionShopSetting.FindOneAndUpdate(filter, update, options);
        if (!string.IsNullOrEmpty(updated.UserAccessToken))
            updated.UserAccessToken = _cryptoRepository.Decrypt(updated.UserAccessToken);
        if (!string.IsNullOrEmpty(updated.UserRefreshToken))
            updated.UserRefreshToken = _cryptoRepository.Decrypt(updated.UserRefreshToken);
        return updated;
    }

    public ShopSetting? FindByMiniAppId(string miniAppId)
    {
        var obj = _collectionShopSetting.Find(x => x.MiniAppId == miniAppId).FirstOrDefault();
        if (obj == null)
            return null;
        if (!string.IsNullOrEmpty(obj.UserAccessToken))
            obj.UserAccessToken = _cryptoRepository.Decrypt(obj.UserAccessToken);
        if (!string.IsNullOrEmpty(obj.UserRefreshToken))
            obj.UserRefreshToken = _cryptoRepository.Decrypt(obj.UserRefreshToken);
        if (!string.IsNullOrEmpty(obj.ZaloSecretKey))
            obj.ZaloSecretKey = _cryptoRepository.Decrypt(obj.ZaloSecretKey);
        return obj;
    }
}