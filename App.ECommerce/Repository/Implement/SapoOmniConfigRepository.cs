using App.Base.Repository;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using MongoDB.Driver;

namespace App.ECommerce.Repository.Implement;

public class SapoOmniConfigRepository : BaseRepository, ISapoOmniConfigRepository
{
    private readonly IMongoCollection<SapoOmniConfig> _collection;

    public SapoOmniConfigRepository()
    {
        _collection = _database.GetCollection<SapoOmniConfig>("SapoOmniConnect");
    }

    public async Task<SapoOmniConfig> CreateOrUpdate(SapoOmniConfig config)
    {
        var existing = await _collection.Find(x => x.DomainApi == config.DomainApi && x.ShopId == config.ShopId).FirstOrDefaultAsync();
        
        if (existing == null)
        {
            config.CreatedDate = DateTime.Now;
            config.Status = "Active";
            config.DomainApi = config.DomainApi.Trim();
            await _collection.InsertOneAsync(config);
        }
        else
        {
            var update = Builders<SapoOmniConfig>.Update
                .Set(x => x.AccessToken, config.AccessToken)
                .Set(x => x.Notes, config.Notes)
                .Set(x => x.ModifiedDate, DateTime.Now)
                .Set(x => x.ModifiedBy, config.ModifiedBy);
            
            await _collection.UpdateOneAsync(x => x.DomainApi == config.DomainApi && x.ShopId == config.ShopId, update);
            config = existing;
        }
        
        return config;
    }

    public Task<SapoOmniConfig?> FindByShopId(string shopId)
    {
        return _collection.Find(x => x.ShopId == shopId).FirstOrDefaultAsync();
    }

    public Task<SapoOmniConfig?> FindByDomainApi(string domainApi)
    {
        return _collection.Find(x => x.DomainApi == domainApi).FirstOrDefaultAsync();
    }

    public Task<SapoOmniConfig?> FindByDomainApiAndShopId(string domainApi, string shopId)
    {
        return _collection.Find(x => x.DomainApi == domainApi && x.ShopId == shopId).FirstOrDefaultAsync();
    }


    public Task<SapoOmniConfig?> GetByShopId(string shopId)
    {
        return _collection.Find(x => x.ShopId == shopId).FirstOrDefaultAsync();
    }

    public async Task<SapoOmniConfig> UpdateAccessToken(string domainApi, string shopId, string accessToken)
    {
        var update = Builders<SapoOmniConfig>.Update
            .Set(x => x.AccessToken, accessToken)
            .Set(x => x.ModifiedDate, DateTime.Now);
        
        await _collection.UpdateOneAsync(x => x.DomainApi == domainApi && x.ShopId == shopId, update);
        return await _collection.Find(x => x.DomainApi == domainApi && x.ShopId == shopId).FirstOrDefaultAsync();
    }

        public async Task<SapoOmniConfig> UpdateStatus(string domainApi, string shopId, string status)
    {
        var update = Builders<SapoOmniConfig>.Update
            .Set(x => x.Status, status)
            .Set(x => x.ModifiedDate, DateTime.Now);
        
        await _collection.UpdateOneAsync(x => x.DomainApi == domainApi && x.ShopId == shopId, update);
        return await _collection.Find(x => x.DomainApi == domainApi && x.ShopId == shopId).FirstOrDefaultAsync();
    }

    public async Task<SapoOmniConfig> UpdateLastSyncAt(string domainApi, string shopId, DateTime lastSyncAt)
    {
        var update = Builders<SapoOmniConfig>.Update
            .Set(x => x.LastSyncAt, lastSyncAt)
            .Set(x => x.ModifiedDate, DateTime.Now);
        
        await _collection.UpdateOneAsync(x => x.DomainApi == domainApi && x.ShopId == shopId, update);
        return await _collection.Find(x => x.DomainApi == domainApi && x.ShopId == shopId).FirstOrDefaultAsync();
    }

    public async Task<bool> DeleteByDomainApiAndShopId(string domainApi, string shopId)
    {
        var result = await _collection.DeleteOneAsync(x => x.DomainApi == domainApi && x.ShopId == shopId);
        return result.DeletedCount > 0;
    }

    public async Task<bool> DeleteByShopId(string shopId)
    {
        var result = await _collection.DeleteManyAsync(x => x.ShopId == shopId);
        return result.DeletedCount > 0;
    }

    public async Task<bool> ExistsByDomainApiAndShopId(string domainApi, string shopId, Guid? excludeId = null)
    {
        var filter = Builders<SapoOmniConfig>.Filter.And(
            Builders<SapoOmniConfig>.Filter.Eq(x => x.DomainApi, domainApi),
            Builders<SapoOmniConfig>.Filter.Eq(x => x.ShopId, shopId)
        );

        if (excludeId.HasValue)
        {
            filter = Builders<SapoOmniConfig>.Filter.And(
                filter,
                Builders<SapoOmniConfig>.Filter.Ne("Id", excludeId.Value) 
            );
        }

        var count = await _collection.CountDocumentsAsync(filter);
        return count > 0;
    }

    public IMongoCollection<SapoOmniConfig> GetCollection()
    {
        return _collection;
    }
}