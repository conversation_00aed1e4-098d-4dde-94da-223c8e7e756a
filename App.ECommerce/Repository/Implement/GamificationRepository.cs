using App.Base.Repository;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;

using MongoDB.Driver;

namespace App.ECommerce.Repository.Implement;

public class GamificationRepository : BaseRepository, IGamificationRepository
{
    private readonly IMongoCollection<Gamification> _collectionGamificationSettings;

    public GamificationRepository() : base()
    {
        _collectionGamificationSettings = _database.GetCollection<Gamification>("GamificationSettings");
    }
    public async Task<Gamification> GetSettingsAsync()
    {
        return await _collectionGamificationSettings.Find(FilterDefinition<Gamification>.Empty).FirstOrDefaultAsync();
    }

    public async Task UpdateSettingsAsync(Gamification settings)
    {
        var filter = Builders<Gamification>.Filter.Eq(gs => gs.Id, settings.Id);
        await _collectionGamificationSettings.ReplaceOneAsync(filter, settings);
    }

    public async Task InsertSettingsAsync(Gamification settings)
    {
        await _collectionGamificationSettings.InsertOneAsync(settings);
    }
}