﻿using App.Base.Repository;
using App.Base.Utilities;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units;
using MongoDB.Bson;
using MongoDB.Driver;

namespace App.ECommerce.Repository.Implement;

public class ShopRepository : BaseRepository, IShopRepository
{
    private readonly IMongoCollection<Shop> _collectionShop;
    private readonly IMongoCollection<Payment> _collectionPayment;
    public ShopRepository() : base()
    {
        _collectionShop = _database.GetCollection<Shop>($"Shop");
        _collectionPayment = _database.GetCollection<Payment>($"Payment");
        var indexOptions = new CreateIndexOptions();
        var indexModelShop = new List<CreateIndexModel<Shop>>()
        {
            new CreateIndexModel<Shop>(Builders<Shop>.IndexKeys.Ascending(item => item.ShopId), indexOptions),
            new CreateIndexModel<Shop>(Builders<Shop>.IndexKeys.Ascending(item => item.PartnerId), indexOptions),
            new CreateIndexModel<Shop>(Builders<Shop>.IndexKeys.Geo2DSphere(item => item.Location), indexOptions)
        };
        _collectionShop.Indexes.CreateMany(indexModelShop);
    }

    public Shop CreateShop(Shop item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.ShopId = Guid.NewGuid().ToString();
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionShop.InsertOne(item);

        List<Payment> listPayment = new List<Payment>
        {
            new Payment
            {
                PaymentId = Guid.NewGuid().ToString(),
                ShopId = item.ShopId,
                IsActive = true,
                Name = "Thanh toán khi nhận hàng",
                Platform = new List<string>(),
                Position = 0,
                Detail = "Thanh toán COD",
                TypePay = TypePayment.COD,
                Created = DateTimes.Now(),
                Updated = DateTimes.Now(),
            },
            new Payment
            {
                PaymentId = Guid.NewGuid().ToString(),
                ShopId = item.ShopId,
                IsActive = false,
                Name = "Chuyển khoản",
                TypePay = TypePayment.Transfer,
                Platform = new List<string>(),
                Position = 1,
                Detail = "Chuyển khoản",
                Created = DateTimes.Now(),
                Updated = DateTimes.Now(),
            },
            new Payment
            {
                PaymentId = Guid.NewGuid().ToString(),
                ShopId = item.ShopId,
                IsActive = false,
                Platform = new List<string>(),
                Position = 2,
                Name = "VNPAY",
                Detail = "Thanh toán VNPAY",
                TypePay = TypePayment.Vnpay,
                Created = DateTimes.Now(),
                Updated = DateTimes.Now(),
            }
        };
        _collectionPayment.InsertMany(listPayment);

        return item;
    }

    public Shop RestoreShop(Shop item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.ShopId = (!string.IsNullOrEmpty(item.ShopId) ? item.ShopId : Guid.NewGuid().ToString());
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionShop.InsertOne(item);
        return item;
    }

    public Shop DeleteShop(string shopId)
    {
        _collectionPayment.DeleteMany(x => x.ShopId == shopId);
        return _collectionShop.FindOneAndDelete(item => item.ShopId == shopId);
    }

    public Shop? FindByShopId(string shopId)
    {
        return _collectionShop.Find(item => item.ShopId == shopId).FirstOrDefault();
    }

    public List<Shop> FindByShopIds(string shopIds)
    {
        var listIds = shopIds.Split(',');
        var filter = Builders<Shop>.Filter.In(x => x.ShopId, listIds);
        return _collectionShop.Find(filter).ToList();
    }

    public List<Shop> FindListByPartnerId(string partnerId)
    {
        return _collectionShop.Find(item => item.PartnerId == partnerId).ToList();
    }

    public PagingResult<Shop> ListShop(Paging paging, string? partnerId = null, TypeActive? active = null)
    {
        PagingResult<Shop> result = new PagingResult<Shop>();
        FilterDefinition<Shop> filterBuilders = Builders<Shop>.Filter.And(
            Builders<Shop>.Filter.Where(p => partnerId == null || p.PartnerId == partnerId),
            Builders<Shop>.Filter.Where(p => active == null || p.Active == active),
            Builders<Shop>.Filter.Or(
                Builders<Shop>.Filter.Where(x => string.IsNullOrEmpty(paging.Search)),
                Builders<Shop>.Filter.Regex(x => x.ShopName, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i"))
            )
        );

        var query = _collectionShop.Find(filterBuilders);
        result.Total = query.ToList().Count;
        result.Result = query.Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}").Skip(paging.PageIndex * paging.PageSize).Limit(paging.PageSize).ToList();
        return result;
    }

    public List<Shop> FindAll(TypeActive? active = null)
    {
        return _collectionShop.Find(item => (active == null || item.Active == active)).SortByDescending(item => item.Updated).ToList();
    }

    public Shop? UpdateShop(Shop item)
    {
        Shop _item = _collectionShop.Find(x => x.ShopId == item.ShopId).FirstOrDefault();
        if (_item == null) return null;

        if(item.BusinessType == null)
            item.BusinessType = _item.BusinessType;

        var update = Builders<Shop>.Update
            .Set("ShopId", item.ShopId)
            .Set("PartnerId", item.PartnerId)
            .Set("OaId", item.OaId)
            .Set("BusinessType", item.BusinessType)
            .Set("ShopName", item.ShopName)
            .Set("ShopSlogan", item.ShopSlogan)
            .Set("ShopDesc", item.ShopDesc)
            .Set("ShopInfo", item.ShopInfo)
            .Set("ShopLogo", item.ShopLogo)
            .Set("ShopDeeplink", item.ShopDeeplink)
            .Set("ReferralCode", item.ReferralCode)
            .Set("PrefixCode", item.PrefixCode)
            .Set("StartDate", item.StartDate)
            .Set("EndDate", item.EndDate)
            .Set("OpenTime", item.OpenTime)
            .Set("CloseTime", item.CloseTime)
            .Set("ShopTheme", item.ShopTheme)
            .Set("ProvinceId", item.ProvinceId)
            .Set("ProvinceName", item.ProvinceName)
            .Set("DistrictId", item.DistrictId)
            .Set("DistrictName", item.DistrictName)
            .Set("WardsId", item.WardsId)
            .Set("WardsName", item.WardsName)
            .Set("Address", item.Address)
            .Set("TransportPrice", item.TransportPrice)
            .Set("Active", item.Active)
            .Set("Status", item.Status)
            .Set("EnableInShop", item.EnableInShop)
            .Set("EnableExpressDelivery", item.EnableExpressDelivery)
            .Set("Created", _item.Created)
            .Set("Updated", DateTimes.Now());
        var filter = Builders<Shop>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<Shop> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        return _collectionShop.FindOneAndUpdate(filter, update, options);
    }

    public async Task<long> TotalShop(string? partnerId)
    {
        FilterDefinition<Shop> filterBuilders = Builders<Shop>.Filter.And(
            Builders<Shop>.Filter.Where(p => partnerId == null || p.PartnerId == partnerId)
        );
        return await _collectionShop.CountDocumentsAsync(filterBuilders);
    }

    public Shop UpsertShopPolicy(ShopPolicyDto shop)
    {
        Shop _item = _collectionShop.Find(x => x.ShopId == shop.ShopId).FirstOrDefault();

        if (_item == null) return null;

        var update = Builders<Shop>.Update
            .Set("ShopPolicy", shop.ShopPolicy)
            .Set("Updated", DateTimes.Now());
        var filter = Builders<Shop>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<Shop> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        return _collectionShop.FindOneAndUpdate(filter, update, options);
    }

    public PagingResult<ShopPolicyDto> ListShopPolicy(Paging paging, string shopId, string? partnerId = null, TypeActive? active = null)
    {
        PagingResult<ShopPolicyDto> result = new PagingResult<ShopPolicyDto>();
        FilterDefinition<Shop> filterBuilders = Builders<Shop>.Filter.And(
            Builders<Shop>.Filter.Where(p => partnerId == null || p.PartnerId == partnerId),
            Builders<Shop>.Filter.Where(p => active == null || p.Active == active),
            Builders<Shop>.Filter.Where(p => p.ShopId == shopId),
            Builders<Shop>.Filter.Or(
                Builders<Shop>.Filter.Where(x => string.IsNullOrEmpty(paging.Search)),
                Builders<Shop>.Filter.Regex(x => x.ShopName, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i"))
            )
        );

        // Truy vấn tổng số lượng bản ghi
        result.Total = _collectionShop.CountDocuments(filterBuilders);

        // Lấy danh sách với các trường cụ thể
        result.Result = _collectionShop.Find(filterBuilders)
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .Project(x => new ShopPolicyDto
            {
                ShopId = x.ShopId,
                ShopPolicy = x.ShopPolicy,
                Updated = x.Updated,
            }) // Chỉ lấy các trường cần thiết
            .ToList(); // Chuyển thành danh sách

        return result;
    }

    public Shop GetShopPolicy(string shopId)
    {
        Shop _item = _collectionShop.Find(x => x.ShopId == shopId).FirstOrDefault();
        if (_item == null) return null;
        return _item;
    }

    public async Task<List<Shop>> GetActivedShops()
    {
        // Lọc các shop có Status = Active
        var filter = Builders<Shop>.Filter.Eq(s => s.Status, TypeStatus.Actived);
        return await _collectionShop.Find(filter).ToListAsync();
    }

    public async Task<bool> UpdateTaxRate(string shopId, decimal taxRate)
    {
        var shop = await _collectionShop.Find(x => x.ShopId == shopId).FirstOrDefaultAsync();
        if (shop == null) return false;

        var update = Builders<Shop>.Update
            .Set("DefaultTaxRate", taxRate)
            .Set("Updated", DateTimes.Now());

        var filter = Builders<Shop>.Filter.Eq("Id", shop.Id);
        var result = await _collectionShop.UpdateOneAsync(filter, update);

        return result.IsAcknowledged && result.ModifiedCount > 0;
    }
}