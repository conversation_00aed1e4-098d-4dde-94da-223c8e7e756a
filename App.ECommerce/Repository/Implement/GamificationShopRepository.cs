using App.Base.Repository;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;

using MongoDB.Driver;

namespace App.ECommerce.Repository.Implement;

public class GamificationShopRepository : BaseRepository, IGamificationShopRepository
{
    private readonly IMongoCollection<GamificationShop> _collectionGamificationShop;

    public GamificationShopRepository() : base()
    {
        _collectionGamificationShop = _database.GetCollection<GamificationShop>("GamificationShop");
    }
    public async Task<GamificationShop> GetGameBrandIdByShopId(string shopId)
    {
        var filter = Builders<GamificationShop>.Filter.Eq(x => x.ShopId, shopId);
        return await _collectionGamificationShop.Find(filter).FirstOrDefaultAsync();
    }

    public async Task Insert(GamificationShop gamificationShop)
    {
        await _collectionGamificationShop.InsertOneAsync(gamificationShop);
    }

    public async Task UpdateCampaignIdAsync(string shopId, string campaignId)
    {
        var filter = Builders<GamificationShop>.Filter.Eq(x => x.ShopId, shopId);
        var update = Builders<GamificationShop>.Update.Set(x => x.CampaignId, campaignId);
        await _collectionGamificationShop.UpdateOneAsync(filter, update);
    }

    public async Task UpdateCampaignStatusAsync(string shopId, bool isActived)
    {
        var filter = Builders<GamificationShop>.Filter.Eq(x => x.ShopId, shopId);
        var update = Builders<GamificationShop>.Update
            .Set(x => x.IsActived, isActived)
            .Set(x => x.CampaignId, null);
        await _collectionGamificationShop.UpdateOneAsync(filter, update);
    }
}
