using App.Base.Repository;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Model;
using MongoDB.Driver;

namespace App.ECommerce.Repository.Implement
{
    public class PartnerFunctionRepository : BaseRepository, IPartnerFunctionRepository
    {
        private readonly IMongoCollection<PartnerPackageHistory> _packageHistoryCollection;
        private readonly IMongoCollection<FunctionPackage> _functionPackageCollection;
        private readonly IMongoCollection<Function> _functionCollection;
        private readonly IMongoCollection<PartnerRole> _partnerRoleCollection;
        private readonly IMongoCollection<PartnerRoleFunction> _roleFunctionCollection;

        public PartnerFunctionRepository() : base()
        {
            _packageHistoryCollection = _database.GetCollection<PartnerPackageHistory>("PartnerPackageHistory");
            _functionPackageCollection = _database.GetCollection<FunctionPackage>("PartnerFunctionPackage");
            _functionCollection = _database.GetCollection<Function>("PartnerFunction");
            _partnerRoleCollection = _database.GetCollection<PartnerRole>("PartnerRole");
            _roleFunctionCollection = _database.GetCollection<PartnerRoleFunction>("PartnerRoleFunction");
        }
        public async Task<FunctionPackage> GetPackageByIdAsync(string packageId)
        {

            return await _functionPackageCollection
                .Find(p => p.PackageId.Equals(packageId) && p.IsActive == true)
                .FirstOrDefaultAsync();
        }

        public async Task<PartnerPackageHistory> InsertPartnerPackageHistoryAsync(PartnerPackageHistory packageHistory)
        {
            await _packageHistoryCollection.InsertOneAsync(packageHistory);
            return packageHistory;
        }

        public async Task<PartnerPackageHistory> CheckExistingPackageAsync(string partnerId, string packageId)
        {
            return await _packageHistoryCollection
                .Find(p => p.PartnerId == partnerId
                           && p.PackageId == packageId
                           && p.Status == TypeStatus.Actived
                           && p.EndDate >= DateTime.UtcNow)
                .FirstOrDefaultAsync();
        }

        public async Task<PartnerPackageHistory> GetPackageHistoryAsync(string partnerId)
        {
            var packageHistory = await _packageHistoryCollection
                .Find(pph => pph.PartnerId == partnerId && pph.Status == TypeStatus.Actived && pph.EndDate >= DateTime.UtcNow)
                .FirstOrDefaultAsync();

            return packageHistory;
        }

        public async Task<List<Function>> GetFunctionsByPackageIdAsync(string packageId)
        {
            var functionPackage = await _functionPackageCollection
                .Find(fp => fp.PackageId.Equals(packageId))
                .FirstOrDefaultAsync();

            if (functionPackage == null || functionPackage.FunctionPackageDetails == null || !functionPackage.FunctionPackageDetails.Any())
            {
                return new List<Function>();
            }

            var functionIds = functionPackage.FunctionPackageDetails.Select(fpd => fpd.FunctionId).ToList();
            return await _functionCollection
                .Find(f => functionIds.Contains(f.FunctionId))
                .ToListAsync();
        }

        public async Task<List<FunctionPackage>> GetAvailableFunctionPackagesAsync()
        {
            return await _functionPackageCollection
                .Find(p => p.IsActive == true && p.IsShow == true)
                .ToListAsync();
        }

        public async Task<List<PartnerRoleFunction>> GetFunctionsByEmployeeRolesAsync(string employeePartnerId)
        {
            var roleIds = (await _partnerRoleCollection
                .Find(pr => pr.PartnerId == employeePartnerId)
                .ToListAsync())
                .Select(pr => pr.RoleId);

            var roleFunctions = await _roleFunctionCollection
                .Find(rf => roleIds.Contains(rf.RoleId))
                .ToListAsync();

            return roleFunctions;
        }
        public async Task UpdatePartnerPackageHistoryAsync(PartnerPackageHistory packageHistory)
        {
            var filter = Builders<PartnerPackageHistory>.Filter.Eq(p => p.Id, packageHistory.Id);
            await _packageHistoryCollection.ReplaceOneAsync(filter, packageHistory);
        }

        public async Task<List<PartnerPackageHistory>> GetPackageHistoriesAsync(string partnerId)
        {
            return await _packageHistoryCollection
                .Find(pph => pph.PartnerId == partnerId)
                .SortByDescending(pph => pph.CreatedDate)
                .ToListAsync();
        }

        public async Task<PagingResult<PartnerPackageHistory>> GetPackageHistoriesAsync(string partnerId, Paging paging)
        {
            var filter = Builders<PartnerPackageHistory>.Filter.Eq(pph => pph.PartnerId, partnerId);
            var totalCount = await _packageHistoryCollection.CountDocumentsAsync(filter);
            
            var options = new FindOptions<PartnerPackageHistory>
            {
                Skip = (paging.PageIndex * paging.PageSize),
                Limit = paging.PageSize,
                Sort = Builders<PartnerPackageHistory>.Sort.Descending(pph => pph.CreatedDate)
            };
            
            var cursor = await _packageHistoryCollection.FindAsync(filter, options);
            var histories = await cursor.ToListAsync();
            
            return new PagingResult<PartnerPackageHistory>
            {
                Total = (int)totalCount,
                Result = histories
            };
        }

        public async Task<long> UpdateExpiredPackageHistoriesAsync()
        {
            var filter = Builders<PartnerPackageHistory>.Filter
                .Where(pph => pph.Status == TypeStatus.Actived && pph.EndDate <= DateTime.UtcNow);

            var update = Builders<PartnerPackageHistory>.Update
                .Set(pph => pph.Status, TypeStatus.InActived);

            var result = await _packageHistoryCollection.UpdateManyAsync(filter, update);
            return result.ModifiedCount;
        }

        public async Task<List<Function>> GetAllFunctionsAsync()
        {
            return await _functionCollection
                .Find(_ => true)
                .SortBy(pph => pph.OrderNumber)
                .ToListAsync();
        }

        public async Task<PartnerPackageHistory> GetLastPackageHistoryAsync(string partnerId)
        {
            var packageHistory = await _packageHistoryCollection
                .Find(pph => pph.PartnerId == partnerId)
                .SortByDescending(pph => pph.CreatedDate)
                .FirstOrDefaultAsync();

            return packageHistory;
        }
    }

}