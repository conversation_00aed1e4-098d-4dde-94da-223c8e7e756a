﻿using App.Base.Repository;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;

using MongoDB.Bson;
using MongoDB.Driver;

using static App.ECommerce.Resource.Enums.AffiliationEnum;

namespace App.ECommerce.Repository.Implement;

public class CommissionsReportRepository : BaseRepository, ICommissionsReportRepository
{
    private readonly IMongoCollection<CommissionsReport> _collectionCommissionReport;

    public CommissionsReportRepository() : base()
    {
        _collectionCommissionReport = _database.GetCollection<CommissionsReport>($"CommissionReport");
    }

    public async Task<bool> InsertCommissionReports(List<CommissionsReport> commissionReports)
    {
        if (commissionReports == null || !commissionReports.Any()) return false;
        await _collectionCommissionReport.InsertManyAsync(commissionReports);
        return true;
    }

    public async Task<PagingResult<CommissionsReport>> GetCommissionsReport(GetPartnerCommissionsByMonthYearInputDto inputDto)
    {
        // Thêm filter
        var filter = Builders<CommissionsReport>.Filter.Eq(cr => cr.ShopId, inputDto.ShopId) &
            Builders<CommissionsReport>.Filter.Eq(cr => cr.Month, inputDto.Month) &
            Builders<CommissionsReport>.Filter.Eq(cr => cr.Year, inputDto.Year);
        if (!string.IsNullOrWhiteSpace(inputDto.Paging.Search))
        {
            // Tìm kiếm không phân biệt hoa thường
            var searchFilter = Builders<CommissionsReport>.Filter.Regex(cr => cr.FullName, new BsonRegularExpression(inputDto.Paging.Search, "i"))
                | Builders<CommissionsReport>.Filter.Regex(cr => cr.BankAccountNumber, new BsonRegularExpression(inputDto.Paging.Search, "i"));
            filter &= searchFilter;
        }

        // Tìm kiếm theo filter
        var commissionsReport = _collectionCommissionReport.Find(filter);
        PagingResult<CommissionsReport> commissionsReportPaging = new PagingResult<CommissionsReport>();

        // Paging bất đồng bộ
        commissionsReportPaging.Total = await commissionsReport.CountDocumentsAsync();
        commissionsReportPaging.Result = await commissionsReport
            .Sort($"{{{inputDto.Paging.NameType}: {(inputDto.Paging.SortType == TypeSort.asc ? 1 : -1)}}}")
            .Skip(inputDto.Paging.PageIndex * inputDto.Paging.PageSize)
            .Limit(inputDto.Paging.PageSize)
            .ToListAsync();

        return commissionsReportPaging;
    }
    public async Task<List<CommissionsReport>> GetCommissionsReportByFilter(FilterDefinition<CommissionsReport> filter)
    {
        return await _collectionCommissionReport.Find(filter).ToListAsync();
    }

    public PagingResult<CommissionsReport> ListUserCommissionReport(Paging paging, string? userId = null)
    {
        string searchQuery = ConvertPhoneNumber.NormalizePhoneNumber(paging.Search);
        PagingResult<CommissionsReport> result = new PagingResult<CommissionsReport>();
        FilterDefinition<CommissionsReport> filterBuilders = Builders<CommissionsReport>.Filter.And(
            Builders<CommissionsReport>.Filter.Where(x => x.UserId == userId),
            Builders<CommissionsReport>.Filter.Eq(x => x.PaymentStatus, PaymentStatus.Paid) // Chỉ lấy những report đã paid
        );
        var query = _collectionCommissionReport.Find(filterBuilders);
        result.Total = query.CountDocuments();
        result.Result = query.Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}").Skip(paging.PageIndex * paging.PageSize).Limit(paging.PageSize).ToList();
        return result;

    }

    public async Task<long> TotalCommissionValue(string userId)
    {
        var filter = Builders<CommissionsReport>.Filter.And(
            Builders<CommissionsReport>.Filter.Eq(cr => cr.UserId, userId),
            Builders<CommissionsReport>.Filter.Eq(cr => cr.PaymentStatus, PaymentStatus.Paid) // Chỉ tính những commission đã paid
        );
        var commissions = await _collectionCommissionReport.Find(filter).ToListAsync();
        var total = commissions.Sum(cr => cr.CommissionValue); // Tính tổng giá trị hoa hồng đã paid
        return (long)total;
    }

    public async Task<bool> DeleteCommissionReportsByShopMonthYear(string shopId, int month, int year)
    {
        try
        {
            // Tạo filter để xóa tất cả bản ghi theo shopId, month, year
            var filter = Builders<CommissionsReport>.Filter.And(
                Builders<CommissionsReport>.Filter.Eq(cr => cr.ShopId, shopId),
                Builders<CommissionsReport>.Filter.Eq(cr => cr.Month, month),
                Builders<CommissionsReport>.Filter.Eq(cr => cr.Year, year)
            );

            // Thực hiện xóa và lấy kết quả
            var result = await _collectionCommissionReport.DeleteManyAsync(filter);

            // Trả về true nếu có ít nhất một bản ghi bị xóa
            return result.DeletedCount > 0;
        }
        catch (Exception)
        {
            // Trả về false nếu có lỗi xảy ra
            return false;
        }
    }
}