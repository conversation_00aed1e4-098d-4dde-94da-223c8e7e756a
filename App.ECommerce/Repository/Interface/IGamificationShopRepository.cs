using App.ECommerce.Repository.Entities;

namespace App.ECommerce.Repository.Interface
{
    public interface IGamificationShopRepository
    {
        Task<GamificationShop> GetGameBrandIdByShopId(string shopId);
        Task Insert(GamificationShop gamificationShop);
        Task UpdateCampaignIdAsync(string shopId, string campaignId);
        Task UpdateCampaignStatusAsync(string shopId, bool isActived);
    }
}