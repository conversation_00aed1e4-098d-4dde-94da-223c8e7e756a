
using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Model;
using MongoDB.Driver;

namespace App.ECommerce.Repository.Interface
{
    public interface IPartnerFunctionRepository
    {
        Task<FunctionPackage> GetPackageByIdAsync(string packageId);
        Task<PartnerPackageHistory> InsertPartnerPackageHistoryAsync(PartnerPackageHistory packageHistory);
        Task<PartnerPackageHistory> CheckExistingPackageAsync(string partnerId, string packageId);
        Task<PartnerPackageHistory> GetPackageHistoryAsync(string partnerId);
        Task<List<Function>> GetFunctionsByPackageIdAsync(string packageId);
        Task<List<FunctionPackage>> GetAvailableFunctionPackagesAsync();
        Task<List<PartnerRoleFunction>> GetFunctionsByEmployeeRolesAsync(string employeePartnerId);
        Task UpdatePartnerPackageHistoryAsync(PartnerPackageHistory packageHistory);
        Task<List<PartnerPackageHistory>> GetPackageHistoriesAsync(string partnerId);
        Task<PagingResult<PartnerPackageHistory>> GetPackageHistoriesAsync(string partnerId, Paging paging);
        Task<long> UpdateExpiredPackageHistoriesAsync();
        Task<List<Function>> GetAllFunctionsAsync();
        Task<PartnerPackageHistory> GetLastPackageHistoryAsync(string partnerId);
    }
}