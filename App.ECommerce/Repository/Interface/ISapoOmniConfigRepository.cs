using App.ECommerce.Repository.Entities;
using MongoDB.Driver;

namespace App.ECommerce.Repository.Interface;

public interface ISapoOmniConfigRepository
{
    Task<SapoOmniConfig> CreateOrUpdate(SapoOmniConfig config);
    Task<SapoOmniConfig?> FindByShopId(string shopId);
    Task<SapoOmniConfig?> FindByDomainApi(string domainApi);
    Task<SapoOmniConfig?> FindByDomainApiAndShopId(string domainApi, string shopId);
    Task<SapoOmniConfig?> GetByShopId( string shopId);
    Task<SapoOmniConfig> UpdateAccessToken(string domainApi, string shopId, string accessToken);
    Task<SapoOmniConfig> UpdateStatus(string domainApi, string shopId, string status);
    Task<SapoOmniConfig> UpdateLastSyncAt(string domainApi, string shopId, DateTime lastSyncAt);
    Task<bool> DeleteByDomainApiAndShopId(string domainApi, string shopId);
    Task<bool> DeleteByShopId(string shopId);
    Task<bool> ExistsByDomainApiAndShopId(string domainApi, string shopId, Guid? excludeId = null);
    IMongoCollection<SapoOmniConfig> GetCollection();
}