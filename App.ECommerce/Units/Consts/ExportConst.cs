using System;

namespace App.ECommerce.Units.Consts;

public static class ExportConst
{
    public const string PATH = "exports";
    public const string PATH_TEMPLATE = "exports/template";
    public const string PATH_INVOICE = "exports/invoice";
    public const string PATH_IMPORT = "import";
    public const string EXCEL_CONTENT_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    public const string PDF_CONTENT_TYPE = "application/pdf";

    public static readonly string[] HEADER_TEMPLATE_PRODUCT = new[]
    {
        "Tên sản phẩm (Bắt buộc)",
        "<PERSON><PERSON> mục (Bắt buộc)",
        "Danh mục 2",
        "<PERSON><PERSON> mục 3",
        "<PERSON><PERSON><PERSON> bán (Bắt buộc)",
        "<PERSON><PERSON><PERSON> niê<PERSON> yết (Bắt buộc)",
        "<PERSON>i<PERSON> vốn",
        "<PERSON><PERSON> lượ<PERSON> (Bắt buộc)",
        "Trọng lượng (kg)",
        "<PERSON>ề<PERSON> dài (cm)",
        "Chiều rộng (cm)",
        "<PERSON><PERSON><PERSON> cao (cm)",
        "Tên thuộc tính 1",
        "<PERSON><PERSON><PERSON> trị thuộc tính 1",
        "Tên thuộc tính 2",
        "Giá trị thuộc tính 2",
        "Tên thuộc tính 3",
        "Giá trị thuộc tính 3",
        "Hình ảnh (Bắt buộc và mỗi ảnh cách nhau bằng dấu chấm phẩy)",
        "Mô tả sản phẩm (Bắt buộc)",
        "Số lượng đã bán",
        "Thứ tự hiển thị",
        "Nổi bật (TRUE/FALSE)",
        "Hiển thị (TRUE/FALSE)"
    };

    public static readonly string[] HEADER_TEMPLATE_SERVICE = new[]
    {
        "Tên dịch vụ (Bắt buộc)",
        "Danh mục (Bắt buộc)",
        "Danh mục 2",
        "Danh mục 3",
        "Giá bán (Bắt buộc)",
        "Giá niêm yết (Bắt buộc)",
        "Giá vốn",
        "Số lượng (Bắt buộc)",
        "Hình ảnh (Bắt buộc và mỗi ảnh cách nhau bằng dấu chấm phẩy)",
        "Mô tả dịch vụ (Bắt buộc)",
        "Số lượng đã bán",
        "Thứ tự hiển thị",
        "Nổi bật (TRUE/FALSE)",
        "Hiển thị (TRUE/FALSE)"
    };

    public static readonly string[] HEADER_EXPPORT_PRODUCT = new[]
    {
        "STT",
        "Tên sản phẩm",
        "Danh mục",
        "Giá bán",
        "Giá niêm yết",
        "Giá vốn",
        "Số lượng",
        "Mã kho",
        "Cân nặng",
        "Chiều dài",
        "Chiều rộng",
        "Chiều cao",
        "Tên biến thể 1",
        "Giá trị biến thể 1",
        "Tên biến thể 2",
        "Giá trị biến thể 2",
        "Tên biến thể 3",
        "Giá trị biến thể 3",
        "Số lượng đã bán",
        "Thứ tự hiển thị",
        "Trạng thái",
        "Sản phẩm nổi bật",
        "Hình ảnh",
    };

    public static readonly string[] HEADER_EXPPORT_SERVICE = new[]
    {
        "STT",
        "Tên dịch vụ",
        "Danh mục",
        "Giá bán",
        "Giá niêm yết",
        "Giá vốn",
        "Số lượng",
        "Số lượng đã bán",
        "Thứ tự hiển thị",
        "Trạng thái",
        "Sản phẩm nổi bật",
        "Hình ảnh"
    };

    public static readonly string[] HEADER_TEMPLATE_USER = new[]
    {
        "Họ và tên",
        "Email",
        "Số Điện Thoại",
        "Giới tính",
        "Ngày sinh"
    };

    public static readonly string[] HEADER_EXPPORT_USER = new[]
    {
        "STT",
        "Mã khách hàng",
        "Tên khách hàng",
        "Số điện thoại",
        "Email",
        "Điểm tích lũy",
        "Địa chỉ",
        "Trạng thái",
        "Phân loại",
        "Hạng thành viên",
        "Đã chi tiêu(đ)",
        "Thời gian kích hoạt",
    };

    public static readonly string[] HEADER_TEMPLATE_CATEGORY = new[]
    {
        "Tên danh mục",
        "Danh mục cha",
        "Vị trí hiển thị",
        "Hình ảnh"
    };

    public static readonly string[] HEADER_EXPPORT_CATEGORY = new[]
    {
        "STT",
        "Tên danh mục",
        "Danh mục cha",
        "Số lượng",
        "Vị trí hiển thị",
        "Trạng thái",
        "Hình ảnh"
    };

    public static readonly string[] HEADER_TEMPLATE_USER_GROUP = new[]
    {
        "Họ và tên",
        "Email",
        "Số Điện Thoại"
    };


    public static readonly string[] HEADER_EXPORT_USER_GROUP = new[]
    {
        "STT",
        "Mã khách hàng",
        "Tên khách hàng",
        "Số điện thoại",
        "Email"
    };
    public static readonly string[] HEADER_EXPORT_ORDER = new[]
    {
        "STT",
        "Mã đơn hàng",
        "Tên sản phẩm",
        "Số lượng",
        "Đơn giá",
        "Ngày tạo đơn",
        "Tên khách hàng",
        "SĐT khách hàng",
        "Tổng tiền đơn hàng",
        "Trạng thái vận chuyển",
        "Trạng thái thanh toán",
        "Phương thức giao hàng",
        "Mã vận chuyển",
        "Label vận chuyển"
    };
}
