﻿@page
@using App.ECommerce.Controllers
@using App.ECommerce.Repository.Entities
@using App.ECommerce.Services.UploadStore
@using App.ECommerce.Units
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.AspNetCore.Mvc.TagHelpers
@inject IHtmlLocalizer<LangController> HtmlLocalizer
@model App.ECommerce.Areas.Admin.Shops.ListShopModel
@{
    ViewData["Title"] = "List Shop";
    ViewData["NameActivePage"] = NavigationPages.List_Shop;
}

@section Styles {
    <!-- DataTables -->
    <link rel="stylesheet" href="~/lib/datatables-bs4/css/dataTables.bootstrap4.css">
    <style>
        .selected {
            background-color: #cff4d6 !important;
        }

        table {
            width: 100% !important;
            overflow: scroll !important;
        }
    </style>
}
@await Html.PartialAsync("~/Areas/Admin/Shared/_StatusMessage.cshtml", Model.StatusMessage)

<!-- CONTAINER -->
<div class="main-container container-fluid">
    <!-- PAGE-HEADER -->
    <div class="page-header">
        <h1 class="page-title">@HtmlLocalizer["Shop.list.title"]</h1>
        <div>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">@HtmlLocalizer["Dashboard.pages"]</a></li>
                <li class="breadcrumb-item active" aria-current="page">@HtmlLocalizer["Menu.Shops"]</li>
            </ol>
        </div>
    </div>
    <!-- PAGE-HEADER END -->

    <!-- ROW-1 OPEN -->
    <!-- Row -->
    <div class="row ">
        <div class="col-md-12">
            <!-- CARD TABLE-->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title" style="width:100%;">@HtmlLocalizer["Shop.list.title"]</h3>
                    <div class="card-options" style="width:100%;">
                        <div class="row" style="width:100%;display:flex;justify-content:flex-end;">
                            @if (User.IsInRole(RolePrefixEx.Developer))
                            {
                                <button id="btn_delete_fake_data" class="btn btn-danger"
                                    style="width:fit-content;margin-bottom:0.5em;margin-right:0.5em;">Delete Fake Data <i
                                        class="ion ion-trash-a"></i></button>
                                <button id="btn_fake_data" class="btn btn-primary"
                                    style="width:fit-content;margin-bottom:0.5em;margin-right:0.5em;">Fake Data <i
                                        class="ion ion-loop"></i></button>
                            }
                            <a id="addRow" asp-area="Admin" asp-page="/shops/createshop"
                                class="btn btn-success float-right"
                                style="width:fit-content;margin-bottom:0.5em;margin-right:0.5em;">@HtmlLocalizer["Paging.Create.New"]
                                &nbsp;<i class="ion ion-plus"></i></a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="id_table_shops"
                            class="table-responsive table table-bordered text-nowrap border-bottom">
                            <thead>
                                <tr>
                                    <th class="wd-15p border-bottom-0">ShopId</th>
                                    <th class="wd-15p border-bottom-0">PartnerId</th>
                                    <th class="wd-15p border-bottom-0">Name</th>
                                    <th class="wd-15p border-bottom-0">Phone</th>
                                    <th class="wd-10p border-bottom-0">Logo</th>
                                    <th class="wd-15p border-bottom-0">Name</th>
                                    <th class="wd-10p border-bottom-0">Active</th>
                                    <th class="wd-25p border-bottom-0">Updated</th>
                                    <th class="wd-25p border-bottom-0">Controls</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- /.CARD TABLE-->
        </div>
    </div>
    <!-- /Row -->
</div>
<!-- CONTAINER CLOSED -->

<!-- modal DELETE -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger mb-20">@HtmlLocalizer["Paging.Delete"]</h5>
                <button class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="deleteDescription">@HtmlLocalizer["Paging.Delete.Description"] ?&hellip;</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-light" data-bs-dismiss="modal">@HtmlLocalizer["Paging.Close"]</button>
                <button id="deleteConfirm" class="btn btn-primary">@HtmlLocalizer["Paging.Save"]</button>
            </div>
        </div>
    </div>
</div>
<!-- /.modal DELETE -->

@section Scripts {
    <!-- DataTables -->
    <script src="~/lib/datatables/jquery.dataTables.js"></script>
    <script src="~/lib/datatables-bs4/js/dataTables.bootstrap4.js"></script>
    <script>
        $(function () {
            config_datatables('#id_table_package'); // Assuming you have a global config_datatables function
            $(document).ready(function () {
                var table = $('#id_table_shops').DataTable({
                    "dom": '<"top thiago-top-datatables"lf>rt<"bottom thiago-bottom-datatables"ip><"clear">',
                    "language": {
                        "lengthMenu": "@HtmlLocalizer["Paging.Lengt.Menu"]",
                        "zeroRecords": "@HtmlLocalizer["Paging.Zero.Records"]",
                        "info": "@HtmlLocalizer["Paging.Info"]",
                        "infoEmpty": "@HtmlLocalizer["Paging.Info.Empty"]",
                        "infoFiltered": "@HtmlLocalizer["Paging.Info.Filtered"]",
                        "sSearch": "@HtmlLocalizer["Paging.sSearch"]",
                        "paginate": {
                            "next": "@HtmlLocalizer["Paging.Next"]",
                            "previous": "@HtmlLocalizer["Paging.Previous"]",
                        },
                    },
                    "paging": true,
                    "lengthChange": true,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "processing": true,
                    "serverSide": true,
                    "order": [[3, 'asc']], // Default sort by Name (index adjusted for new Code column)
                    "columns": [
                        {
                            "data": "shopId", "orderable": false, "width": "15%",
                            "render": function (data, type, row, meta) {
                                return `<div class="auto-break-line-150">${row.shopId}</div>`;
                            }
                        },
                        {
                            "data": "partnerId", "orderable": false, "width": "15%",
                            "render": function (data, type, row, meta) {
                                return `<div class="auto-break-line-150">${row.partnerId}</div>`;
                            }
                        },
                        { "data": "partnerName", "orderable": true, "width": "30%", },
                        { "data": "partnerPhone", "orderable": true, "width": "30%", },
                        {
                            "data": "shopLogo", "orderable": false,
                            "render": function (data, type, row, meta) {
                                if (typeof row.shopLogo === "string" && row.shopLogo.indexOf("https://") !== -1) {
                                    return '<img src="' + row.shopLogo + '" width="100px" height="auto">';
                                } else if (typeof row.shopLogo === "string" && row.shopLogo.length > 0) {
                                    return '<img src="' + '@(Constants.IsAlwaysUseS3? @S3Upload.GetDomainS3() : "")' + row.shopLogo + '" onerror="this.src=\'/assets/sample/not_available.png\';" width="100px" height="auto">';
                                } else {
                                    return '<img src="/assets/sample/not_available.png" width="100px" height="auto">';
                                }
                            },
                        },
                        {
                            "data": "shopName", "orderable": true,
                            "render": function (data, type, row, meta) {
                                return `<div>${row.shopName}</div>`;
                            }
                        },
                        {
                            "data": "active", "orderable": false, "width": "10%",
                            "render": function (data, type, row, meta) {
                                if (row.active === '@TypeActive.Actived') {
                                    return "<div class='mt-sm-1 d-block'>\
                                                <span class='badge bg-success-transparent rounded-pill text-success p-2 px-3'>"+ row.active + "</span>\
                                            </div>"
                                } else if (row.active === '@TypeActive.InActived') {
                                    return "<div class='mt-sm-1 d-block'>\
                                                <span class='badge bg-danger-transparent rounded-pill text-danger p-2 px-3'>"+ row.active + "</span>\
                                            </div>"
                                } else {
                                    return "<div class='mt-sm-1 d-block'>\
                                                <span class='badge bg-warning-transparent rounded-pill text-warning p-2 px-3'>"+ row.active + "</span>\
                                            </div>"
                                }
                            }
                        },
                        { "orderable": true, "data": "updated" },
                       {
                            "data": "button", "orderable": false, "width": "15%",
                            "render": function (data, type, row, meta) {
                                return `<div class='g-2'>
                                        <a onclick="EditData(this,${meta.row},${meta.col});" class='btn text-primary btn-sm' data-bs-toggle='tooltip' data-bs-original-title='Edit'>Edit <span class='fe fe-edit fs-14'></span></a>
                                        <a onclick="DeleteData(this,${meta.row},${meta.col});" class='btn text-danger btn-sm' data-bs-toggle='tooltip' data-bs-original-title='Delete'>Delete <span class='fe fe-trash-2 fs-14'></span></a>
                                    </div>`;
                            }
                        }
                    ],
                    "ajax": $.fn.dataTable.pipeline({
                        url: '?handler=ListShop',
                        data: function (d) {
                            delete d.columns;
                        },
                        pages: 1
                    })
                });
            });
        });

        function EditData(btn, row, col) {
            var rowData = $('#id_table_shops').DataTable().row(row).data();
            var shopId = rowData.shopId;
            window.location.href = '/admin/shops/editshop?shopId=' + shopId;
        }

        function DeleteData(btn, row, col) {
            var rowData = $('#id_table_shops').DataTable().row(row).data();
            var shopId = rowData.shopId;
            $('#deleteModal').modal('show');
            $('#deleteConfirm').off('click').on('click', function (e) {
                $('#deleteModal').modal('hide');
                window.location.href = '/admin/shops/listshop?handler=Delete&shopId=' + shopId;
            });
        }
    </script>
}
