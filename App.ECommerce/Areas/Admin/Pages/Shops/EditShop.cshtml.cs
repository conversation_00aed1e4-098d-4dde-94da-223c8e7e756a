﻿using App.Base.Middleware;
using App.Base.SysSetting;
using App.Base.Utilities;
using App.ECommerce.Areas.Admin.Pages;
using App.ECommerce.Repository.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Services.ViettelPost;
using App.ECommerce.Units;
using Microsoft.AspNetCore.Authentication.Cookies;
using App.ECommerce.Repository.Interface;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Linq;
using System.Threading.Tasks;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;

namespace App.ECommerce.Areas.Admin.Shops;

[Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefixEx.AdminSys + "," + RolePrefixEx.Manager)]
public class EditShopModel : BasePageModel
{
    private readonly IConfiguration _config;
    private readonly IBusinessTypeRepository _businessTypeRepository;
    private readonly IGroupFileRepository _groupFileRepository;

    [TempData] public string StatusMessage { get; set; }

    [BindProperty] public ShopDto? ShopDto { get; set; }

    public List<SelectListItem> BusinessTypeList { get; set; }

    public string GOOGLE_MAP_API = "AIzaSyBN1AU7MfvhknKn5wLdcfw1D5S-6gekWoc";

    public EditShopModel(
        IConfiguration config,
        IBusinessTypeRepository businessTypeRepository,
        IGroupFileRepository groupFileRepository
    ) : base()
    {
        _config = config;
        _businessTypeRepository = businessTypeRepository;
        _groupFileRepository = groupFileRepository;
    }

    private async Task<List<SelectListItem>> GetBusinessTypeSelectListAsync()
    {
        List<BusinessType> businessTypes = await _businessTypeRepository.GetAllAsync();
        return businessTypes.Select(x => new SelectListItem()
        {
            Value = x.Name,
            Text = x.Label
        }).ToList();
    }

    public async Task OnGetAsync(string shopId)
    {
        BusinessTypeList = await GetBusinessTypeSelectListAsync();

        SettingSys configurations = _configurationsRepository.FindConfigDefault();
        GOOGLE_MAP_API = configurations?.SystemSetting?.GoogleMapAPIKey ?? "";

        Shop shop = _shopRepository.FindByShopId(shopId);
        ShopDto = _mapper.Map<ShopDto>(shop);
    }

    public async Task<IActionResult> OnPostCreateOrUpdate(ShopDto model, IFormFile[] attachment)
    {
        if (string.IsNullOrEmpty(model.ShopName))
        {
            StatusMessage = "Error: invalid shop name";
            return LocalRedirect($"~/admin/shops/editshop?shopId={model.ShopId}");
        }

        if (string.IsNullOrEmpty(model.PartnerId))
        {
            StatusMessage = "Error: invalid partner id";
            return LocalRedirect($"~/admin/shops/editshop?shopId={model.ShopId}");
        }

        model.ProvinceName = ViettelPostData.FindProvince(model.ProvinceId)?.ProvinceName;
        model.DistrictName = ViettelPostData.FindDistrict(model.DistrictId)?.DistrictName;
        model.WardsName = ViettelPostData.FindWards(model.WardsId)?.WardName;

        Shop? item = _shopRepository.FindByShopId(model.ShopId);

        if (item == null)
        {
            StatusMessage = "Error: shop not found";
            return LocalRedirect($"~/admin/shops/editshop?shopId={model.ShopId}");
        }

        Logs.debug($"Update shop...\n{Newtonsoft.Json.JsonConvert.SerializeObject(model, JsonSettings.SettingForNewtonsoftPretty)}");
        //item.ShopId = model.ShopId;
        //item.PartnerId = model.PartnerId;
        item.OaId = model.OaId;
        item.BusinessType = ShopDto?.BusinessType ?? model.BusinessType;
        item.ShopName = model.ShopName;
        item.ShopSlogan = model.ShopSlogan;
        item.ShopDesc = model.ShopDesc;
        item.ShopInfo = model.ShopInfo;
        //shop.ShopLogo = model.ShopLogo;
        item.ShopDeeplink = model.ShopDeeplink;
        // item.StartDate = model.StartDate ?? DateTimes.Now();
        // item.EndDate = model.EndDate;
        item.OpenTime = model.OpenTime;
        item.CloseTime = model.CloseTime;
        item.PrefixCode = model.PrefixCode;
        item.ShopTheme = new ShopTheme();
        item.ProvinceId = model.ProvinceId;
        item.ProvinceName = model.ProvinceName;
        item.DistrictId = model.DistrictId;
        item.DistrictName = model.DistrictName;
        item.WardsId = model.WardsId;
        item.WardsName = model.WardsName;
        item.Address = model.Address;
        item.TransportPrice = model.TransportPrice ?? Constants.TransportPrice;
        item.Active = model.Active;
        item.Status = TypeStatus.Actived;
        //item.Created = model.Created;
        item.Updated = DateTimes.Now();

        Logs.debug($"File size: {attachment.Length}");
        if (attachment.Length > 0)
        {
            MediaFile file = new MediaFile
            {
                ShopId = model.ShopId,
                Type = TypeMedia.IMAGE,
                Source = BaseSourceEnum.Manual,
                RefType = RefTypeEnum.Shop,
                RefId = item.ShopId,
                Link = "",
            };

            await S3Upload.DeleteImageS3(new List<string>() { item.ShopLogo?.Link ?? "" });

            var keyFile = S3Upload.SendMyFileToS3(attachment[0], attachment[0].FileName.FixFileName(), "shops").Result;
            if (!string.IsNullOrEmpty(keyFile))
            {
                file.Type = TypeMedia.IMAGE;
                file.Link = $"{keyFile}";
            }

            file = _groupFileRepository.CreateMediaFile(file);
            item.ShopLogo = new MediaInfo()
            {
                MediaFileId = file.MediaFileId,
                Type = TypeMedia.IMAGE,
                Link = keyFile
            };
        }
        else
        {
            item.ShopLogo = Constants.IsAlwaysUseS3 ?
                    new MediaInfo()
                    {
                        MediaFileId = "",
                        Type = TypeMedia.IMAGE,
                        Link = CommonConst.NO_IMAGE_SHOP
                    } :
                    new MediaInfo()
                    {
                        MediaFileId = "",
                        Type = TypeMedia.IMAGE,
                        Link = CommonConst.NO_IMAGE_SHOP
                    };
        }

        _shopRepository.UpdateShop(item);
        StatusMessage = "Shop edited successfully";
        return LocalRedirect($"~/admin/shops/editshop?shopId={item.ShopId}");
    }
}