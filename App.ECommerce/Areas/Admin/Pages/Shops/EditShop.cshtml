﻿@page
@using App.ECommerce.Controllers
@using App.ECommerce.Repository.Entities
@using App.ECommerce.Services.UploadStore
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.AspNetCore.Mvc.TagHelpers
@inject IHtmlLocalizer<LangController> HtmlLocalizer
@model App.ECommerce.Areas.Admin.Shops.EditShopModel
@{
    ViewData["Title"] = "Edit Shop";
    ViewData["NameActivePage"] = NavigationPages.Edit_Shop;
}

@section Styles {
}
@await Html.PartialAsync("~/Areas/Admin/Shared/_StatusMessage.cshtml", Model.StatusMessage)

<!-- CONTAINER -->
<div class="main-container container-fluid">
    <!-- PAGE-HEADER -->
    <div class="page-header">
        <h1 class="page-title">@HtmlLocalizer["Shop.edit"]</h1>
        <div>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">@HtmlLocalizer["Dashboard.pages"]</a></li>
                <li class="breadcrumb-item active" aria-current="page">@HtmlLocalizer["Menu.Shops"]</li>
            </ol>
        </div>
    </div>
    <!-- PAGE-HEADER END -->

    <!-- ROW-1 OPEN -->
    <!-- Row -->
    <div class="row ">
        <div class="col-xl-12">
            <div class="card">
                <form asp-page-handler="CreateOrUpdate" method="post" enctype="multipart/form-data"
                    class="needs-validation" novalidate>
                    <div class="card-header">
                        <h3 class="card-title">@HtmlLocalizer["Shop.edit"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <input type="text" class="form-control" name="ShopId" value="@Model.ShopDto?.ShopId" hidden>
                        </div>
                        <div class="row">
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_ShopName">Shop name <b style="color: red;">(*)</b></label>
                                    <input type="text" name="ShopName" value="@Model.ShopDto?.ShopName"
                                        id="input_ShopName" class="form-control" placeholder="Shop name" required>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_OaId">OaId <b style="color: red;">(*)</b></label>
                                    <input type="text" name="OaId" value="@Model.ShopDto?.OaId" id="input_OaId"
                                        class="form-control" placeholder="OaId">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="_partnerId">PartnerId <b style="color: red;">(*)</b></label>
                                    <input type="text" class="form-control" id="_partnerId" name="PartnerId"
                                        value="@Model.ShopDto?.PartnerId" onfocus="onfocus_partnerId(this);"
                                        onblur="onblur_partnerId(this);">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_ShopSlogan">Shop slogan</label>
                                    <input type="text" name="ShopSlogan" value="@Model.ShopDto?.ShopSlogan"
                                        id="input_ShopSlogan" class="form-control" placeholder="Shop slogan">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_PrefixCode">Prefix code</label>
                                    <input type="text" name="PrefixCode" value="@Model.ShopDto?.PrefixCode"
                                        id="input_PrefixCode" class="form-control" placeholder="Prefix code">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_ShopDeeplink">Shop deep link</label>
                                    <input type="text" name="ShopDeeplink" value="@Model.ShopDto?.ShopDeeplink"
                                        id="input_ShopDeeplink" class="form-control" placeholder="Shop deep link">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_ShipCost">Ship sost</label>
                                    <input type="number" name="ShipCost" value="@(Model.ShopDto?.ShipCost ?? 0)"
                                        id="input_ShipCost" class="form-control" placeholder="Ship sost">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_OpenTime">OpenTime</label>
                                    <input type="text" name="OpenTime" value="@Model.ShopDto?.OpenTime"
                                        id="input_OpenTime" class="form-control" placeholder="OpenTime">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_CloseTime">CloseTime</label>
                                    <input type="text" name="CloseTime" value="@Model.ShopDto?.CloseTime"
                                        id="input_CloseTime" class="form-control" placeholder="CloseTime">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="_Province">Province</label>
                                    <select id="_Province" name="ProvinceId" value="@Model.ShopDto?.ProvinceId"
                                        class="form-control select2-show-search form-select select2-hidden-accessible"></select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="_District">District</label>
                                    <select id="_District" name="DistrictId" value="@Model.ShopDto?.DistrictId"
                                        class="form-control select2-show-search form-select select2-hidden-accessible"></select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="_Wards">Wards</label>
                                    <select id="_Wards" name="WardsId" value="@Model.ShopDto?.WardsId"
                                        class="form-control select2-show-search form-select select2-hidden-accessible"></select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="input_Address">Address</label>
                                    <input type="text" name="Address" value="@Model.ShopDto?.Address" id="input_Address"
                                        class="form-control" placeholder="Address">
                                    <input type="text" name="Longitude" value="@Model.ShopDto?.Longitude"
                                        id="input_Longitude" class="form-control" placeholder="Longitude"
                                        style="display: none;">
                                    <input type="text" name="Latitude" value="@Model.ShopDto?.Latitude"
                                        id="input_Latitude" class="form-control" placeholder="Latitude"
                                        style="display: none;">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_BusinessType">Business Type</label>
                                    <select id="input_BusinessType" asp-for="ShopDto.BusinessType"
                                        asp-items="@Model.BusinessTypeList"
                                        class="form-control select2-show-search form-select" required>
                                        <option value="">-- Chọn loại cửa hàng --</option>
                                    </select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Active">Active</label>
                                    <select id="input_Active" name="Active"
                                        value="@(Model.ShopDto?.Active ?? TypeActive.Actived)"
                                        class="form-control select2-show-search form-select select2-hidden-accessible"
                                        required>
                                        <option value="@TypeActive.Actived">Actived</option>
                                        <option value="@TypeActive.InActived">InActived</option>
                                    </select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label>Shop Logo</label>
                                    <input class="form-control" name="Attachment"
                                        accept="image/jpeg, image/jpg, image/png" type="file" id="myFileUpload">
                                </div>
                                <div class="card-group bg-white" style="margin-bottom:10px;">
                                    <ul id="show-myFileUpload"
                                        class="mailbox-attachments d-flex align-items-stretch clearfix"
                                        style="overflow: scroll; width:100%;scrollbar-width:none;">
                                        @if (Model.ShopDto?.ShopLogo != null)
                                        {
                                            <li>
                                                <span class="mailbox-attachment-icon has-img" style="overflow: hidden;">
                                                    <img src="@(S3Upload.GetUrlImage(Model.ShopDto?.ShopLogo?.Link ?? ""))"
                                                        onerror="this.src='/assets/sample/not_available.png';"
                                                        alt="Attachment" style="height:50px;">
                                                </span>
                                                <div class="mailbox-attachment-info">
                                                    <a href="#" class="mailbox-attachment-name">
                                                        <i class="fa fa-camera"
                                                            style="margin-top:5px;margin-right:2px;"></i>
                                                        <div>@Model.ShopDto?.ShopLogo</div>
                                                    </a>
                                                </div>
                                            </li>
                                        }
                                    </ul>
                                    <input hidden name="ShopLogo" value="@Model.ShopDto?.ShopLogo" />
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="input_Notes">Shop desc</label>
                                    <textarea name="ShopDesc" id="input_Notes" class="form-control"
                                        rows="6">@Model.ShopDto?.ShopDesc</textarea>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 col-md-12">
                                <div class="form-group">
                                    <label for="summernote">Shop info</label>
                                    <textarea id="summernote" name="ShopInfo">@Model.ShopDto?.ShopInfo</textarea>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-end">
                        <button class="btn btn-primary" type="submit">Update</button>
                        <a asp-area="Admin" asp-page="/shops/listshop" class="btn btn-success float-right">Back &nbsp;<i
                                class="ion-ios-rewind"></i></a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- /Row -->
</div>
<!-- CONTAINER CLOSED -->

<!-- Select2 modal -->
<div id="myModal" class="modal fade" data-bs-backdrop="static" data-bs-keyboard="true" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content modal-content-demo">
            <div class="modal-header">
                <h4 id="_titleModal" class="modal-title">List Object...</h4>
                <button class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="color: black;"><span
                        aria-hidden="true">×</span></button>
            </div>
            <div class="modal-body">
                @*<div class="form-group">*@
                <h6 id="_descriptionModal">Search with...</h6>
                <!-- Select2 -->
                <select id="select2_IdFor" class="form-control select2 select2-dropdown" multiple="multiple"
                    data-placeholder="Please select item" data-dropdown-css-class="select2-purple"
                    style="width: 100%;"></select>
                <!-- Select2 -->
                <p id="_contentModal" class="mt-3">Please select...</p>
                @*</div>*@
            </div>
            <div class="modal-footer">
                <button class="btn ripple btn-light" data-bs-dismiss="modal" type="button">Close</button>
                <button id="_btnModal" class="btn ripple btn-success" type="button">Select</button>
            </div>
        </div>
    </div>
</div>
<!-- End Select2 modal -->

@section Scripts {
    <!-- INTERNAL intlTelInput js-->
    @* <script src="~/sash/assets/plugins/intl-tel-input-master/intlTelInput.js"></script> *@
    @* <script src="~/sash/assets/plugins/intl-tel-input-master/country-select.js"></script> *@
    @* <script src="~/sash/assets/plugins/intl-tel-input-master/utils.js"></script> *@
    <!-- InputMask -->
    @* <script src="~/lib/moment/moment.min.js"></script> *@
    @* <script src="~/lib/inputmask/min/jquery.inputmask.bundle.min.js"></script> *@
    <!-- bs-custom-file-input -->
    <script src="~/lib/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <!-- INTERNAL SUMMERNOTE Editor JS -->
    <script src="~/sash/assets/plugins/summernote/summernote1.js"></script>
    <script src="~/sash/assets/js/summernote.js"></script>
    <!-- google-map -->
    <script async
        src="https://maps.googleapis.com/maps/api/js?key=@(Model.GOOGLE_MAP_API)&libraries=places&callback=getLocation"></script>
    <script>
        function getLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(showPosition);
            } else {
                console.log(`Geolocation is not supported by this browser.`);
                const autocompleteOptions = {
                    componentRestrictions: { country: ["vn"] },
                    fields: ["formatted_address", "geometry", "name"],
                    strictBounds: false,
                };
                initMap(autocompleteOptions);
            }
        };

        function showPosition(position) {
            var longitude = position.coords.longitude;
            var latitude = position.coords.latitude;
            console.log(`current gps location: [${longitude},${latitude}]`);
            const defaultBounds = new google.maps.LatLngBounds(new google.maps.LatLng(latitude, longitude));
            const autocompleteOptions = {
                bounds: defaultBounds,
                componentRestrictions: { country: ["vn"] },
                fields: ["formatted_address", "geometry", "name"],
                strictBounds: false,
            };
            initMap(autocompleteOptions);
        };

        function initMap(autocompleteOptions) {
            const input = document.getElementById("input_Address");
            var options = {
                componentRestrictions: { country: ["vn"] },
                fields: ["formatted_address", "geometry", "name"],
                strictBounds: false,
            };
            if (autocompleteOptions !== undefined) options = autocompleteOptions;
            const autocomplete = new google.maps.places.Autocomplete(input, options);
            autocomplete.addListener("place_changed", () => {
                const place = autocomplete.getPlace();
                if (!place.geometry || !place.geometry.location) {
                    // User entered the name of a Place that was not suggested and
                    // pressed the Enter key, or the Place Details request failed.
                    window.alert("No details available for input: '" + place.name + "'");
                    return;
                }
                console.log(place);
                console.log(`place.name: ${place.name}`);
                console.log(`place.formatted_address: ${place.formatted_address}`);
                const longitude = `${place.geometry.location.lng()}`;
                const latitude = `${place.geometry.location.lat()}`;
                console.log(`place.longitude: ${longitude}`);
                console.log(`place.latitude: ${latitude}`);
                $("#input_Longitude").val(longitude);
                $("#input_Latitude").val(latitude);
            });
        };
    </script>
    <script>
        function initControl() {
            /* Select Address */
            var s1data = $('#_Province').select2({
                cacheDataSource: [],
                placeholder: "Tỉnh/Thành phố",
                minimumInputLength: 0,
                tokenSeparators: [",", " "],
                templateResult: function (item) { return item.text; },
                templateSelection: function (item) { return item.text; },
                ajax: {
                    url: '?handler=ListProvince',
                    contentType: "application/json; charset=utf-8",
                    dataType: 'json',
                    type: "POST",
                    delay: 200,
                    cache: true,
                    cacheDataSource: [],
                    headers: { RequestVerificationToken: $('input:hidden[name="__RequestVerificationToken"]').val() },
                    data: function (query) {
                        return JSON.stringify({
                            search: query.term,
                            provinceId: -1,
                        });
                    },
                    processResults: function (data) {
                        var data = JSON.parse(data);
                        var items = $.map(data.data, function (item) {
                            return {
                                id: item.ProvinceID,
                                text: item.ProvinceName,
                            }
                        });
                        this.options.set('cacheDataSource', { items: items });
                        return { results: items };
                    },
                },
            }).data('select2');
            s1data.dataAdapter.query = function (params, callback) {
                var cacheDataSource = this.options.get('cacheDataSource');
                if (cacheDataSource && cacheDataSource.items) {
                    var term = params.term;
                    if (typeof term == "undefined" || term == null) {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    term = $.trim(term.toLowerCase());
                    if (term == "") {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    callback({
                        results: cacheDataSource.items.filter(function (item) {
                            return item.text.toLowerCase().includes(term);
                        })
                    });
                } else { // call the original logic
                    var ajaxAdapterFunc = jQuery.fn.select2.amd.require('select2/data/ajax');
                    var ajaxAdapter = new ajaxAdapterFunc(this.$element, this.options);
                    ajaxAdapter.query(params, callback);
                }
            };
            var provinceId = "@Html.Raw(Model.ShopDto?.ProvinceId ?? "")";
            var provinceName = "@Html.Raw(Model.ShopDto?.ProvinceName ?? "")";
            if (provinceId !== "" && provinceName !== "") {
                $('#_Province').append(new Option(provinceName, provinceId, true, true)).trigger('change');
            }
            //$('#_Province').append(new Option('Bình Ðịnh (BDH)', 40, true, true)).trigger('change');

            var s2data = $('#_District').select2({
                cacheDataSource: [],
                placeholder: "Quận/Huyện",
                minimumInputLength: 0,
                tokenSeparators: [",", " "],
                templateResult: function (item) { return item.text; },
                templateSelection: function (item) { return item.text; },
                ajax: {
                    url: '?handler=ListDistrict',
                    contentType: "application/json; charset=utf-8",
                    dataType: 'json',
                    type: "POST",
                    delay: 200,
                    cache: true,
                    cacheDataSource: [],
                    headers: { RequestVerificationToken: $('input:hidden[name="__RequestVerificationToken"]').val() },
                    data: function (query) {
                        var provinceId = $("#_Province option:selected").val();
                        return JSON.stringify({
                            search: query.term,
                            provinceId: (provinceId == undefined ? -1 : provinceId),
                        });
                    },
                    processResults: function (data) {
                        var data = JSON.parse(data);
                        var items = $.map(data.data, function (item) {
                            return {
                                id: item.DistrictID,
                                text: item.DistrictName,
                            }
                        });
                        this.options.set('cacheDataSource', { items: items });
                        return { results: items };
                    },
                },
            }).data('select2');
            s2data.dataAdapter.query = function (params, callback) {
                var cacheDataSource = this.options.get('cacheDataSource');
                if (cacheDataSource && cacheDataSource.items) {
                    var term = params.term;
                    if (typeof term == "undefined" || term == null) {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    term = $.trim(term.toLowerCase());
                    if (term === "") {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    callback({
                        results: cacheDataSource.items.filter(function (item) {
                            return item.text.toLowerCase().includes(term);
                        })
                    });
                } else { // call the original logic
                    var ajaxAdapterFunc = jQuery.fn.select2.amd.require('select2/data/ajax');
                    var ajaxAdapter = new ajaxAdapterFunc(this.$element, this.options);
                    ajaxAdapter.query(params, callback);
                }
            };
            var districtId = "@Html.Raw(Model.ShopDto?.DistrictId ?? "")";
            var districtName = "@Html.Raw(Model.ShopDto?.DistrictName ?? "")";
            if (districtId !== "" && districtName !== "") {
                $('#_District').append(new Option(districtName, districtId, true, true)).trigger('change');
            }
            //$('#_District').append(new Option('THÀNH PHỐ QUI NHƠN (5910)', 464, true, true)).trigger('change');

            var s3data = $('#_Wards').select2({
                cacheDataSource: [],
                placeholder: "Phường/Xã",
                minimumInputLength: 0,
                tokenSeparators: [",", " "],
                templateResult: function (item) { return item.text; },
                templateSelection: function (item) { return item.text; },
                ajax: {
                    url: '?handler=ListWards',
                    contentType: "application/json; charset=utf-8",
                    dataType: 'json',
                    type: "POST",
                    delay: 200,
                    cache: true,
                    cacheDataSource: [],
                    headers: { RequestVerificationToken: $('input:hidden[name="__RequestVerificationToken"]').val() },
                    data: function (query) {
                        var districtId = $("#_District option:selected").val();
                        return JSON.stringify({
                            search: query.term,
                            districtId: (districtId === undefined ? -1 : districtId),
                        });
                    },
                    processResults: function (data) {
                        var data = JSON.parse(data);
                        var items = $.map(data.data, function (item) {
                            return {
                                id: item.WardID,
                                text: item.WardName,
                            }
                        });
                        this.options.set('cacheDataSource', { items: items });
                        return { results: items };
                    },
                },
            }).data('select2');
            s3data.dataAdapter.query = function (params, callback) {
                var cacheDataSource = this.options.get('cacheDataSource');
                if (cacheDataSource && cacheDataSource.items) {
                    var term = params.term;
                    if (typeof term == "undefined" || term == null) {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    term = $.trim(term.toLowerCase());
                    if (term === "") {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    callback({
                        results: cacheDataSource.items.filter(function (item) {
                            return item.text.toLowerCase().includes(term);
                        })
                    });
                } else { // call the original logic
                    var ajaxAdapterFunc = jQuery.fn.select2.amd.require('select2/data/ajax');
                    var ajaxAdapter = new ajaxAdapterFunc(this.$element, this.options);
                    ajaxAdapter.query(params, callback);
                }
            };
            var wardsId = "@Html.Raw(Model.ShopDto?.WardsId ?? "")";
            var wardsName = "@Html.Raw(Model.ShopDto?.WardsName ?? "")";
            if (wardsId !== "" && wardsName !== "") {
                $('#_Wards').append(new Option(wardsName, wardsId, true, true)).trigger('change');
            }
            //$('#_Wards').append(new Option('PHƯỜNG TRẦN QUANG DIỆU', 8548, true, true)).trigger('change');

            $('#_Province').on('select2:selecting', function (e) {
                var data = $("#_Province option:selected").val();
                //console.log('_Province: ', e.params.args.data);
                s2data.dataAdapter.options.options.cacheDataSource = [];
                $("#_District").empty();
                s3data.dataAdapter.options.options.cacheDataSource = [];
                $("#_Wards").empty();
            });

            $('#_District').on('select2:selecting', function (e) {
                var data = $("#_District option:selected").val();
                //console.log('_District: ', e.params.args.data);
                s3data.dataAdapter.options.options.cacheDataSource = [];
                $("#_Wards").empty();
            });
        }
    </script>
    <script>
        @* ParentId *@
            function onfocus_partnerId(item) {
                console.log('onfocus ' + item.value);
                $('#select2_IdFor').val(null).trigger("change");
                $('#_typeForCache').val("Partner"); @* Save cache *@
                    $("#_titleModal").html("List Partner");
                $("#_descriptionModal").html("Search with name");
                $("#_contentModal").html("Please select a Partner for this shop.");
                $('#_btnModal').unbind('click');
                $("#_btnModal").click(save_partnerId);
                $("#myModal").modal("show");
            }

        function onblur_partnerId(item) {
            console.log('onblur ' + item.value);
        }

        function save_partnerId() {
            var select_data = $('#select2_IdFor').select2('data');
            console.log(select_data.length);
            if (select_data.length >= 2) {
                @* multiple *@
                                        var result = [];
                select_data.forEach(function (item) {
                    console.log('item.id ' + item.id);
                    result.push(item.id); @* [item.id, item.text] *@
                                        });
                console.log(result);
                $('#_partnerId').val(JSON.stringify(result));
            } else if (select_data.length == 1) {
                $('#_partnerId').val(select_data[0].id);
            } else {
                $('#_partnerId').val("");
            }
            $("#myModal").modal("hide");
        }

        $(function () {
            @* select2_IdFor *@
                $("#select2_IdFor").select2({
                    placeholder: "Please select item",
                    allowClear: true,
                    multiple: false,
                    minimumInputLength: 0,
                    dropdownParent: $('#myModal'),
                    ajax: {
                        url: "?handler=ListSearch",
                        type: "GET",
                        contentType: "application/json; charset=utf-8",
                        dataType: 'json',
                        quietMillis: 200,
                        delay: 250,
                        data: function (params) {
                            @* console.log(params);*@
                                                var queryParameters = {
                                typeFor: $("#_typeForCache").val(),
                                idFor: '',
                                search: params.term,
                                page: params.page || 1,
                                flag: 'selectprogram',
                            };
                            return queryParameters;
                        },
                        processResults: function (data, page) {
                            @* console.log('OnGetListSearch processResults: ' + JSON.stringify(data.data.listData, null, 2));*@
                                                var more = (page * 10) < data.data.total_count;
                            return {
                                results: data.data.listData,
                                pagination: { more: more }
                            };
                        },
                        cache: true,
                    },
                    dropdownCssClass: "bigdrop",
                    formatResult: function (element) {
                        return element.text + ' (' + element.id + ')';
                    },
                    formatSelection: function (element) {
                        return element.text + ' (' + element.id + ')';
                    },
                    escapeMarkup: function (m) { return m; }
                });
        });
    </script>
    <script>
        $(function () {
            @* Auto selected value *@
                @* $("select").each((index, item) => {
                    if ($(item).attr("value")) {
                        $(item).val($(item).attr("value")).change();
                    }
                }); *@

            @* Validate form submit *@
                window.addEventListener('load', function () {
                    // Fetch all the forms we want to apply custom Bootstrap validation styles to
                    var forms = document.getElementsByClassName('needs-validation');
                    // Loop over them and prevent submission
                    var validation = Array.prototype.filter.call(forms, function (form) {
                        form.addEventListener('submit', function (event) {
                            if (form.checkValidity() === false) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                }, false);

            initControl();

            //$('[data-mask]').inputmask();
        });
    </script>
    <script>
        function _onDelete(index, el) {
            var myFileUpload = document.getElementById("myFileUpload");
            var files = myFileUpload.files;
            @* remove file at index *@
                                    var fileBuffer = [];
            Array.prototype.push.apply(fileBuffer, files);
            fileBuffer.splice(index, 1);
            const dT = new ClipboardEvent('').clipboardData || new DataTransfer();
            for (let file of fileBuffer) { dT.items.add(file); }
            myFileUpload.files = dT.files;
            files = myFileUpload.files;
            console.log(files);
            $(".image_" + index).remove()
        };

        $(function () {
            bsCustomFileInput.init();
            @* myFileUpload *@
                function readURL() {
                    var $input = $(this);
                    var showMyFileUpload = document.getElementById("show-myFileUpload");
                    while (showMyFileUpload.firstChild) showMyFileUpload.removeChild(showMyFileUpload.firstChild);

                    for (let i = 0; i < this.files.length; i++) {
                        var file = this.files[i];
                        console.log(file);
                        var fileName = file.name;
                        var fileSize = (file.size / 1024).toFixed(2);
                        var fileType = file.type;
                        var reader = new FileReader();
                        reader.onload = function (e) {
                            @*console.log(e.target);*@
                            var li = document.createElement('li');
                            li.className = 'image_' + i;
                            li.innerHTML = '\
                                                <span class="mailbox-attachment-icon has-img" > \
                                                    <img src="' + e.target.result + '" alt="Attachment" style="height:50px;">\
                                                </span>\
                                                <div class="mailbox-attachment-info">\
                                                    <a href="#" class="mailbox-attachment-name">\
                                                        <i class="fa fa-camera" style="margin-top:5px;margin-right:2px;"></i>\
                                                        <div>' + fileName + '</div>\
                                                    </a>\
                                                    <span class="mailbox-attachment-size clearfix mt-1">\
                                                        <span>' + fileSize + ' KB</span>\
                                                        <a href="javascript:_onDelete('+ i + ', this)" class="btn btn-default btn-sm float-right"><i class="fa fa-trash"></i></a>\
                                                    </span>\
                                                </div>';
                            showMyFileUpload.appendChild(li);
                        }
                        reader.readAsDataURL(file);
                    }
                };
            $("#myFileUpload").change(readURL);
        })
    </script>
}
