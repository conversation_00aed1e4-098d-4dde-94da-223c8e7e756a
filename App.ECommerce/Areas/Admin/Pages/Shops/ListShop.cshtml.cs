﻿using App.Base.Middleware;
using App.Base.SysSetting;
using App.Base.Utilities;
using App.ECommerce.Areas.Admin.Pages;
using App.ECommerce.MockData;
using App.ECommerce.ProcessFlow;
using App.ECommerce.Repository.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units;
using Microsoft.AspNetCore.Authentication.Cookies;
using App.ECommerce.ProcessFlow.Interface;
using Newtonsoft.Json;
using MongoDB.Bson;

namespace App.ECommerce.Areas.Admin.Shops
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    [MultiPolicysAuthorize(Policys = RolePrefixEx.AdminSys + "," + RolePrefixEx.Manager)]
    public class ListShopModel : BasePageModel
    {
        private readonly IConfiguration _config;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IShopFlow _shopFlow;

        [TempData]
        public string StatusMessage { get; set; }

        [BindProperty]
        public List<Shop>? ListShop { get; set; }

        public ListShopModel(IConfiguration config, IServiceScopeFactory serviceScopeFactory)
        {
            _config = config;
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task<JsonResult> OnGetListShop()
        {
            int draw = int.Parse(Request.Query["draw"]);
            int start = int.Parse(Request.Query["start"]);
            int length = int.Parse(Request.Query["length"]);
            string search = Request.Query["search[value]"];
            string sortName = Request.Query["order[0][column]"];
            string sort = Request.Query["order[0][dir]"];
            long total = 0;
            int page = start / (length == 0 ? 1 : length);
            TypeSort? typeSort = sort.GetSort();
            TypeSortName? typeSortName = sortName.GetSortName();
            if (StringsEx.IsValidPhoneHome(search)) search = search?.FormatPhonePrefix84();

            PagingResult<Shop> pagingResult = _shopRepository.ListShop(new Paging() { PageIndex = page, PageSize = length, Search = search?.ToLower(), NameType = typeSortName, SortType = typeSort });
            total = pagingResult.Total;

            List<object> listData = new List<object>();
            foreach (var shop in pagingResult.Result)
            {
                Partner? partner = await _partnerRepository.FindByPartnerId(shop.PartnerId);
                listData.Add(new
                {
                    shopId = shop.ShopId,
                    partnerId = shop.PartnerId,
                    partnerName = partner?.Fullname,
                    partnerPhone = partner?.PhoneNumber,
                    shopName = shop.ShopName,
                    shopSlogan = shop.ShopSlogan,
                    shopDesc = shop.ShopDesc,
                    shopLogo = shop.ShopLogo,
                    provinceId = shop.ProvinceId,
                    provinceName = shop.ProvinceName,
                    districtId = shop.DistrictId,
                    districtName = shop.DistrictName,
                    wardsId = shop.WardsId,
                    wardsName = shop.WardsName,
                    address = shop.Address,
                    active = shop.Active,
                    created = shop.Created,
                    updated = shop.Updated
                });
            }

            return await Task.FromResult(new JsonResult(new { draw = draw, recordsTotal = total, recordsFiltered = total, data = listData }));
        }

        public async Task<IActionResult> OnGetDelete(string shopId)
        {
            var item = _shopRepository.FindByShopId(shopId);
            if (item == null)
            {
                StatusMessage = "Error: Shop not found";
                return Page();
            }

            // remove Shop
            await _shopFlow.DeleteShop(item.ShopId);

            StatusMessage = "Shop deleted successfully";
            return LocalRedirect($"~/admin/shops/listshop");
        }

        public IActionResult OnPostFakeData()
        {
            Logs.debug("Fake data...");
            try
            {
                if (UtilitiesEx.IsRelease()) return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(new { message = "Error", data = "This feature is not available on production environment" }));

                var list = _shopRepository.ListShop(Constants.MaxPaging).Result;
                if (list.Count > 0) return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(new { message = "Error", data = "Fake data successfully" }));

                Partner partner = _partnerRepository.FindByPartnerPhone("+84336371979");
                if (partner == null) return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(new { message = "Error", data = "Partner not exits" }));

                ShopData.LoadDefaultData(partner.PartnerId, _shopRepository, _settingRepository);

                return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(new { message = "Success", data = "Fake data successfully" }));
            }
            catch (Exception ex)
            {
                Logs.debug($"Exception: {ex.Message ?? ex.InnerException.Message}");
                return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(new { message = "Error", data = ex })) { StatusCode = StatusCodes.Status400BadRequest };
            }
        }

        public async Task<IActionResult> OnPostDeleteFakeData()
        {
            Logs.debug("Delete fake data...");
            try
            {
                if (UtilitiesEx.IsRelease()) return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(new { message = "Error", data = "This feature is not available on production environment" }));

                var list = _shopRepository.ListShop(Constants.MaxPaging).Result;
                foreach (var item in list)
                {
                    await _shopFlow.DeleteShop(item.ShopId);
                }

                return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(new { message = "Success", data = "Delete dake data successfully" }));
            }
            catch (Exception ex)
            {
                Logs.debug($"Exception: {ex.Message ?? ex.InnerException.Message}");
                return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(new { message = "Error", data = ex })) { StatusCode = StatusCodes.Status400BadRequest };
            }
        }
    }
}
