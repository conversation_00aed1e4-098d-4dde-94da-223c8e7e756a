﻿@page
@using App.Base.Utilities
@using App.ECommerce.Units

@using App.ECommerce.Controllers
@using App.ECommerce.Repository.Entities
@using App.ECommerce.Services.UploadStore
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.AspNetCore.Mvc.TagHelpers
@inject IHtmlLocalizer<LangController> HtmlLocalizer

@model App.ECommerce.Areas.Admin.Pages.EditPartnerModel
@{
    ViewData["Title"] = "Edit Partner";
    ViewData["NameActivePage"] = NavigationPages.Edit_Partner;
}
@section Styles {
    <style>
    </style>
}
@await Html.PartialAsync("~/Areas/Admin/Shared/_StatusMessageNew.cshtml", Model.StatusMessageModel)

<!-- CONTAINER -->
<div class="main-container container-fluid">
    <!-- PAGE-HEADER -->
    <div class="page-header">
        <h1 class="page-title">@HtmlLocalizer["Partner.edit"]</h1>
        <div>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">@HtmlLocalizer["Dashboard.pages"]</a></li>
                <li class="breadcrumb-item active" aria-current="page">@HtmlLocalizer["Menu.Partners"]</li>
            </ol>
        </div>
    </div>
    <!-- PAGE-HEADER END -->

    <!-- ROW-1 OPEN -->
    <!-- Row -->
    <div class="row ">
        <div class="col-xl-12">
            <div class="card">
                <form asp-page-handler="CreateOrUpdate" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="card-header">
                        <h3 class="card-title">@HtmlLocalizer["Partner.edit"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <input type="text" class="form-control" name="PartnerId" value="@Model.Partner?.PartnerId" hidden>
                        </div>
                        <div class="row">
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Firstname">First Name <b style="color: red;">(*)</b></label>
                                    <input type="text" name="Firstname" value="@Model.Partner?.Firstname" id="input_Firstname" class="form-control" placeholder="First name" required>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Lastname">Last Name <b style="color: red;">(*)</b></label>
                                    <input type="text" name="Lastname" value="@Model.Partner?.Lastname" id="input_Lastname" class="form-control" placeholder="Last name" required>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Gender">Gender</label>
                                    <select id="input_Gender" name="Gender" value="@(Model.Partner?.Gender ?? TypeGender.Male)" class="form-control select2-show-search form-select select2-hidden-accessible" required>
                                        <option value="@TypeGender.Male">Male</option>
                                        <option value="@TypeGender.Female">Female</option>
                                        <option value="@TypeGender.Other">Other</option>
                                    </select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Birthdate">Birthdate <i>(yyyy-mm-dd)</i></label>
                                    <input type="text" name="Birthdate" value="@Model.Partner?.Birthdate?.ToString(DateTimes.format())" class="form-control" data-inputmask-alias="datetime" data-inputmask-inputformat="yyyy-mm-dd" data-mask="" im-insert="false" placeholder="yyyy-mm-dd">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            @* <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_Fullname">Full Name <b style="color: red;">(*)</b></label>
                                    <input type="text" name="Fullname" value="@Model.Partner?.Fullname" id="input_Fullname" class="form-control" placeholder="Full name" required>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div> *@
                        </div>
                        <div class="row">
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_PhoneNumber">Phone number <b style="color: red;">(*)</b></label>
                                    <div class="form-control" style="padding: 0; border: 0;">
                                        <input type="text" name="PhoneNumber" value="@Model.Partner?.PhoneNumber" style="display:none">
                                        <input type="text" name="PhoneNumber" value="@Model.Partner?.PhoneNumber" id="input_PhoneNumber" class="form-control" placeholder="Phone number" required disabled>
                                        <div class="valid-feedback">Looks good!</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Password">Password <b style="color: red;">(*)</b></label>
                                    <input type="text" name="Password" value="@Model.Partner?.Password.Decrypt()" id="input_Password" class="form-control" placeholder="Password" required>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Email">Email address <b style="color: red;">(*)</b></label>
                                    <input type="email" name="Email" value="@Model.Partner?.Email" id="input_Email" class="form-control" placeholder="Email address" required>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Balance">Balance (VNĐ)</label>
                                    <input type="text" name="Balance" value="@(Model.Partner?.Balance.ToString("#,##0").Replace(",", ".") ?? "0")" id="input_Balance" class="form-control" placeholder="Balance (VNĐ)" onkeyup="formatNumber(this)">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            @* <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_Point">Point</label>
                                    <input type="number" name="Point" value="@(Model.Partner?.Point ?? 0)" id="input_Point" class="form-control" placeholder="Point">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div> *@
                        </div>
                        @* <div class="row">
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_BankName">BankName</label>
                                    <input type="text" name="BankName" value="@Model.Partner?.BankName" id="input_BankName" class="form-control" placeholder="Bank name">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_BankAccountName">BankAccountName</label>
                                    <input type="text" name="BankAccountName" value="@Model.Partner?.BankAccountName" id="input_BankAccountName" class="form-control" placeholder="Bank account name">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12">
                                <div class="form-group">
                                    <label for="input_BankAccountNumber">BankAccountNumber</label>
                                    <input type="text" name="BankAccountNumber" value="@Model.Partner?.BankAccountNumber" id="input_BankAccountNumber" class="form-control" placeholder="Bank account number">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div> *@
                        <div class="row">
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Firstname">Province</label>
                                    <select id="_Province" name="ProvinceId" value="@Model.Partner?.ProvinceId" class="form-control select2-show-search form-select select2-hidden-accessible"></select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Firstname">District</label>
                                    <select id="_District" name="DistrictId" value="@Model.Partner?.DistrictId" class="form-control select2-show-search form-select select2-hidden-accessible"></select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Firstname">Wards</label>
                                    <select id="_Wards" name="WardsId" value="@Model.Partner?.WardsId" class="form-control select2-show-search form-select select2-hidden-accessible"></select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Address">Address</label>
                                    <input type="text" name="Address" value="@Model.Partner?.Address" id="input_Address" class="form-control" placeholder="Address">
                                    <input type="text" name="Longitude" value="@Model.Partner?.Longitude" id="input_Longitude" class="form-control" placeholder="Longitude" hidden>
                                    <input type="text" name="Latitude" value="@Model.Partner?.Latitude" id="input_Latitude" class="form-control" placeholder="Latitude" hidden>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Status">Status</label>
                                    <select id="input_Status" name="Status" value="@(Model.Partner?.Status ?? TypeStatus.Actived)" class="form-control select2-show-search form-select select2-hidden-accessible" required>
                                        <option value="@TypeStatus.Pending">Pending</option>
                                        <option value="@TypeStatus.Actived">Actived</option>
                                        <option value="@TypeStatus.InActived">InActived</option>
                                    </select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_Package">Package</label>
                                    <select id="input_Package" name="PackageId" class="form-control select2-show-search form-select select2-hidden-accessible">
                                        <option value="">Select Package</option>
                                    </select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_PackageStartDate">Package Start Date <i>(yyyy-mm-dd)</i></label>
                                    <input type="text" name="PackageStartDate" value="@(Model.Partner?.PackageStartDate?.ToString("yyyy-MM-dd"))" class="form-control" data-inputmask-alias="datetime" data-inputmask-inputformat="yyyy-mm-dd" data-mask="" im-insert="false" placeholder="yyyy-mm-dd">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-12">
                                <div class="form-group">
                                    <label for="input_PackageEndDate">Package End Date <i>(yyyy-mm-dd)</i></label>
                                    <input type="text" name="PackageEndDate" value="@(Model.Partner?.PackageEndDate?.ToString("yyyy-MM-dd"))" class="form-control" data-inputmask-alias="datetime" data-inputmask-inputformat="yyyy-mm-dd" data-mask="" im-insert="false" placeholder="yyyy-mm-dd">
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label>Avatar</label>
                                    <input class="form-control" name="Attachment" accept="image/jpeg, image/jpg, image/png" type="file" id="myFileUpload">
                                </div>
                                <div class="card-group bg-white" style="margin-bottom:10px;">
                                    <ul id="show-myFileUpload" class="mailbox-attachments d-flex align-items-stretch clearfix" style="overflow: scroll; width:100%;">
                                        @if (Model.Partner?.Avatar != null)
                                        {
                                            <li>
                                                <span class="mailbox-attachment-icon has-img" style="overflow: hidden;">
                                                    <img id="CategoryIconLable" src="@(S3Upload.GetUrlImage(Model.Partner?.Avatar))" onerror="this.src='/assets/sample/not_available.png';" alt="Attachment" style="height:10em;">
                                                </span>
                                                <div class="mailbox-attachment-info">
                                                    <a href="#" class="mailbox-attachment-name">
                                                        <i class="fa fa-camera" style="margin-top:5px;margin-right:2px;"></i>
                                                        <div id="CategoryIconPath">@Model.Partner?.Avatar</div>
                                                    </a>
                                                    <span class="mailbox-attachment-size clearfix mt-1">
                                                        <a onclick="$(this).parent().parent().parent().remove()" class="btn btn-default btn-sm float-right"><i class="fa fa-trash"></i></a>
                                                    </span>
                                                </div>
                                            </li>
                                        }
                                    </ul>
                                    <input hidden id="CategoryIcon" name="CategoryIcon" value="@Model.Partner?.Avatar" />
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="input_TypeLogin">Type Login</label>
                                    <select id="input_TypeLogin" name="Provider" value="@(Model.Partner?.Provider ?? TypeProviderLogin.Phone)" class="form-control select2-show-search form-select select2-hidden-accessible" required>
                                        <option value="@TypeProviderLogin.Phone">Phone</option>
                                        <option value="@TypeProviderLogin.Email">Email</option>
                                        <option value="@TypeProviderLogin.Google">Google</option>
                                        <option value="@TypeProviderLogin.Facebook">Facebook</option>
                                        <option value="@TypeProviderLogin.Website">Apple</option>
                                        <option value="@TypeProviderLogin.Other">Other</option>
                                    </select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                                <div class="form-group">
                                    <label for="input_TypeKind">Type Kind</label>
                                    <select id="input_TypeKind" name="Kind" value="@(Model.Partner?.Kind ?? TypeKind.Business)" class="form-control select2-show-search form-select select2-hidden-accessible" required>
                                        <option value="@TypeKind.Business">Business</option>
                                        <option value="@TypeKind.Individual">Individual</option>
                                        <option value="@TypeKind.Other">Other</option>
                                    </select>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                                <div class="form-group">
                                    <label for="input_Notes">About Me</label>
                                    <textarea name="Notes" id="input_Notes" class="form-control" rows="3">@Model.Partner?.Notes</textarea>
                                    <div class="valid-feedback">Looks good!</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-end">
                        <button class="btn btn-primary" type="submit">Update</button>
                        <a asp-area="Admin" asp-page="/partners/listpartner" class="btn btn-success float-right">Back &nbsp;<i class="ion-ios-rewind"></i></a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- /Row -->

    <!-- Package History -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Package History</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered text-nowrap border-bottom">
                            <thead>
                                <tr>
                                    <th class="wd-15p border-bottom-0">Package ID</th>
                                    <th class="wd-15p border-bottom-0">Package Name</th>
                                    <th class="wd-15p border-bottom-0">Start Date</th>
                                    <th class="wd-15p border-bottom-0">End Date</th>
                                    <th class="wd-15p border-bottom-0">Price</th>
                                    <th class="wd-15p border-bottom-0">Status</th>
                                    <th class="wd-15p border-bottom-0">Created Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.PackageHistories != null && Model.PackageHistories.Any())
                                {
                                    foreach (var package in Model.PackageHistories)
                                    {
                                        <tr>
                                            <td>@package.PackageId</td>
                                            <td>@(Model.PackageNames.ContainsKey(package.PackageId) ? Model.PackageNames[package.PackageId] : "Unknown")</td>
                                            <td>@package.StartDate.ToString("yyyy-MM-dd")</td>
                                            <td>@(package.EndDate != DateTime.MinValue ? package.EndDate.ToString("yyyy-MM-dd") : "Unlimited")</td>
                                            <td>@package.Price.ToString("#,##0").Replace(",", ".") VND</td>
                                            <td>
                                                @if (package.Status == TypeStatus.Actived)
                                                {
                                                    <span class="badge bg-success-transparent rounded-pill text-success p-2 px-3">@package.Status</span>
                                                }
                                                else if (package.Status == TypeStatus.InActived)
                                                {
                                                    <span class="badge bg-danger-transparent rounded-pill text-danger p-2 px-3">@package.Status</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning-transparent rounded-pill text-warning p-2 px-3">@package.Status</span>
                                                }
                                            </td>
                                            <td>@package.CreatedDate.ToString("dd/MM/yyyy")</td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="4" class="text-center">No package history found</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /Package History -->
</div>
<!-- CONTAINER CLOSED -->

@section Scripts {
    <!-- INTERNAL intlTelInput js-->
    <script src="~/sash/assets/plugins/intl-tel-input-master/intlTelInput.js"></script>
    <script src="~/sash/assets/plugins/intl-tel-input-master/country-select.js"></script>
    <script src="~/sash/assets/plugins/intl-tel-input-master/utils.js"></script>
    <!-- InputMask -->
    <script src="~/lib/moment/moment.min.js"></script>
    <script src="~/lib/inputmask/min/jquery.inputmask.bundle.min.js"></script>
    <!-- bs-custom-file-input -->
    <script src="~/lib/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <!-- google-map -->
    <script async src="https://maps.googleapis.com/maps/api/js?key=@(Model.GOOGLE_MAP_API)&libraries=places&callback=getLocation"></script>
    <script>
        function getLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(showPosition);
            } else {
                console.log(`Geolocation is not supported by this browser.`);
                const autocompleteOptions = {
                    componentRestrictions: { country: ["vn"] },
                    fields: ["formatted_address", "geometry", "name"],
                    strictBounds: false,
                };
                initMap(autocompleteOptions);
            }
        };

        function showPosition(position) {
            var longitude = position.coords.longitude;
            var latitude = position.coords.latitude;
            console.log(`current gps location: [${longitude},${latitude}]`);
            const defaultBounds = new google.maps.LatLngBounds(new google.maps.LatLng(latitude, longitude));
            const autocompleteOptions = {
                bounds: defaultBounds,
                componentRestrictions: { country: ["vn"] },
                fields: ["formatted_address", "geometry", "name"],
                strictBounds: false,
            };
            initMap(autocompleteOptions);
        };

        function initMap(autocompleteOptions) {
            const input = document.getElementById("input_Address");
            var options = {
                componentRestrictions: { country: ["vn"] },
                fields: ["formatted_address", "geometry", "name"],
                strictBounds: false,
            };
            if (autocompleteOptions != undefined) options = autocompleteOptions;
            const autocomplete = new google.maps.places.Autocomplete(input, options);
            autocomplete.addListener("place_changed", () => {
                const place = autocomplete.getPlace();
                if (!place.geometry || !place.geometry.location) {
                    // User entered the name of a Place that was not suggested and
                    // pressed the Enter key, or the Place Details request failed.
                    window.alert("No details available for input: '" + place.name + "'");
                    return;
                }
                console.log(place);
                console.log(`place.name: ${place.name}`);
                console.log(`place.formatted_address: ${place.formatted_address}`);
                const longitude = `${place.geometry.location.lng()}`;
                const latitude = `${place.geometry.location.lat()}`;
                console.log(`place.longitude: ${longitude}`);
                console.log(`place.latitude: ${latitude}`);
                $("#input_Longitude").val(longitude);
                $("#input_Latitude").val(latitude);
            });
        };
    </script>
    <script>
        function initControl() {
            /* Select Address */
            var s1data = $('#_Province').select2({
                cacheDataSource: [],
                placeholder: "Tỉnh/Thành phố",
                minimumInputLength: 0,
                tokenSeparators: [",", " "],
                templateResult: function (item) { return item.text; },
                templateSelection: function (item) { return item.text; },
                ajax: {
                    url: '?handler=ListProvince',
                    contentType: "application/json; charset=utf-8",
                    dataType: 'json',
                    type: "POST",
                    delay: 200,
                    cache: true,
                    cacheDataSource: [],
                    headers: { RequestVerificationToken: $('input:hidden[name="__RequestVerificationToken"]').val() },
                    data: function (query) {
                        return JSON.stringify({
                            search: query.term,
                            provinceId: -1,
                        });
                    },
                    processResults: function (data) {
                        var data = JSON.parse(data);
                        var items = $.map(data.data, function (item) {
                            return {
                                id: item.ProvinceID,
                                text: item.ProvinceName,
                            }
                        });
                        this.options.set('cacheDataSource', { items: items });
                        return { results: items };
                    },
                },
            }).data('select2');
            s1data.dataAdapter.query = function (params, callback) {
                var cacheDataSource = this.options.get('cacheDataSource');
                if (cacheDataSource && cacheDataSource.items) {
                    var term = params.term;
                    if (typeof term == "undefined" || term == null) {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    term = $.trim(term.toLowerCase());
                    if (term == "") {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    callback({
                        results: cacheDataSource.items.filter(function (item) {
                            return item.text.toLowerCase().includes(term);
                        })
                    });
                } else { // call the original logic
                    var ajaxAdapterFunc = jQuery.fn.select2.amd.require('select2/data/ajax');
                    var ajaxAdapter = new ajaxAdapterFunc(this.$element, this.options);
                    ajaxAdapter.query(params, callback);
                }
            };
            var provinceId = "@Html.Raw(Model.Partner?.ProvinceId ?? "")";
            var provinceName = "@Html.Raw(Model.Partner?.ProvinceName ?? "")";
            if (provinceId != "" && provinceName != "") {
                $('#_Province').append(new Option(provinceName, provinceId, true, true)).trigger('change');
            }
            //$('#_Province').append(new Option('Bình Ðịnh (BDH)', 40, true, true)).trigger('change');

            var s2data = $('#_District').select2({
                cacheDataSource: [],
                placeholder: "Quận/Huyện",
                minimumInputLength: 0,
                tokenSeparators: [",", " "],
                templateResult: function (item) { return item.text; },
                templateSelection: function (item) { return item.text; },
                ajax: {
                    url: '?handler=ListDistrict',
                    contentType: "application/json; charset=utf-8",
                    dataType: 'json',
                    type: "POST",
                    delay: 200,
                    cache: true,
                    cacheDataSource: [],
                    headers: { RequestVerificationToken: $('input:hidden[name="__RequestVerificationToken"]').val() },
                    data: function (query) {
                        var provinceId = $("#_Province option:selected").val();
                        return JSON.stringify({
                            search: query.term,
                            provinceId: (provinceId == undefined ? -1 : provinceId),
                        });
                    },
                    processResults: function (data) {
                        var data = JSON.parse(data);
                        var items = $.map(data.data, function (item) {
                            return {
                                id: item.DistrictID,
                                text: item.DistrictName,
                            }
                        });
                        this.options.set('cacheDataSource', { items: items });
                        return { results: items };
                    },
                },
            }).data('select2');
            s2data.dataAdapter.query = function (params, callback) {
                var cacheDataSource = this.options.get('cacheDataSource');
                if (cacheDataSource && cacheDataSource.items) {
                    var term = params.term;
                    if (typeof term == "undefined" || term == null) {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    term = $.trim(term.toLowerCase());
                    if (term == "") {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    callback({
                        results: cacheDataSource.items.filter(function (item) {
                            return item.text.toLowerCase().includes(term);
                        })
                    });
                } else { // call the original logic
                    var ajaxAdapterFunc = jQuery.fn.select2.amd.require('select2/data/ajax');
                    var ajaxAdapter = new ajaxAdapterFunc(this.$element, this.options);
                    ajaxAdapter.query(params, callback);
                }
            };
            var districtId = "@Html.Raw(Model.Partner?.DistrictId ?? "")";
            var districtName = "@Html.Raw(Model.Partner?.DistrictName ?? "")";
            if (districtId != "" && districtName != "") {
                $('#_District').append(new Option(districtName, districtId, true, true)).trigger('change');
            }
            //$('#_District').append(new Option('THÀNH PHỐ QUI NHƠN (5910)', 464, true, true)).trigger('change');

            var s3data = $('#_Wards').select2({
                cacheDataSource: [],
                placeholder: "Phường/Xã",
                minimumInputLength: 0,
                tokenSeparators: [",", " "],
                templateResult: function (item) { return item.text; },
                templateSelection: function (item) { return item.text; },
                ajax: {
                    url: '?handler=ListWards',
                    contentType: "application/json; charset=utf-8",
                    dataType: 'json',
                    type: "POST",
                    delay: 200,
                    cache: true,
                    cacheDataSource: [],
                    headers: { RequestVerificationToken: $('input:hidden[name="__RequestVerificationToken"]').val() },
                    data: function (query) {
                        var districtId = $("#_District option:selected").val();
                        return JSON.stringify({
                            search: query.term,
                            districtId: (districtId == undefined ? -1 : districtId),
                        });
                    },
                    processResults: function (data) {
                        var data = JSON.parse(data);
                        var items = $.map(data.data, function (item) {
                            return {
                                id: item.WardID,
                                text: item.WardName,
                            }
                        });
                        this.options.set('cacheDataSource', { items: items });
                        return { results: items };
                    },
                },
            }).data('select2');
            s3data.dataAdapter.query = function (params, callback) {
                var cacheDataSource = this.options.get('cacheDataSource');
                if (cacheDataSource && cacheDataSource.items) {
                    var term = params.term;
                    if (typeof term == "undefined" || term == null) {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    term = $.trim(term.toLowerCase());
                    if (term == "") {
                        callback({ results: cacheDataSource.items });
                        return
                    }
                    callback({
                        results: cacheDataSource.items.filter(function (item) {
                            return item.text.toLowerCase().includes(term);
                        })
                    });
                } else { // call the original logic
                    var ajaxAdapterFunc = jQuery.fn.select2.amd.require('select2/data/ajax');
                    var ajaxAdapter = new ajaxAdapterFunc(this.$element, this.options);
                    ajaxAdapter.query(params, callback);
                }
            };
            var wardsId = "@Html.Raw(Model.Partner?.WardsId ?? "")";
            var wardsName = "@Html.Raw(Model.Partner?.WardsName ?? "")";
            if (wardsId != "" && wardsName != "") {
                $('#_Wards').append(new Option(wardsName, wardsId, true, true)).trigger('change');
            }
            //$('#_Wards').append(new Option('PHƯỜNG TRẦN QUANG DIỆU', 8548, true, true)).trigger('change');

            $('#_Province').on('select2:selecting', function (e) {
                var data = $("#_Province option:selected").val();
                //console.log('_Province: ', e.params.args.data);
                s2data.dataAdapter.options.options.cacheDataSource = [];
                $("#_District").empty();
                s3data.dataAdapter.options.options.cacheDataSource = [];
                $("#_Wards").empty();
            });

            $('#_District').on('select2:selecting', function (e) {
                var data = $("#_District option:selected").val();
                //console.log('_District: ', e.params.args.data);
                s3data.dataAdapter.options.options.cacheDataSource = [];
                $("#_Wards").empty();
            });
        }
    </script>
    <script>
        $(function () {
            @* Auto selected value *@
            $("select").each((index, item) => {
                if ($(item).attr("value")) {
                    $(item).val($(item).attr("value")).change();
                }
            });

            @* Validate form submit *@
            window.addEventListener('load', function () {
                // Fetch all the forms we want to apply custom Bootstrap validation styles to
                var forms = document.getElementsByClassName('needs-validation');
                // Loop over them and prevent submission
                var validation = Array.prototype.filter.call(forms, function (form) {
                    form.addEventListener('submit', function (event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);

            initControl();

            $('[data-mask]').inputmask();
        });
    </script>
    <script>
        function _onDelete(index, el) {
            var myFileUpload = document.getElementById("myFileUpload");
            var files = myFileUpload.files;
            @* remove file at index *@
            var fileBuffer = [];
            Array.prototype.push.apply(fileBuffer, files);
            fileBuffer.splice(index, 1);
            const dT = new ClipboardEvent('').clipboardData || new DataTransfer();
            for (let file of fileBuffer) { dT.items.add(file); }
            myFileUpload.files = dT.files;
            files = myFileUpload.files;
            console.log(files);
            $(".image_" + index).remove()
        };

        $(function () {
            bsCustomFileInput.init();
            @* myFileUpload *@
            function readURL() {
                var $input = $(this);
                var showMyFileUpload = document.getElementById("show-myFileUpload");
                while (showMyFileUpload.firstChild) showMyFileUpload.removeChild(showMyFileUpload.firstChild);

                for (let i = 0; i < this.files.length; i++) {
                    var file = this.files[i];
                    console.log(file);
                    var fileName = file.name;
                    var fileSize = (file.size / 1024).toFixed(2);
                    var fileType = file.type;
                    var reader = new FileReader();
                    reader.onload = function (e) {
                    @*console.log(e.target);*@
                    var li = document.createElement('li');
                        li.className = 'image_' + i;
                        li.innerHTML = '\
                        <span class="mailbox-attachment-icon has-img" > \
                            <img src="' + e.target.result + '" alt="Attachment" style="height:10em;">\
                        </span>\
                        <div class="mailbox-attachment-info">\
                            <a href="#" class="mailbox-attachment-name">\
                                <i class="fa fa-camera" style="margin-top:5px;margin-right:2px;"></i>\
                                <div>' + fileName + '</div>\
                            </a>\
                            <span class="mailbox-attachment-size clearfix mt-1">\
                                <span>' + fileSize + ' KB</span>\
                                <a href="javascript:_onDelete('+ i + ', this)" class="btn btn-default btn-sm float-right"><i class="fa fa-trash"></i></a>\
                            </span>\
                        </div>';
                        showMyFileUpload.appendChild(li);
                    }
                    reader.readAsDataURL(file);
                }
            };
            $("#myFileUpload").change(readURL);
        })
    </script>
    <script>
         function formatNumber(input) {
            // Lấy giá trị hiện tại của input
            let value = input.value.replace(/\./g, '');
            
            // Chuyển đổi thành số
            let number = parseInt(value);
            
            // Kiểm tra nếu không phải số
            if (isNaN(number)) {
                input.value = '0';
                return;
            }
            
            // Định dạng số với dấu chấm phân cách hàng nghìn
            input.value = number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        }
    </script>
    <script>
        $(function() {
            // Load packages via AJAX
            $.ajax({
                url: '?handler=GetPackages',
                type: 'POST',
                contentType: 'application/json',
                headers: {
                    RequestVerificationToken: $('input:hidden[name="__RequestVerificationToken"]').val()
                },
                success: function(response) {
                    var data = JSON.parse(response);
                    if (data.success) {
                        var packages = data.data;
                        var packageSelect = $('#input_Package');
                        
                        packageSelect.find('option:gt(0)').remove();
                        
                        $.each(packages, function(index, package) {
                            packageSelect.append('<option value="' + package.PackageId + '">' + package.Name + '</option>');
                        });
                        
                        var selectedPackage = '@Model.Partner?.PackageId';
                        
                        if (selectedPackage && selectedPackage !== 'null' && selectedPackage !== '') {
                            if (packageSelect.find('option[value="' + selectedPackage + '"]').length > 0) {
                                packageSelect.val(selectedPackage).trigger('change');
                            }
                        }
                    } else {
                    }
                },
                error: function(error) {
                }
            });

            setTimeout(function() {
                try {
                    $('#input_Package').select2({
                        placeholder: "Select a package",
                        allowClear: true,
                        width: '100%'
                    });
                } catch (e) {
                }
            }, 500);
            
            $('#input_Package').change(function() {
                var packageId = $(this).val();
                if (packageId) {
                    // Nếu chọn gói, thì các trường ngày bắt đầu và kết thúc là bắt buộc
                    $('input[name="PackageStartDate"]').prop('required', true);
                    $('input[name="PackageEndDate"]').prop('required', true);
                    
                    // Thêm label * đỏ cho các trường bắt buộc
                    $('label[for="input_PackageStartDate"]').html('Package Start Date <i>(yyyy-mm-dd)</i> <b style="color: red;">(*)</b>');
                    $('label[for="input_PackageEndDate"]').html('Package End Date <i>(yyyy-mm-dd)</i> <b style="color: red;">(*)</b>');
                } else {
                    // Nếu không chọn gói, thì các trường ngày không bắt buộc
                    $('input[name="PackageStartDate"]').prop('required', false);
                    $('input[name="PackageEndDate"]').prop('required', false);
                    
                    // Bỏ label * đỏ
                    $('label[for="input_PackageStartDate"]').html('Package Start Date <i>(yyyy-mm-dd)</i>');
                    $('label[for="input_PackageEndDate"]').html('Package End Date <i>(yyyy-mm-dd)</i>');
                }
            });
            
            // Trigger change event to apply validation on page load
            $('#input_Package').trigger('change');
            
            // Form validation before submit
            $('form').submit(function(e) {
                var packageId = $('#input_Package').val();
                if (packageId) {
                    var startDate = $('input[name="PackageStartDate"]').val();
                    var endDate = $('input[name="PackageEndDate"]').val();
                    
                    if (!startDate) {
                        e.preventDefault();
                        return false;
                    }
                    
                    if (!endDate) {
                        e.preventDefault();
                        return false;
                    }
                    
                    // Validate end date is greater than start date
                    if (startDate && endDate) {
                        var start = new Date(startDate);
                        var end = new Date(endDate);
                        
                        if (end <= start) {
                            alert('Package End Date must be greater than Package Start Date');
                            e.preventDefault();
                            return false;
                        }
                    }
                }
            });
        });
    </script>
}
