@page "/admin/packages/listpackage"
@using App.ECommerce.Units
@using App.ECommerce.Controllers
@using App.ECommerce.Repository.Entities
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.AspNetCore.Mvc.TagHelpers
@inject IHtmlLocalizer<LangController> HtmlLocalizer

@model App.ECommerce.Areas.Admin.Pages.Packages.ListPackageModel
@{
    ViewData["Title"] = "List Package";
    ViewData["NameActivePage"] = NavigationPages.List_Package;
}

@section Styles {
    <!-- DataTables -->
    <link rel="stylesheet" href="~/lib/datatables-bs4/css/dataTables.bootstrap4.css">
    <style>
        .selected {
            background-color: #cff4d6 !important;
        }
        table {
            width: 100% !important;
            overflow: scroll !important;
        }
    </style>
}
@await Html.PartialAsync("~/Areas/Admin/Shared/_StatusMessage.cshtml", Model.StatusMessage)

<!-- CONTAINER -->
<div class="main-container container-fluid">
    <!-- PAGE-HEADER -->
    <div class="page-header">
        <h1 class="page-title">@HtmlLocalizer["Package.list.title"]</h1>
        <div>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">@HtmlLocalizer["Dashboard.pages"]</a></li>
                <li class="breadcrumb-item active" aria-current="page">@HtmlLocalizer["Menu.Packages"]</li>
            </ol>
        </div>
    </div>
    <!-- PAGE-HEADER END -->

    <!-- ROW-1 OPEN -->
    <div class="row">
        <div class="col-md-12">
            <!-- CARD TABLE-->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title" style="width:100%;">@HtmlLocalizer["Package.list.title"]</h3>
                    <div class="card-options" style="width:100%;">
                        <div class="row" style="width:100%;display:flex;justify-content:flex-end;">
                            <a id="addRow" asp-area="Admin" asp-page="/packages/createpackage" class="btn btn-success float-right" style="width:fit-content;margin-bottom:0.5em;margin-right:0.5em;">@HtmlLocalizer["Paging.Create.New"] <i class="ion ion-plus"></i></a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="id_table_package" class="table table-bordered text-nowrap border-bottom">
                            <thead>
                                <tr>
                                    <th class="wd-5p border-bottom-0">.No</th>
                                    <th class="wd-15p border-bottom-0">PackageID</th>
                                    <th class="wd-10p border-bottom-0">Code</th>
                                    <th class="wd-20p border-bottom-0">Name</th>
                                    <th class="wd-10p border-bottom-0">Price</th>
                                    <th class="wd-10p border-bottom-0">Duration (Days)</th>
                                    <th class="wd-5p border-bottom-0">Level</th>
                                    <th class="wd-5p border-bottom-0">Is Active</th>
                                    <th class="wd-5p border-bottom-0">Is Show</th>
                                    <th class="wd-15p border-bottom-0">Controls</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- /.CARD TABLE-->
        </div>
    </div>
    <!-- /Row -->
</div>
<!-- CONTAINER CLOSED -->

<!-- modal DELETE -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger mb-20">@HtmlLocalizer["Paging.Delete"]</h5>
                <button class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="deleteDescription">@HtmlLocalizer["Paging.Delete.Description"] ?…</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-light" data-bs-dismiss="modal">@HtmlLocalizer["Paging.Close"]</button>
                <button id="deleteConfirm" class="btn btn-primary">@HtmlLocalizer["Paging.Save"]</button>
            </div>
        </div>
    </div>
</div>
<!-- /.modal DELETE -->

@section Scripts {
    <!-- DataTables -->
    <script src="~/lib/datatables/jquery.dataTables.js"></script>
    <script src="~/lib/datatables-bs4/js/dataTables.bootstrap4.js"></script>
    <script>
        $(function () {
            config_datatables('#id_table_package'); // Assuming you have a global config_datatables function
            $(document).ready(function () {
                var table = $('#id_table_package').DataTable({
                    "dom": '<"top thiago-top-datatables"lf>rt<"bottom thiago-bottom-datatables"ip><"clear">',
                    "language": {
                        "lengthMenu": "@HtmlLocalizer["Paging.Lengt.Menu"]",
                        "zeroRecords": "@HtmlLocalizer["Paging.Zero.Records"]",
                        "info": "@HtmlLocalizer["Paging.Info"]",
                        "infoEmpty": "@HtmlLocalizer["Paging.Info.Empty"]",
                        "infoFiltered": "@HtmlLocalizer["Paging.Info.Filtered"]",
                        "sSearch": "@HtmlLocalizer["Paging.sSearch"]",
                        "paginate": {
                            "next": "@HtmlLocalizer["Paging.Next"]",
                            "previous": "@HtmlLocalizer["Paging.Previous"]",
                        },
                    },
                    "paging": true,
                    "lengthChange": true,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "processing": true,
                    "serverSide": true,
                    "order": [[3, 'asc']], // Default sort by Name (index adjusted for new Code column)
                    "columns": [
                        {
                            "data": "packageId", "orderable": false, "width": "5%",
                            "render": function (data, type, row, meta) {
                                var _iDisplayLength = meta.settings._iDisplayLength;
                                var _iDisplayStart = meta.settings._iDisplayStart;
                                var _iRecordsTotal = meta.settings._iRecordsTotal;
                                return `${(_iDisplayStart + (meta.row + 1))}`;
                            }
                        },
                        {
                            "data": "packageId", "orderable": false, "width": "15%",
                            "render": function (data, type, row, meta) {
                                return `<div class="auto-break-line-150">${row.packageId}</div>`;
                            }
                        },
                        { "data": "code", "orderable": true, "width": "10%" },
                        { "data": "name", "orderable": true, "width": "20%" },
                        { "data": "price", "orderable": true, "width": "10%", "render": function(data) { return data.toLocaleString(); } },
                        { "data": "durationDays", "orderable": true, "width": "10%" },
                        { "data": "level", "orderable": true, "width": "5%" },
                        { "data": "isActive", "orderable": true, "width": "5%", "render": function(data) { return data ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-danger">No</span>'; } },
                        { "data": "isShow", "orderable": true, "width": "5%", "render": function(data) { return data ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-danger">No</span>'; } },
                        {
                            "data": "button", "orderable": false, "width": "15%",
                            "render": function (data, type, row, meta) {
                                return `<div class='g-2'>
                                        <a onclick="EditData(this,${meta.row},${meta.col});" class='btn text-primary btn-sm' data-bs-toggle='tooltip' data-bs-original-title='Edit'>Edit <span class='fe fe-edit fs-14'></span></a>
                                        <a onclick="DeleteData(this,${meta.row},${meta.col});" class='btn text-danger btn-sm' data-bs-toggle='tooltip' data-bs-original-title='Delete'>Delete <span class='fe fe-trash-2 fs-14'></span></a>
                                    </div>`;
                            }
                        }
                    ],
                    "ajax": $.fn.dataTable.pipeline({
                        url: '?handler=ListPackage', // Changed handler
                        data: function (d) {
                            delete d.columns;
                        },
                        pages: 1 // if you have pipeline.js, otherwise use standard ajax
                    }),
                });
            });
        });

        function EditData(btn, row, col) {
            var rowData = $('#id_table_package').DataTable().row(row).data();
            var packageId = rowData.packageId; // Changed from rowData.id
            window.location.href = '/admin/packages/editpackage?id=' + packageId;
        }

        function DeleteData(btn, row, col) {
            var rowData = $('#id_table_package').DataTable().row(row).data();
            var packageId = rowData.packageId; // Changed from rowData.id
            $('#deleteModal').modal('show');
            $('#deleteConfirm').off('click').on('click', function (e) { // Use .off('click').on('click', ...) to prevent multiple bindings
                $('#deleteModal').modal('hide');
                // Consider using an AJAX call for delete for a smoother experience
                window.location.href = '/admin/packages/listpackage?handler=Delete&id=' + packageId;
            });
        }
    </script>
}