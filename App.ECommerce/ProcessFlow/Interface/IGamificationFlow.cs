using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos.GamificationDtos;

namespace App.ECommerce.ProcessFlow.Interface
{
    public interface IGamificationFlow
    {
        Task<List<GameCampaignDto>> GetListCampaign(string shopId);
        Task<GameCampaignDto> GetCampaignActiveByShopId(string shopId);
        Task<string> CreateCampaign(GameCampaignDto gameCampaign);
        Task<string> UpdateCampaign(UpdateCampaignDto updateCampaign);
        Task<CampaignResponseDto> GetCampaign(string campaignId);
        Task<PrizeResponseDto> CreatePrizeAsync(CreatePrizeInputDto input);
        Task<PrizeResponseDto> UpdatePrizeAsync(UpdatePrizeInputDto input);
        Task<List<PrizeResponseDto>> GetAllPrizesAsync(string campaignId);
        Task<GameSessionResponseDto> StartGameSessionAsync(string shopId, User user);
        Task<BrandResponseDataDto> CreateGameBrand(string shopId);
        Task ActiveCampaign(string shopId, string campaignId);
        Task Activate(string shopId);
        Task Deactivate(string shopId);
        Task<GamificationShop> GetGameShopInfo(string shopId);
        Task<List<GameDto>> GetAllGamesAsync();
    }
}