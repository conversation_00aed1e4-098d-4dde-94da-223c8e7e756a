using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.NhanhDtos;
using App.ECommerce.Resource.Dtos.Webhooks;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Enums;

namespace App.ECommerce.ProcessFlow.Interface;

public interface ISyncServiceFlow
{
    /// <summary>
    /// L<PERSON>u cấu hình đồng bộ
    /// </summary>
    Task<Result<SyncServiceConfig>> SaveConfig(SyncServiceConfigDto dto);

    /// <summary>
    /// L<PERSON>y cấu hình theo shop ID và service
    /// </summary>
    Task<SyncServiceConfig> GetConfig(SyncServiceEnum syncService, string shopId);

    /// <summary>
    /// Xóa cấu hình
    /// </summary>
    Task<Result<bool>> DeleteConfig(SyncServiceEnum syncService, string shopId);

    /// <summary>
    /// Đồng bộ sản phẩm từ webhook
    /// </summary>
    Task<Result<bool>> SyncProductFromWebhook(SyncServiceEnum syncService, object productData, string shopId);

    /// <summary>
    /// Đồng bộ đơn hàng từ webhook
    /// </summary>
    Task<Result<bool>> SyncOrderFromWebhook(SyncServiceEnum syncService, object orderData, string shopId);

    /// <summary>
    /// Đồng bộ khách hàng từ webhook
    /// </summary>
    Task<Result<bool>> SyncCustomerFromWebhook(SyncServiceEnum syncService, object customerData, string shopId);

    /// <summary>
    /// Xóa sản phẩm từ webhook
    /// </summary>
    Task<Result<bool>> DeleteProductsFromWebhook(SyncServiceEnum syncService, object productIds, string shopId);

    /// <summary>
    /// Xóa đơn hàng từ webhook
    /// </summary>
    Task<Result<bool>> DeleteOrderFromWebhook(SyncServiceEnum syncService, object orderData, string shopId);

    /// <summary>
    /// Cập nhật đơn hàng lên external service
    /// </summary>
    Task<Result<bool>> CreateOrderToExternalService(SyncServiceEnum syncService, Order order, string shopId);

    /// <summary>
    /// Cập nhật access code
    /// </summary>
    Task<Result<SyncServiceConfig>> UpdateAccessCode(UpdateAccessCodeDto dto);

    Task<SyncServiceConfig> FindByBusinessId(int businessId);
}
