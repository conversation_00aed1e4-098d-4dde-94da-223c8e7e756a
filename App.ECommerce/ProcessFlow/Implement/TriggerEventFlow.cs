using System;
using System.Globalization;
using System.Linq;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Services;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using log4net;
using Newtonsoft.Json;

namespace App.ECommerce.ProcessFlow.Implement;

public class TriggerEventFlow : ITriggerEventFlow
{
    private readonly ILog _log = LogManager.GetLogger(typeof(TriggerEventFlow));
    private readonly IZalo_Template_Repository _zaloTemplateRepository;
    private readonly ITriggerEventHistoryRepository _triggerEventHistoryRepository;
    private readonly IUserRepository _userRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IShopRepository _shopRepository;
    private readonly IShopSettingRepository _shopSettingRepository;
    private readonly ITriggerEventRepository _triggerEventRepository;
    private readonly IZaloUIDFlow _zaloUIDFlow;
    private readonly IEmailRepository _emailRepository;
    private readonly IPartnerRepository _partnerRepository;
    private readonly IMembershipLevelRepository _membershipLevelRepository;


    public TriggerEventFlow(
        IZalo_Template_Repository zaloTemplateRepository,
        ITriggerEventHistoryRepository triggerEventHistoryRepository,
        IOrderRepository orderRepository,
        IUserRepository userRepository,
        IShopRepository shopRepository,
        IShopSettingRepository shopSettingRepository,
        ITriggerEventRepository triggerEventRepository,
        IZaloUIDFlow zaloUIDFlow,
        IEmailRepository emailRepository,
        IPartnerRepository partnerRepository,
        IMembershipLevelRepository membershipLevelRepository
    )
    {
        _zaloTemplateRepository = zaloTemplateRepository;
        _triggerEventHistoryRepository = triggerEventHistoryRepository;
        _orderRepository = orderRepository;
        _userRepository = userRepository;
        _shopRepository = shopRepository;
        _shopSettingRepository = shopSettingRepository;
        _triggerEventRepository = triggerEventRepository;
        _zaloUIDFlow = zaloUIDFlow;
        _emailRepository = emailRepository;
        _partnerRepository = partnerRepository;
        _membershipLevelRepository = membershipLevelRepository;
    }

    public async Task TriggerEventSendMessage(string triggerEventCode, string shopId, string refId)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();
            var objTriggerEvent = await _triggerEventRepository.GetTriggerEventByCode(requestId, triggerEventCode);
            if (objTriggerEvent == null) return;
            
            var listTemplate = await _triggerEventRepository.GetListTemplateById(shopId, objTriggerEvent.TriggerEventId);
            await SendOrderNotificationEmail(requestId, triggerEventCode, refId);
            foreach (TriggerEventTemplate obj in listTemplate) 
            {
                if (obj.ChannelType == ChannelTypeEnum.UID) 
                {
                    var objTemplate = await _zaloTemplateRepository.GetTemplateById(requestId, shopId, obj.TemplateId);
                    TriggerSendMessageInputDto objInput = new TriggerSendMessageInputDto() 
                    {
                        RequestId = requestId,
                        ShopId = shopId,
                        TriggerEventId = objTriggerEvent.TriggerEventId,
                        TriggerEventCode = triggerEventCode,
                        RefId = refId,
                        Template = objTemplate
                    };
                    await SendMessageZaloUID(objInput);
                }
            }

            await UpdateUserZaloOAStatus(triggerEventCode, refId);
        }
        catch (Exception ex)
        {
            _log.Error($"Error updating trigger event {triggerEventCode} with RefId {refId}.", ex);
        }
    }

    private async Task UpdateUserZaloOAStatus(string triggerEventCode, string refId)
    {
        try
        {
            if (triggerEventCode == TriggerEventConst.CUSTOMER_CARE_FOLLOW_OA || triggerEventCode == TriggerEventConst.CUSTOMER_CARE_UNFOLLOW_OA)
            {
                var user = _userRepository.FindByZaloId(refId);
                if (user != null)
                {
                    user.IsZaloOA = triggerEventCode == TriggerEventConst.CUSTOMER_CARE_FOLLOW_OA;
                    await _userRepository.UpdateFollowOAZalo(user);
                }
            }
        }
        catch (Exception ex)
        {
            _log.Error($"Error updating user ZaloOA status for trigger event {triggerEventCode} with RefId {refId}.", ex);
        }
    }

    private async Task SendMessageZaloUID(TriggerSendMessageInputDto objInput)
    {
        try
        {
            _log.Info($"{objInput.RequestId} SendMessageZaloUID objInput: {JsonConvert.SerializeObject(objInput)}");

            var objOA = _shopSettingRepository.FindByShopId(objInput.ShopId);

            var parameters = GetTemplateData(objInput.RequestId, objInput.TriggerEventCode, objInput.RefId);

            if (parameters.Count <= 0) {
                _log.Info($"{objInput.RequestId} SendMessageZaloUID Count parameters: {parameters.Count}");

                return;
            }

            _log.Info($"{objInput.RequestId} SendMessageZaloUID parameters: {JsonConvert.SerializeObject(parameters)}");

            var message = ReplaceTemplateParameters(objInput.Template.Transaction, parameters);

            string zaloId = "";
            if (parameters.ContainsKey("{{zalo_id}}") && parameters["{{zalo_id}}"] != null)
                zaloId = parameters["{{zalo_id}}"].ToString();

            _log.Info($"{objInput.RequestId} SendMessageZaloUID zaloId: {zaloId}");

            string recipient = "";
            if (parameters.ContainsKey("{{customer_name}}") && parameters["{{customer_name}}"] != null)
                recipient = parameters["{{customer_name}}"].ToString();

            if (zaloId.Length > 0)
            {
                objInput.Template.Transaction.Recipient.UserId = zaloId;

                TriggerEventHistory objHistory = new TriggerEventHistory
                {
                    Id = Guid.NewGuid(),
                    PartnerId = objInput.Template.PartnerId,
                    ShopId = objInput.ShopId,
                    TriggerEventId = objInput.TriggerEventId,
                    TriggerEventCode = objInput.TriggerEventCode,
                    RefId = objInput.RefId,
                    TemplateId = objInput.Template.TemplateId,
                    ChannelType = ChannelTypeEnum.UID,
                    Status = TriggerEventHistoryStatusEnum.Processing,
                    Price =  objInput.Template.Cost,
                    Recipient = recipient,
                    Request = JsonConvert.SerializeObject(message),
                    RetryCount = 0
                };

                _log.Info($"{objInput.RequestId} TriggerEventSendMessage Create History Send UID objHistory: {JsonConvert.SerializeObject(objHistory)}");

                await _triggerEventHistoryRepository.CreateAsync(objHistory);

                await _zaloUIDFlow.ProcessMessageAsync(objInput.RequestId, objInput.Template, objHistory);
            }
            else {
                _log.Info($"{objInput.RequestId} SendMessageZaloUID zaloId is empty for RefId: {objInput.RefId}");
            }
        }
        catch (Exception ex)
        {
            _log.Error(ex);
        }
    }

    private Dictionary<string, object> GetTemplateData(string requestId, string triggerEvent, string refId)
    {
        var parameters = new Dictionary<string, object>();

        switch (triggerEvent)
        {
            case TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_SUCCESS:
                GetOrderData(requestId, parameters, refId);
                break;
            case TriggerEventConst.TRANSACTION_NOTIFICATION_PAYMENT_SUCCESS:
                GetOrderData(requestId, parameters, refId);
                break;
            case TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_CANCELED:
                GetOrderData(requestId, parameters, refId);
                break;
            case TriggerEventConst.TRANSACTION_NOTIFICATION_DELIVERY:
                GetOrderData(requestId, parameters, refId);
                break;
            case TriggerEventConst.TRANSACTION_NOTIFICATION_DELIVERY_SUCCESS:
                GetOrderData(requestId, parameters, refId);
                break;
            case TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_PROCESSING:
                GetOrderData(requestId, parameters, refId);
                break;
            case TriggerEventConst.CUSTOMER_CARE_PAYMENT_REMINDER:
                GetOrderData(requestId, parameters, refId);
                break;
            case TriggerEventConst.CUSTOMER_CARE_BIRTHDAY_GREETING:
                GetUserData(requestId, parameters, refId);
                break;
            case TriggerEventConst.CUSTOMER_CARE_CREATE_MINIAPP_ACCOUNT:
                GetUserData(requestId, parameters, refId);
                break;
            case TriggerEventConst.CUSTOMER_CARE_FOLLOW_OA:
                GetUserByZaloId(requestId, parameters, refId);
                break;
            case TriggerEventConst.CUSTOMER_CARE_UNFOLLOW_OA:
                GetUserByZaloId(requestId, parameters, refId);
                break;
        }

        return parameters;
    }

    private void GetShopData(string requestId, Dictionary<string, object> parameters, string shopId) {
        try
        {
            Shop objShop = _shopRepository.FindByShopId(shopId);

            if (objShop != null) {
                parameters.Add("{{shop_name}}", objShop.ShopName);
                parameters.Add("{{opening_hours}}", objShop.OpenTime);

                var objSetting = _shopSettingRepository.FindByShopId(shopId);

                if (objSetting != null) {
                    parameters.Add("{{oa_name}}", objSetting.ZaloOAName);
                }
            }
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} GetShopData Exception: {ex.Message}", ex);

            throw;
        }
    }

    private void HandleUserData(string requestId, Dictionary<string, object> parameters, User objUser) {
        try
        {
            if (objUser != null)
            {
                var transaction = _membershipLevelRepository.CalculateUserScoreDetails(objUser);
                objUser.Point = transaction.CurrentPoint;

                parameters.Add("{{zalo_id}}", objUser.ZaloIdByOA ?? objUser.ZaloId);
                parameters.Add("{{customer_id}}", objUser.UserId);
                parameters.Add("{{customer_name}}", objUser.Fullname);
                parameters.Add("{{phone}}", objUser.PhoneNumber);
                parameters.Add("{{email}}", objUser.Email);
                parameters.Add("{{gender}}", objUser.Gender);
                parameters.Add("{{birthday}}", objUser.Birthdate);
                parameters.Add("{{customer_address}}", objUser.Address);
                parameters.Add("{{point}}", objUser.Point);
                parameters.Add("{{register_date}}", objUser.Created.ToString("dd/MM/yyyy"));
                parameters.Add("{{referral_code}}", objUser.ReferralCode);
            }
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} HandleUserData Exception: {ex.Message}", ex);

            throw;
        }
    }

    private void GetUserData(string requestId, Dictionary<string, object> parameters, string refId) {
        try
        {
            User objUser = _userRepository.FindByUserId(refId);

            if (objUser != null) {
                HandleUserData(requestId, parameters, objUser);

                if (!string.IsNullOrEmpty(objUser.ShopId))
                    GetShopData(requestId, parameters, objUser.ShopId);
            }
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} GetUserData Exception: {ex.Message}", ex);

            throw;
        }
    }

    private void GetUserByZaloId(string requestId, Dictionary<string, object> parameters, string zaloId) {
        try
        {
            User objUser = _userRepository.FindByZaloId(zaloId);

            if (objUser != null) {
                HandleUserData(requestId, parameters, objUser);

                if (!string.IsNullOrEmpty(objUser.ShopId))
                    GetShopData(requestId, parameters, objUser.ShopId);
            }
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} objUser Exception: {ex.Message}", ex);

            throw;
        }
    }

    private void GetOrderData(string requestId, Dictionary<string, object> parameters, string refId)
    {
        try
        {
            var objOrder = _orderRepository.FindByOrderId(refId);

            if (objOrder != null)
            {
                if (objOrder.ShopId != null)
                    GetShopData(requestId, parameters, objOrder.ShopId);

                if (objOrder.Creator != null)
                {
                    User objUser = _userRepository.FindByUserId(objOrder.Creator.UserId);

                    if (objUser != null)
                        HandleUserData(requestId, parameters, objUser);
                }

                parameters.Add("{{order_code}}", objOrder.OrderNo);
                parameters.Add("{{order_status}}", objOrder.Status);
                parameters.Add("{{order_id}}", objOrder.OrderId);
                parameters.Add("{{total_amount}}", objOrder.Price.ToString("N0", new CultureInfo("vi-VN")));
                parameters.Add("{{order_date}}", objOrder.Created.ToString("HH:mm dd/MM/yyyy"));
                parameters.Add("{{order_note}}", objOrder.Notes);
                parameters.Add("{{payment_method}}", objOrder.TypePay);
                parameters.Add("{{payment_status}}", objOrder.StatusPay);
                
                // order items information
                parameters.Add("{{order_items}}", objOrder.ListItems);
                parameters.Add("{{items_count}}", objOrder.ListItems?.Count ?? 0);
                parameters.Add("{{point_price}}", objOrder.PointPrice.ToString("N0", new CultureInfo("vi-VN")));
                parameters.Add("{{voucher_transport_price}}", objOrder.VoucherTransportPrice.ToString("N0", new CultureInfo("vi-VN")));
                parameters.Add("{{voucher_promotion_price}}", objOrder.VoucherPromotionPrice.ToString("N0", new CultureInfo("vi-VN")));
                parameters.Add("{{total_after_tax}}", objOrder.TotalAfterTax.ToString("N0", new CultureInfo("vi-VN")));
                parameters.Add("{{user_shipping_address}}", objOrder.UserShippingAddress);
                parameters.Add("{{transport_service}}", objOrder.TransportService);
                parameters.Add("{{status_delivery}}", objOrder.StatusDelivery);
                
                // order summary information
                if (objOrder.ListItems != null && objOrder.ListItems.Count > 0)
                {
                    var originPrice = objOrder.ListItems.Sum(item => item.Price * item.Quantity);
                    var totalBeforeTax = objOrder.ListItems.Sum(item => item.TotalBeforeTax);
                    var totalAfterTaxItems = objOrder.ListItems.Sum(item => item.TotalAfterTax);
                    var taxPrice = objOrder.ListItems.Sum(item => item.TaxAmount);
                    var taxRate = objOrder.ListItems.Sum(item => item?.TaxRate ?? 0);
                    var taxAmount = objOrder.ListItems.Sum(item => item.TaxAmount);
                    var voucherProductDiscount = objOrder.ListItems.Sum(item => item.VoucherDiscount);
                    var pointDiscount = objOrder.ListItems.Sum(item => item.PointDiscount);
                    var transportPrice = objOrder.TransportPrice;
                    var voucherTransportDiscount = objOrder.VoucherTransportPrice;
                    var voucherPromotionDiscount = objOrder.VoucherPromotionPrice;
                    
                    var viCulture = new CultureInfo("vi-VN");
                    parameters.Add("{{origin_price}}", ((decimal)originPrice).ToString("N0", viCulture));
                    parameters.Add("{{tax_price}}", taxPrice.ToString("N0", viCulture));   
                    parameters.Add("{{tax_rate}}", taxRate.ToString("N0", viCulture));
                    parameters.Add("{{tax_amount}}", taxAmount.ToString("N0", viCulture));
                    parameters.Add("{{total_before_tax}}", totalBeforeTax.ToString("N0", viCulture));
                    parameters.Add("{{voucher_product_discount}}", voucherProductDiscount.ToString("N0", viCulture));
                    parameters.Add("{{point_discount}}", pointDiscount.ToString("N0", viCulture));
                    parameters.Add("{{total_after_tax_item}}", totalAfterTaxItems.ToString("N0", viCulture));
                    parameters.Add("{{transport_price}}", transportPrice.ToString("N0", viCulture));
                    parameters.Add("{{voucher_transport_discount}}", voucherTransportDiscount.ToString("N0", viCulture));
                    parameters.Add("{{voucher_promotion_discount}}", voucherPromotionDiscount.ToString("N0", viCulture));
                }
            }
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} GetOrderSuccessData Exception: {ex.Message}", ex);

            throw;
        }
    }

    private Zalo_Transaction ReplaceTemplateParameters(Zalo_Transaction template, Dictionary<string, object> parameters)
    {
        if (template == null)
            return null;

        try
        {
            var result = template;

            if (result.Message?.Text != null)
                result.Message.Text = ReplaceParametersInString(result.Message.Text, parameters);

            if (result.Message?.Attachment?.Payload?.Elements != null)
            {
                foreach (var element in result.Message.Attachment.Payload.Elements)
                {
                    if ((element.Type == Zalo_Element_Type_Enum.Header || element.Type == Zalo_Element_Type_Enum.Text) && element.Content != null)
                        element.Content = ReplaceParametersInString(element.Content, parameters);

                    if (element.Type == Zalo_Element_Type_Enum.Table && element.ContentTable != null)
                    {
                        var tableContent = element.ContentTable;

                        if (tableContent != null)
                        {
                            foreach (var obj in tableContent)
                            {
                                obj.Value = ReplaceParametersInString(obj.Value, parameters);
                            }
                        }
                    }
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _log.Error($"Error replacing template parameters: {ex.Message}", ex);
            return null;
        }
    }

    private string ReplaceParametersInString(string content, Dictionary<string, object> parameters)
    {
        if (string.IsNullOrEmpty(content))
            return content;

        var result = content;
        foreach (var param in parameters)
        {
            var value = param.Value?.ToString() ?? string.Empty;
            result = result.Replace(param.Key, value);
        }
        return result;
    }

    private async Task SendOrderNotificationEmail(string requestId, string triggerEventCode, string refId)
    {
        try
        {
            if (!IsValidTriggerEvent(triggerEventCode)) return;
            
            var objOrder = _orderRepository.FindByOrderId(refId);
            if (objOrder == null) return;

            var partnerId = objOrder.PartnerId;

            if (partnerId == null) {
                var objShop = _shopRepository.FindByShopId(objOrder.ShopId);
                if (objShop != null) partnerId = objShop.PartnerId;
            }

            var objPartner = await _partnerRepository.FindByPartnerId(partnerId);
            if (objPartner == null) return;

            var parameters = GetTemplateData(requestId, triggerEventCode, refId);
            if (parameters.Count <= 0) return;

            string toEmail = !string.IsNullOrEmpty(objPartner.NotificationEmail) 
                ? objPartner.NotificationEmail 
                : objPartner.Email;                
            if (string.IsNullOrEmpty(toEmail)) return;

            await _emailRepository.SendOrderNotificationEmailAsync(toEmail, parameters);
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} SendOrderNotificationEmail EXCEPTION: {ex.Message}", ex);
        }
    }

    private bool IsValidTriggerEvent(string triggerEventCode)
    {
        return triggerEventCode switch
        {
            TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_SUCCESS => true,
            // TriggerEventConst.TRANSACTION_NOTIFICATION_PAYMENT_SUCCESS => true,
            // TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_CANCELED => true,
            // TriggerEventConst.TRANSACTION_NOTIFICATION_DELIVERY => true,
            // TriggerEventConst.TRANSACTION_NOTIFICATION_DELIVERY_SUCCESS => true,
            // TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_PROCESSING => true,
            // TriggerEventConst.CUSTOMER_CARE_PAYMENT_REMINDER => true,
            _ => false
        };
    }

    #region  Check Unpaid Orders
    private async Task<bool> HasSentNotification(string requestId, string shopId, string triggerEventCode, string refId)
    {
        try
        {
            var history = await _triggerEventHistoryRepository.GetListHistoryByRefId(shopId, triggerEventCode, refId);

            return history != null && history.Any();
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} HasSentNotification failed: ", ex);
            return false;
        }
    }

    public async Task CheckUnpaidOrders()
    {
        var requestId = Guid.NewGuid().ToString();

        try
        {
            var unpaidOrders = await _orderRepository.GetUnpaidOrdersOlderThanMinutes(30);

            foreach (var order in unpaidOrders)
            {
                var hasSent = await HasSentNotification(
                    requestId,
                    order.ShopId,
                    TriggerEventConst.CUSTOMER_CARE_PAYMENT_REMINDER,
                    order.OrderId
                );

                if (hasSent || order.Creator is not { UserId: var userId }) continue;

                var user = _userRepository.FindByUserId(userId);
                if (user is { ZaloId: not null } && !string.IsNullOrWhiteSpace(user.ZaloId))
                {
                    _log.Info($"{requestId} CheckUnpaidOrdersJob Fullname: {user.Fullname} | ZaloId: {user.ZaloId}");

                    await TriggerEventSendMessage(
                        TriggerEventConst.CUSTOMER_CARE_PAYMENT_REMINDER,
                        order.ShopId,
                        order.OrderId
                    );
                }
            }
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} CheckUnpaidOrdersJob failed:", ex);
        }
    }
    #endregion
}
