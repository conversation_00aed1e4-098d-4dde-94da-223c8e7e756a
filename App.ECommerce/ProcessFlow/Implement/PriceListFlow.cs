using System;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Units;
using App.ECommerce.Units.Enums.Items;
using log4net;
using Newtonsoft.Json;
using App.ECommerce.Resource.Model;
using App.ECommerce.Resource.Dtos.ResultDtos;
using App.Base.Utilities;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Services.UploadStore;

namespace App.ECommerce.ProcessFlow.Implement;

public class PriceListFlow : IPriceListFlow
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(PriceListFlow));
    private readonly IUserRepository _userRepository;
    private readonly IPriceListRepository _priceListRepository;
    private readonly IItemsRepository _itemsRepository;
    private readonly IMembershipLevelRepository _membershipLevelRepository;

    public PriceListFlow(
        IUserRepository userRepository,
        IPriceListRepository priceListRepository,
        IItemsRepository itemsRepository,
        IMembershipLevelRepository membershipLevelRepository
    )
    {
        _userRepository = userRepository;
        _priceListRepository = priceListRepository;
        _itemsRepository = itemsRepository;
        _membershipLevelRepository = membershipLevelRepository;
    }

    /// <summary>
    /// Tính toán giá với PriceList cho một item
    /// </summary>
    /// <param name="items">Item cần tính toán giá</param>
    /// <param name="userId">ID của user (có thể null)</param>
    /// <param name="branchId">ID của branch (có thể null)</param>
    /// <returns>Item với giá đã được điều chỉnh theo PriceList</returns>
    public async Task<Items> CalculatePriceItemWithPriceList(Items items, string? userId = null, string? branchId = null)
    {
        try
        {
            if (items == null) return items;

            var priceListFilter = new Resource.Dtos.InputDtos.PriceListFilterDto
            {
                ShopId = items.ShopId,
                Paging = Constants.MaxPaging
            };

            var priceLists = await _priceListRepository.GetAllAsync(priceListFilter);
            var activePriceLists = priceLists.Result.Where(p => p.IsActive).ToList();

            if (!activePriceLists.Any()) return items;

            items.Price = CalculateItemsAdjustedPrice(items.Price ?? 0, items, activePriceLists, userId, branchId);

            return items;
        }
        catch (Exception ex)
        {
            _log.Error($"Error calculating price with PriceList: {ex.Message}", ex);
            return items;
        }
    }

    /// <summary>
    /// Tính toán giá với PriceList cho một item
    /// </summary>
    /// <param name="itemsGroupBy">Item cần tính toán giá</param>
    /// <param name="userId">ID của user (có thể null)</param>
    /// <param name="branchId">ID của branch (có thể null)</param>
    /// <returns>Item với giá đã được điều chỉnh theo PriceList</returns>
    public async Task<ItemsGroupByDto> CalculatePriceWithPriceList(ItemsGroupByDto itemsGroupBy, string? shopId, string? userId = null, string? branchId = null)
    {
        try
        {
            if (itemsGroupBy == null) return itemsGroupBy;

            var rankId = "";
            if (!string.IsNullOrEmpty(userId))
            {
                var user = _userRepository.FindByUserId(userId ?? "");

                if (user != null)
                    rankId = user.MembershipLevelId ?? "";
            }
            else
                return null;

            List<PriceList> activePriceLists = await _priceListRepository.GetActivePriceListAsync(shopId, rankId);

            if (!activePriceLists.Any()) return itemsGroupBy;

            itemsGroupBy.Price = CalculateAdjustedPrice(itemsGroupBy.Price ?? 0, itemsGroupBy.ItemsId, activePriceLists, userId, branchId);

            if (itemsGroupBy.Price < 0)
                itemsGroupBy.Price = 0;

            if (itemsGroupBy.ListVariant != null && itemsGroupBy.ListVariant.Any())
            {
                foreach (var variant in itemsGroupBy.ListVariant)
                {
                    variant.Price = CalculateAdjustedPrice(variant.Price ?? 0, variant.ItemsId, activePriceLists, userId, branchId);

                    if (variant.Price < 0)
                        variant.Price = 0;
                }
            }

            return itemsGroupBy;
        }
        catch (Exception ex)
        {
            _log.Error($"Error calculating price with PriceList: {ex.Message}", ex);
            return itemsGroupBy;
        }
    }

    /// <summary>
    /// Tính toán giá với PriceList cho danh sách items
    /// </summary>
    /// <param name="itemsGroupByList">Danh sách items cần tính toán giá</param>
    /// <param name="userId">ID của user (có thể null)</param>
    /// <param name="branchId">ID của branch (có thể null)</param>
    /// <returns>Danh sách items với giá đã được điều chỉnh theo PriceList</returns>
    public async Task<List<ItemsGroupByDto>> CalculateListItemWithPriceList(List<ItemsGroupByDto> itemsGroupByList, string? userId = null, string? branchId = null)
    {
        try
        {
            if (itemsGroupByList == null || !itemsGroupByList.Any()) return itemsGroupByList;

            var shopGroups = itemsGroupByList.GroupBy(x => x.ShopId).ToList();
            var result = new List<ItemsGroupByDto>();

            foreach (var shopGroup in shopGroups)
            {
                var shopId = shopGroup.Key;
                var itemsInShop = shopGroup.ToList();

                var priceListFilter = new PriceListFilterDto
                {
                    ShopId = shopId,
                    Paging = Constants.MaxPaging
                };

                var rankId = "";
                if (!string.IsNullOrEmpty(userId))
                {
                    var user = _userRepository.FindByUserId(userId ?? "");

                    if (user != null)
                        rankId = user.MembershipLevelId ?? "";
                }
                else
                    return null;

                List<PriceList> activePriceLists = await _priceListRepository.GetActivePriceListAsync(shopId, rankId);

                if (!activePriceLists.Any())
                {
                    result.AddRange(itemsInShop);
                    continue;
                }

                foreach (var item in itemsInShop)
                {
                    item.Price = CalculateAdjustedPrice(item.Price ?? 0, item.ItemsId, activePriceLists, userId, branchId);

                    if (item.Price < 0)
                        item.Price = 0;

                    if (item.ListVariant != null && item.ListVariant.Any())
                    {
                        foreach (var variant in item.ListVariant)
                        {
                            variant.Price = CalculateAdjustedPrice(variant.Price ?? 0, variant.ItemsId, activePriceLists, userId, branchId);

                            if (variant.Price < 0)
                                variant.Price = 0;
                        }
                    }

                    result.Add(item);
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _log.Error($"Error calculating price with PriceList for list: {ex.Message}", ex);
            return itemsGroupByList;
        }
    }

    /// <summary>
    /// Tính toán giá đã được điều chỉnh dựa trên các PriceList áp dụng
    /// </summary>
    /// <param name="originalPrice">Giá gốc của item</param>
    /// <param name="item">Item cần tính toán</param>
    /// <param name="activePriceLists">Danh sách các PriceList đang hoạt động</param>
    /// <param name="userId">ID của user (có thể null)</param>
    /// <param name="branchId">ID của branch (có thể null)</param>
    /// <returns>Giá đã được điều chỉnh theo các PriceList</returns>
    private long CalculateAdjustedPrice(long originalPrice, string itemsId, List<PriceList> activePriceLists, string? userId, string? branchId)
    {
        try
        {
            if (originalPrice <= 0) return originalPrice;

            var adjustedPrice = originalPrice;
            var appliedPriceLists = new List<PriceList>();

            foreach (var priceList in activePriceLists)
            {
                if (IsPriceListApplicable(priceList, itemsId, userId, branchId))
                    appliedPriceLists.Add(priceList);
            }

            appliedPriceLists = appliedPriceLists.OrderBy(p => p.CreatedDate).ToList();

            foreach (var priceList in appliedPriceLists)
            {
                adjustedPrice = ApplyPriceListAdjustment(adjustedPrice, priceList);
            }

            return adjustedPrice;
        }
        catch (Exception ex)
        {
            _log.Error($"Error calculating adjusted price: {ex.Message}", ex);
            return originalPrice;
        }
    }

    /// <summary>
    /// Tính toán giá đã được điều chỉnh dựa trên các PriceList áp dụng
    /// </summary>
    /// <param name="originalPrice">Giá gốc của item</param>
    /// <param name="item">Item cần tính toán</param>
    /// <param name="activePriceLists">Danh sách các PriceList đang hoạt động</param>
    /// <param name="userId">ID của user (có thể null)</param>
    /// <param name="branchId">ID của branch (có thể null)</param>
    /// <returns>Giá đã được điều chỉnh theo các PriceList</returns>
    private long CalculateItemsAdjustedPrice(long originalPrice, Items item, List<PriceList> activePriceLists, string? userId, string? branchId)
    {
        try
        {
            if (originalPrice <= 0) return originalPrice;

            var adjustedPrice = originalPrice;
            var appliedPriceLists = new List<PriceList>();

            foreach (var priceList in activePriceLists)
            {
                if (IsPriceListApplicable(priceList, item, userId, branchId))
                    appliedPriceLists.Add(priceList);
            }

            appliedPriceLists = appliedPriceLists.OrderBy(p => p.CreatedDate).ToList();

            foreach (var priceList in appliedPriceLists)
            {
                adjustedPrice = ApplyPriceListAdjustment(adjustedPrice, priceList);
            }

            return adjustedPrice;
        }
        catch (Exception ex)
        {
            _log.Error($"Error calculating adjusted price: {ex.Message}", ex);
            return originalPrice;
        }
    }

    /// <summary>
    /// Kiểm tra xem PriceList có áp dụng được cho item, user và branch cụ thể hay không
    /// </summary>
    /// <param name="priceList">PriceList cần kiểm tra</param>
    /// <param name="itemsId">Item cần áp dụng PriceList</param>
    /// <param name="userId">ID của user (có thể null)</param>
    /// <param name="branchId">ID của branch (có thể null)</param>
    /// <returns>True nếu PriceList có thể áp dụng, False nếu không</returns>
    private bool IsPriceListApplicable(PriceList priceList, string itemsId, string? userId, string? branchId)
    {
        try
        {
            if (priceList.AppliedItemIds != null && priceList.AppliedItemIds.Any())
            {
                if (!priceList.AppliedItemIds.Contains(itemsId))
                    return false;
            }

            if (priceList.AppliedCategoryIds != null && priceList.AppliedCategoryIds.Any())
            {
                var item = _itemsRepository.FindByItemsId(itemsId);

                if (item == null)
                    return false;

                if (item.CategoryIds == null || !item.CategoryIds.Any(catId => priceList.AppliedCategoryIds.Contains(catId)))
                    return false;
            }

            if (priceList.AppliedBranchIds != null && priceList.AppliedBranchIds.Any())
            {
                if (string.IsNullOrEmpty(branchId) || !priceList.AppliedBranchIds.Contains(branchId))
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _log.Error($"Error checking price list applicability: {ex.Message}", ex);
            return false;
        }
    }

    /// <summary>
    /// Kiểm tra xem PriceList có áp dụng được cho item, user và branch cụ thể hay không
    /// </summary>
    /// <param name="priceList">PriceList cần kiểm tra</param>
    /// <param name="item">Item cần áp dụng PriceList</param>
    /// <param name="userId">ID của user (có thể null)</param>
    /// <param name="branchId">ID của branch (có thể null)</param>
    /// <returns>True nếu PriceList có thể áp dụng, False nếu không</returns>
    private bool IsPriceListApplicable(PriceList priceList, Items item, string? userId, string? branchId)
    {
        try
        {
            var user = _userRepository.FindByUserId(userId ?? "");

            if (user == null) return false;

            var rankId = user.MembershipLevelId ?? "";

            if (priceList.AppliedItemIds != null && priceList.AppliedItemIds.Any())
            {
                if (!priceList.AppliedItemIds.Contains(item.ItemsId))
                    return false;
            }

            if (priceList.AppliedCategoryIds != null && priceList.AppliedCategoryIds.Any())
            {
                if (item.CategoryIds == null || !item.CategoryIds.Any(catId => priceList.AppliedCategoryIds.Contains(catId)))
                    return false;
            }

            if (priceList.AppliedBranchIds != null && priceList.AppliedBranchIds.Any())
            {
                if (string.IsNullOrEmpty(branchId) || !priceList.AppliedBranchIds.Contains(branchId))
                    return false;
            }

            if (priceList.AppliedRankIds != null && priceList.AppliedRankIds.Any())
            {
                if (string.IsNullOrEmpty(rankId) || !priceList.AppliedRankIds.Contains(rankId))
                    return false;
            }

            if (priceList.AppliedUserIds != null && priceList.AppliedUserIds.Any())
            {
                if (string.IsNullOrEmpty(userId) || !priceList.AppliedUserIds.Contains(userId))
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _log.Error($"Error checking price list applicability: {ex.Message}", ex);
            return false;
        }
    }

    /// <summary>
    /// Áp dụng điều chỉnh giá theo PriceList
    /// </summary>
    /// <param name="currentPrice">Giá hiện tại</param>
    /// <param name="priceList">PriceList cần áp dụng</param>
    /// <returns>Giá sau khi áp dụng điều chỉnh</returns>
    private long ApplyPriceListAdjustment(long currentPrice, PriceList priceList)
    {
        try
        {
            if (currentPrice <= 0) return currentPrice;

            long newPrice = currentPrice;
            var adjustmentValue = priceList.AdjustmentValue;
            var adjustmentType = priceList.AdjustmentType;
            var adjustmentUnit = priceList.AdjustmentUnit;

            if (adjustmentUnit == AdjustmentUnitEnum.Percentage)
            {
                if (adjustmentType == AdjustmentTypeEnum.Increase)
                    newPrice = currentPrice + (long)(currentPrice * adjustmentValue / 100);
                else if (adjustmentType == AdjustmentTypeEnum.Decrease)
                    newPrice = currentPrice - (long)(currentPrice * adjustmentValue / 100);
            }
            else if (adjustmentUnit == AdjustmentUnitEnum.Currency)
            {
                if (adjustmentType == AdjustmentTypeEnum.Increase)
                    newPrice = currentPrice + (long)adjustmentValue;
                else if (adjustmentType == AdjustmentTypeEnum.Decrease)
                    newPrice = currentPrice - (long)adjustmentValue;
            }

            if (newPrice < 0)
                newPrice = 0;

            return newPrice;
        }
        catch (Exception ex)
        {
            _log.Error($"Error applying price list adjustment: {ex.Message}", ex);
            return currentPrice;
        }
    }

    /// <summary>
    /// Kiểm tra sản phẩm đã tồn tại trong bảng giá khác chưa
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="itemIds">Danh sách ID sản phẩm/dịch vụ cần kiểm tra</param>
    /// <param name="excludePriceListId">ID bảng giá cần loại trừ (dùng cho update)</param>
    /// <returns>Danh sách xung đột sản phẩm/dịch vụ</returns>
    public async Task<List<object>> ValidateItemConflicts(string shopId, List<string> itemIds, string? excludePriceListId = null)
    {
        try
        {
            var conflictingProducts = new List<object>();

            if (itemIds == null || !itemIds.Any())
                return conflictingProducts;

            foreach (var itemId in itemIds)
            {
                var existingPriceLists = await _priceListRepository.CheckItemExistsInPriceListAsync(shopId, itemId, excludePriceListId);
                if (existingPriceLists.Any())
                {
                    conflictingProducts.Add(new
                    {
                        ItemId = itemId,
                        Exists = true
                    });
                }
            }

            return conflictingProducts;
        }
        catch (Exception ex)
        {
            _log.Error($"Error validating product conflicts: {ex.Message}", ex);
            return new List<object>();
        }
    }

    /// <summary>
    /// Kiểm tra rank đã tồn tại trong bảng giá khác chưa
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="rankIds">Danh sách ID rank cần kiểm tra</param>
    /// <param name="excludePriceListId">ID bảng giá cần loại trừ (dùng cho update)</param>
    /// <returns>Danh sách xung đột rank</returns>
    public async Task<List<object>> ValidateRankConflicts(string shopId, List<string> rankIds, string? excludePriceListId = null)
    {
        try
        {
            var conflictingRanks = new List<object>();

            if (rankIds == null || !rankIds.Any())
                return conflictingRanks;

            foreach (var rankId in rankIds)
            {
                var existingPriceLists = await _priceListRepository.CheckRankExistsInPriceListAsync(shopId, rankId, excludePriceListId);
                if (existingPriceLists.Any())
                {
                    conflictingRanks.Add(new
                    {
                        RankId = rankId,
                        Exists = true
                    });
                }
            }

            return conflictingRanks;
        }
        catch (Exception ex)
        {
            _log.Error($"Error validating rank conflicts: {ex.Message}", ex);
            return new List<object>();
        }
    }

    public async Task<List<object>> ValidateItemQuantityTierConflicts(string shopId, List<string> itemIds, string? excludePriceListId = null)
    {
        try
        {
            var conflictingProducts = new List<object>();

            if (itemIds == null || !itemIds.Any())
                return conflictingProducts;

            foreach (var itemId in itemIds)
            {
                var existingPriceLists = await _priceListRepository.CheckItemExistsInQuantityTiersAsync(shopId, itemId, excludePriceListId);
                if (existingPriceLists.Any())
                {
                    conflictingProducts.Add(new
                    {
                        ItemId = itemId,
                        Exists = true
                    });
                }
            }

            return conflictingProducts;
        }
        catch (Exception ex)
        {
            _log.Error($"Error validating quantity tier conflicts: {ex.Message}", ex);
            return new List<object>();
        }
    }

    /// <summary>
    /// Lọc và tìm kiếm items theo filter
    /// </summary>
    /// <param name="items">Danh sách items cần lọc</param>
    /// <param name="filter">Filter để lọc items</param>
    /// <returns>Danh sách items đã được lọc</returns>
    private List<Items> FilterAndSearchItems(List<Items> items, PriceListItemsFilterDto filter)
    {
        if (!string.IsNullOrEmpty(filter.CategoryId))
            items = items.Where(x => x.CategoryIds != null && x.CategoryIds.Contains(filter.CategoryId)).ToList();

        if (filter.ItemsType.HasValue)
            items = items.Where(x => x.ItemsType == filter.ItemsType.Value).ToList();

        if (!string.IsNullOrEmpty(filter.Paging.Search))
        {
            var searchKey = filter.Paging.Search.NonUnicode().ToLower();
            items = items.Where(item =>
                (item.ItemsName ?? "").NonUnicode().ToLower().Contains(searchKey) ||
                (item.ItemsCode ?? "").NonUnicode().ToLower().Contains(searchKey) ||
                (item.VariantNameOne ?? "").NonUnicode().ToLower().Contains(searchKey) ||
                (item.VariantValueOne ?? "").NonUnicode().ToLower().Contains(searchKey)
            ).ToList();
        }

        return items;
    }

    /// <summary>
    /// Tạo ItemsPriceListDto từ item
    /// </summary>
    /// <param name="item">Item cần chuyển đổi</param>
    /// <param name="priceListId">ID của price list</param>
    /// <param name="adjustedPrice">Giá đã được điều chỉnh</param>
    /// <returns>ItemsPriceListDto</returns>
    private ItemsPriceListDto CreateItemsPriceListDto(Items item, string priceListId, long adjustedPrice)
    {
        List<MediaInfo>? images = null;
        if (item.Images != null)
        {
            images = new List<MediaInfo>();
            foreach (var image in item.Images)
            {
                images.Add(new MediaInfo
                {
                    MediaFileId = image.MediaFileId,
                    Type = image.Type,
                    Link = (image.Link.IndexOfList(["https://", "http://"]) >= 0 ? image.Link : S3Upload.GetUrlImage(image.Link))
                });
            }
        }
        return new ItemsPriceListDto
        {
            PriceListId = priceListId,
            ItemsId = item.ItemsId,
            ItemsCode = item.ItemsCode,
            CategoryIds = item.CategoryIds,
            Images = images,
            ItemsName = item.ItemsName,
            Price = item.Price ?? 0,
            AdjustedPrice = adjustedPrice,
            IsVariant = item.IsVariant,
            VariantImage = item.VariantImage == null ? null : new MediaInfo
            {
                MediaFileId = item.VariantImage.MediaFileId,
                Type = item.VariantImage.Type,
                Link = (item.VariantImage.Link.IndexOfList(["https://", "http://"]) >= 0 ? item.VariantImage.Link : S3Upload.GetUrlImage(item.VariantImage.Link))
            },
            VariantNameOne = item.VariantNameOne,
            VariantValueOne = item.VariantValueOne,
            VariantNameTwo = item.VariantNameTwo,
            VariantValueTwo = item.VariantValueTwo,
            VariantNameThree = item.VariantNameThree,
            VariantValueThree = item.VariantValueThree
        };
    }

    /// <summary>
    /// Xử lý phân trang cho danh sách items
    /// </summary>
    /// <param name="items">Danh sách items cần phân trang</param>
    /// <param name="filter">Filter chứa thông tin phân trang</param>
    /// <returns>Danh sách items đã được phân trang</returns>
    private List<Items> ApplyPaging(List<Items> items, PriceListItemsFilterDto filter)
    {
        return items
            .Skip(filter.Paging.PageIndex * filter.Paging.PageSize)
            .Take(filter.Paging.PageSize)
            .ToList();
    }

    public async Task<PagingResult<ItemsPriceListDto>> GetItemsByPriceListId(PriceListItemsFilterDto filter)
    {
        var result = new PagingResult<ItemsPriceListDto> { Result = new List<ItemsPriceListDto>(), Total = 0 };
        var priceList = await _priceListRepository.GetByIdAsync(filter.ShopId, filter.PriceListId);
        if (priceList == null) return result;

        var itemIds = priceList.AppliedItemIds ?? new List<string>();
        if (!itemIds.Any()) return result;

        result.Total = itemIds.Count;

        var pagedItemIds = itemIds.Skip(filter.Paging.PageIndex * filter.Paging.PageSize).Take(filter.Paging.PageSize).ToList();
        if (!pagedItemIds.Any()) return result;

        var items = _itemsRepository.FindByItemsIds(string.Join(",", pagedItemIds));
        items = FilterAndSearchItems(items, filter);

        if (items == null || !items.Any())
            result.Total = 0;

        if (itemIds.Count < filter.Paging.PageSize && items.Count < itemIds.Count)
            result.Total = items.Count;

        foreach (var item in items)
        {
            var adjustedPrice = ApplyPriceListAdjustment(item.Price ?? 0, priceList);
            result.Result.Add(CreateItemsPriceListDto(item, filter.PriceListId, adjustedPrice));
        }

        return result;
    }

    public async Task<PagingResult<ItemsPriceListDto>> GetItemsNotInAnyPriceList(PriceListItemsFilterDto filter)
    {
        var result = new PagingResult<ItemsPriceListDto> { Result = new List<ItemsPriceListDto>(), Total = 0 };

        var priceLists = await _priceListRepository.GetByIdAsync(filter.ShopId, filter.PriceListId);
        if (priceLists == null) return result;

        var allItemIdsInPriceList = priceLists.AppliedItemIds ?? new List<string>();

        var allItemsPaging = _itemsRepository.ListItems(Constants.MaxPaging, shopId: filter.ShopId);
        var allItems = allItemsPaging.Result;

        var itemsNotInAnyPriceList = allItems
            .Where(item => !allItemIdsInPriceList.Contains(item.ItemsId))
            .ToList();

        itemsNotInAnyPriceList = FilterAndSearchItems(itemsNotInAnyPriceList, filter);
        result.Total = itemsNotInAnyPriceList.Count;

        var pagedItems = ApplyPaging(itemsNotInAnyPriceList, filter);

        foreach (var item in pagedItems)
        {
            result.Result.Add(CreateItemsPriceListDto(item, filter.PriceListId, item.Price ?? 0));
        }

        return result;
    }

    public async Task<PagingResult<ItemQuantityTierDto>> GetItemQuantityTiers(PriceListItemsFilterDto filter)
    {
        var result = new PagingResult<ItemQuantityTierDto> { Result = new List<ItemQuantityTierDto>(), Total = 0 };
        var priceList = await _priceListRepository.GetByIdAsync(filter.ShopId, filter.PriceListId);
        if (priceList == null || priceList.ItemQuantityTiers == null || !priceList.ItemQuantityTiers.Any())
            return result;

        var itemIds = priceList.ItemQuantityTiers.Select(x => x.ItemId).ToList();
        var items = _itemsRepository.FindByItemsIds(string.Join(",", itemIds));
        var itemsDict = items.ToDictionary(x => x.ItemsId, x => x.ItemsName);

        var filteredTiers = priceList.ItemQuantityTiers;
        if (!string.IsNullOrEmpty(filter.Paging.Search))
        {
            var searchKey = filter.Paging.Search.NonUnicode().ToLower();
            filteredTiers = filteredTiers
                .Where(tier => (itemsDict.ContainsKey(tier.ItemId) ? (itemsDict[tier.ItemId] ?? "").NonUnicode().ToLower().Contains(searchKey) : false))
                .ToList();
        }
        result.Total = filteredTiers.Count;
        var pagedTiers = filteredTiers
            .Skip(filter.Paging.PageIndex * filter.Paging.PageSize)
            .Take(filter.Paging.PageSize)
            .ToList();

        foreach (var tier in pagedTiers)
        {
            result.Result.Add(new ItemQuantityTierDto
            {
                ItemsId = tier.ItemId,
                ItemsName = itemsDict.ContainsKey(tier.ItemId) ? itemsDict[tier.ItemId] : null,
                QuantityTiers = tier.QuantityTiers
            });
        }
        return result;
    }

    public async Task<PagingResult<MembershipLevel>> GetRanksNotInAnyPriceList(PriceListItemsFilterDto filter)
    {
        var result = new PagingResult<MembershipLevel> { Result = new List<MembershipLevel>(), Total = 0 };

        var priceListFilter = new PriceListFilterDto
        {
            ShopId = filter.ShopId,
            Paging = Constants.MaxPaging
        };

        var priceLists = await _priceListRepository.GetAllAsync(priceListFilter);
        if (priceLists == null || !priceLists.Result.Any())
            return result;

        var allRankIdsInPriceList = priceLists.Result.SelectMany(x => x.AppliedRankIds ?? new List<string>()).ToList();

        var allItemsPaging = _membershipLevelRepository.ListMembershipShop(filter.Paging, shopId: filter.ShopId);
        var allItems = allItemsPaging.Result;

        var ranksNotInAnyPriceList = allItems
            .Where(item => !allRankIdsInPriceList.Contains(item.LevelId))
            .ToList();

        result.Total = ranksNotInAnyPriceList.Count;

        var pagedItems = ranksNotInAnyPriceList
            .Skip(filter.Paging.PageIndex * filter.Paging.PageSize)
            .Take(filter.Paging.PageSize)
            .ToList();

        foreach (MembershipLevel item in pagedItems)
        {
            result.Result.Add(item);
        }

        return result;
    }
}
