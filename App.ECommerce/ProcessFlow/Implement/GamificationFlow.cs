using App.ECommerce.Helpers;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Interface;
using log4net;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Security.Cryptography;
using App.ECommerce.Repository.Entities;
using MongoDB.Bson;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Localization;
using App.ECommerce.Resource.Dtos.GamificationDtos;
using App.ECommerce.Units;

namespace App.ECommerce.ProcessFlow.Implement
{
    public class GamificationFlow : IGamificationFlow
    {
        private readonly string _promogameApiUrl;
        private readonly string _dashboardUrl;
        private readonly string _domainWebhook;
        private readonly IGamificationRepository _gamificationRepository;
        private readonly IGamificationShopRepository _gamificationShopRepository;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IShopRepository _shopRepository;
        private readonly ILog _log = LogManager.GetLogger(typeof(GamificationFlow));
        private readonly IStringLocalizer _localizer;
        private readonly IShopSettingRepository _shopSettingRepository;
        private readonly IPrizeRepository _prizeRepository;

        public GamificationFlow(
            IGamificationRepository gamificationRepository,
            IHttpClientFactory httpClientFactory,
            IShopRepository shopRepository,
            IGamificationShopRepository gamificationShopRepository,
            IShopSettingRepository shopSettingRepository,
            IStringLocalizer localizer,
            IConfiguration configuration,
            IPrizeRepository prizeRepository)
        {
            _gamificationRepository = gamificationRepository;
            _httpClientFactory = httpClientFactory;
            _shopRepository = shopRepository;
            _gamificationShopRepository = gamificationShopRepository;
            _shopSettingRepository = shopSettingRepository;
            _localizer = localizer;
            _promogameApiUrl = configuration["Promogame:ApiUrl"];
            _dashboardUrl = configuration["Promogame:DashboardUrl"];
            _domainWebhook = configuration["Promogame:DomainWebhook"];
            _prizeRepository = prizeRepository;
        }

        private async Task<TResponseData> ExecuteGraphQLRequestAsync<TVariables, TResponseData>(
            string queryOrMutation,
            TVariables variables,
            string accessToken = null,
            bool isRetry = false)
        {
            var requestBody = new PromogameGraphQLRequest<TVariables>
            {
                Query = queryOrMutation,
                Variables = variables
            };

            using var client = _httpClientFactory.CreateClient("PromogameClient");
            client.DefaultRequestHeaders.UserAgent.ParseAdd("ECommerceBackend/1.0");

            if (!string.IsNullOrEmpty(accessToken))
            {
                client.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            }

            var serializerOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };
            var requestJson = JsonSerializer.Serialize(requestBody, serializerOptions);
            var content = new StringContent(requestJson, Encoding.UTF8, "application/json");

            _log.Debug($"Promogame API Request to {_promogameApiUrl}. Body: {requestJson}");
            HttpResponseMessage responseMessage;
            try
            {
                responseMessage = await client.PostAsync(_promogameApiUrl, content);
            }
            catch (HttpRequestException ex)
            {
                _log.Error($"HTTP request to Promogame API failed. URL: {_promogameApiUrl}", ex);
                throw new Exception("Failed to connect to gamification service.", ex);
            }

            var responseContent = await responseMessage.Content.ReadAsStringAsync();
            _log.Debug($"Promogame API Response: {responseContent}");

            if (!responseMessage.IsSuccessStatusCode)
            {
                _log.Error($"Failed GraphQL request. Status: {responseMessage.StatusCode}, URL: {_promogameApiUrl}, Details: {responseContent}");
                throw new Exception($"Failed GraphQL request. Status: {responseMessage.StatusCode}, Details: {responseContent}");
            }

            var gqlResponse = JsonSerializer.Deserialize<PromogameGraphQLResponse<TResponseData>>(responseContent,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true, Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) } });

            if (gqlResponse?.Errors != null && gqlResponse.Errors.Any())
            {
                var errorMessages = string.Join("; ", gqlResponse.Errors.Select(e => e.Message));
                _log.Error($"GraphQL error: {errorMessages}. Query: {queryOrMutation}");

                // Check if token is expired and this is not a retry attempt
                if (!isRetry && IsTokenExpiredError(errorMessages))
                {
                    _log.Info("Token expired, attempting to refresh and retry...");
                    var refreshedToken = await RefreshTokenAndRetryAsync();
                    if (!string.IsNullOrEmpty(refreshedToken))
                    {
                        return await ExecuteGraphQLRequestAsync<TVariables, TResponseData>(queryOrMutation, variables, refreshedToken, true);
                    }
                }

                throw new Exception($"GraphQL error(s) from Promogame: {errorMessages}");
            }
            return gqlResponse.Data;
        }

        private bool IsTokenExpiredError(string errorMessage)
        {
            var expiredKeywords = new[] { "token", "expired", "unauthorized", "authentication", "invalid token" };
            return expiredKeywords.Any(keyword => errorMessage.ToLowerInvariant().Contains(keyword));
        }

        private async Task<string> RefreshTokenAndRetryAsync()
        {
            try
            {
                var settings = await _gamificationRepository.GetSettingsAsync();
                if (settings == null)
                {
                    _log.Error("Gamification settings not found for token refresh");
                    return null;
                }

                _log.Info($"Refreshing token for user: {settings.Username}");
                var loginData = await LoginAsync(settings.Username, settings.Password);

                if (loginData == null || string.IsNullOrEmpty(loginData.AccessToken))
                {
                    _log.Error("Failed to refresh token - login returned null or empty token");
                    return null;
                }

                // Update settings with new tokens
                settings.AccessToken = loginData.AccessToken;
                settings.RefreshToken = loginData.RefreshToken;
                await _gamificationRepository.UpdateSettingsAsync(settings);

                _log.Info("Token refreshed successfully");
                return loginData.AccessToken;
            }
            catch (Exception ex)
            {
                _log.Error("Failed to refresh token", ex);
                return null;
            }
        }

        private async Task<TResponseData> ExecuteGraphQLRequestWithTokenRefreshAsync<TVariables, TResponseData>(
            Gamification settings,
            string queryOrMutation,
            TVariables variables,
            Func<string, Task<TResponseData>> executeWithToken)
        {
            try
            {
                return await executeWithToken(settings.AccessToken);
            }
            catch (Exception ex) when (IsTokenExpiredError(ex.Message))
            {
                _log.Info("Token expired, attempting to refresh and retry...");
                var refreshedToken = await RefreshTokenAndRetryAsync();
                if (!string.IsNullOrEmpty(refreshedToken))
                {
                    return await executeWithToken(refreshedToken);
                }
                throw;
            }
        }

        private async Task<PromogameTokenDto> LoginAsync(string username, string password)
        {
            var mutation = @"
                mutation ($username: String!, $password: String!){
                    login( input: { username: $username, password: $password }) {
                        accessToken
                        refreshToken
                    }
                }
            ";
            var variables = new PromogameLoginRequestDto
            {
                Username = username,
                Password = password
            };
            var loginResponseData = await ExecuteGraphQLRequestAsync<PromogameLoginRequestDto, PromogameLoginResponseDto>(mutation, variables);
            return loginResponseData?.Login;
        }

        private async Task<Gamification> EnsureValidGamificationTokenAsync(string shopId)
        {
            var settings = await _gamificationRepository.GetSettingsAsync();

            var loginData = await LoginAsync(settings.Username, settings.Password);
            if (loginData == null || string.IsNullOrEmpty(loginData.AccessToken) || string.IsNullOrEmpty(loginData.RefreshToken))
            {
                _log.Error($"Incomplete login data received from gamification service for shopId {shopId}.");
                throw new Exception("Incomplete login data received from gamification service.");
            }
            settings.AccessToken = loginData.AccessToken;
            settings.RefreshToken = loginData.RefreshToken;
            await _gamificationRepository.UpdateSettingsAsync(settings);
            return settings;
        }

        public async Task<PrizeResponseDto> CreatePrizeAsync(CreatePrizeInputDto input)
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return null;
            }

            // 1. Tạo Prize ở DB mình trước
            var prize = new Prize
            {
                Id = Guid.NewGuid(),
                PrizeGameId = Guid.NewGuid().ToString(),
                Name = input.Name,
                Type = input.Type,
                AvatarLink = null,
                Quantity = input.Quantity,
                Stock = input.Stock,
                ExternalPrizeId = input.ExternalPrizeId,
                Category = input.Category
            };
            await _prizeRepository.InsertAsync(prize);

            // 3. Gọi API bên thứ 3, truyền externalPrizeId = myPrize.PrizeId
            var mutation = @"
                mutation CreatePrize(
                    $name: String!,
                    $avatar: Upload!,
                    $quantity: Int!,
                    $campaignId: String!,
                    $type: PrizeType!,
                    $stock: Int,
                    $externalPrizeId: String
                ){
                  createPrize(
                    input: {
                      name: $name
                      avatar: { file: $avatar }
                      quantity: $quantity
                      campaignId: $campaignId
                      type: $type
                      stock: $stock
                      externalPrizeId: $externalPrizeId
                    }
                  ) {
                    id
                    name
                    type
                    avatarLink
                    quantity
                    stock
                    externalPrizeId
                  }
                }
            ";
            var operationsVariables = new
            {
                name = input.Name,
                avatar = (object)null,
                quantity = input.Quantity,
                campaignId = input.CampaignId,
                type = input.Type.ToString().ToUpperInvariant(),
                stock = input.Stock,
                externalPrizeId = prize.PrizeGameId
            };
            var operationsObject = new
            {
                Query = mutation,
                Variables = operationsVariables
            };
            var requestSerializerOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };
            var operationsJson = JsonSerializer.Serialize(operationsObject, requestSerializerOptions);
            var mapJson = "{\"0\": [\"variables.avatar\"]}";
            using var client = _httpClientFactory.CreateClient("PromogameClient");
            client.DefaultRequestHeaders.UserAgent.ParseAdd("ECommerceBackend/1.0");
            if (!string.IsNullOrEmpty(settings.AccessToken))
            {
                client.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", settings.AccessToken);
            }
            client.DefaultRequestHeaders.Add("X-Apollo-Operation-Name", "CreatePrize");
            using var multipartContent = new MultipartFormDataContent();
            multipartContent.Add(new StringContent(operationsJson, Encoding.UTF8, "application/json"), "operations");
            multipartContent.Add(new StringContent(mapJson, Encoding.UTF8, "application/json"), "map");
            if (input.Avatar != null && input.Avatar.Length > 0)
            {
                multipartContent.Add(new StreamContent(input.Avatar.OpenReadStream()), "0", input.Avatar.FileName);
            }
            _log.Debug($"Promogame API Request (Multipart) to {_promogameApiUrl}. Operations: {operationsJson}, Map: {mapJson}");
            HttpResponseMessage responseMessage;
            try
            {
                responseMessage = await client.PostAsync(_promogameApiUrl, multipartContent);
            }
            catch (HttpRequestException ex)
            {
                _log.Error($"HTTP request to Promogame API failed. URL: {_promogameApiUrl}", ex);
                throw new Exception("Failed to connect to gamification service.", ex);
            }
            var responseContent = await responseMessage.Content.ReadAsStringAsync();
            _log.Debug($"Promogame API Response: {responseContent}");
            if (!responseMessage.IsSuccessStatusCode)
            {
                _log.Error($"Failed GraphQL request. Status: {responseMessage.StatusCode}, URL: {_promogameApiUrl}, Details: {responseContent}");
                throw new Exception($"Failed GraphQL request. Status: {responseMessage.StatusCode}, Details: {responseContent}");
            }
            var responseDeserializerOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };
            var gqlResponse = JsonSerializer.Deserialize<PromogameGraphQLResponse<CreatePrizeResponseDto>>(responseContent, responseDeserializerOptions);
            if (gqlResponse?.Errors != null && gqlResponse.Errors.Any())
            {
                var errorMessages = string.Join("; ", gqlResponse.Errors.Select(e => e.Message));
                _log.Error($"GraphQL error: {errorMessages}. Query: {mutation}");
                throw new Exception($"GraphQL error(s) from Promogame: {errorMessages}");
            }
            // 4. Sau khi thành công, cập nhật lại các trường còn lại vào Prize bên mình
            if (gqlResponse?.Data?.CreatePrize != null)
            {
                prize.AvatarLink = gqlResponse.Data.CreatePrize.AvatarLink;
                prize.PrizeId = gqlResponse.Data.CreatePrize.Id;
                await _prizeRepository.UpdateAsync(prize);
            }
            return gqlResponse.Data.CreatePrize;
        }

        public async Task<PrizeResponseDto> UpdatePrizeAsync(UpdatePrizeInputDto input)
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found for ShopId: " + input.ShopId);
                return null;
            }
            var mutation = @"
                mutation UpdatePrize($id: String!, $name: String!, $quantity: Int!, $stock: Int){
                    updatePrize(input: { id: $id, name: $name, quantity: $quantity, stock: $stock}) {
                        id
                        name
                        type
                        avatarLink
                        quantity
                        stock
                        externalPrizeId
                    }
                }
            ";
            var variables = new
            {
                id = input.Id,
                name = input.Name,
                quantity = input.Quantity,
                stock = input.Stock
            };
            var responseWrapper = await ExecuteGraphQLRequestAsync<object, UpdatePrizeResponseWrapperDto>(mutation, variables, settings.AccessToken);
            var updatedPrize = responseWrapper.UpdatePrize;
            if (updatedPrize != null)
            {
                var prize = await _prizeRepository.GetByPrizeIdAsync(updatedPrize.Id);
                prize.Name = updatedPrize.Name;
                prize.Quantity = updatedPrize.Quantity;
                await _prizeRepository.UpdateByPrizeIdAsync(prize);
            }
            return updatedPrize;
        }

        public async Task<List<PrizeResponseDto>> GetAllPrizesAsync(string campaignId)
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return null;
            }
            var query = @"
                query GetAllPrizes($campaignId: String!){
                    getAllPrizes(input: {campaignId: $campaignId}){
                        id
                        name
                        type
                        avatarLink
                        quantity
                        stock
                        externalPrizeId
                    }
                }
            ";
            var variables = new { campaignId = campaignId };

            var responseWrapper = await ExecuteGraphQLRequestAsync<object, GetAllPrizesResponseWrapperDto>(query, variables, settings.AccessToken);
            var prizes = responseWrapper.GetAllPrizes;

            // Lấy thông tin category từ database bên mình và merge vào response
            if (prizes != null && prizes.Any())
            {
                var localPrizes = await _prizeRepository.GetAllAsync();
                foreach (var prize in prizes)
                {
                    var localPrize = localPrizes.FirstOrDefault(p => p.PrizeGameId == prize.ExternalPrizeId);
                    if (localPrize != null)
                    {
                        prize.Category = localPrize.Category;
                        prize.ExternalPrizeId = localPrize.ExternalPrizeId;
                    }
                }
            }

            return prizes;
        }

        public async Task<GameSessionResponseDto> StartGameSessionAsync(string shopId, User user)
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return new GameSessionResponseDto
                {
                    IsAvailable = false,
                    Status = "ERROR",
                    Message = "Gamification settings not found"
                };
            }
            var shopSetting = await _gamificationShopRepository.GetGameBrandIdByShopId(shopId);
            if (shopSetting == null)
            {
                _log.Error("Gamification settings not found");
                return new GameSessionResponseDto
                {
                    IsAvailable = false,
                    Status = "ERROR",
                    Message = "Shop gamification settings not found"
                };
            }

            var checksum = "";
            var requestedAt = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
            string thumbnailLink = null;
            DateTime? startTime = null;
            DateTime? endTime = null;

            if (!string.IsNullOrEmpty(shopSetting.CampaignId) && shopSetting.IsActived)
            {
                try
                {
                    var campaignQuery = @"
                        query GetCampaign($id: String!, $zaloAppId: String) {
                            getCampaign(id: $id, zaloAppId: $zaloAppId) {
                                id
                                name
                                thumbnailLink
                                startTime
                                endTime
                                externalCampaignSecret
                            }
                        }
                    ";
                    var campaignVariables = new { id = shopSetting.CampaignId };
                    var campaignResponse = await ExecuteGraphQLRequestAsync<object, GetCampaignResponseWrapperDto>(
                        campaignQuery, campaignVariables, settings.AccessToken);

                    if (campaignResponse?.GetCampaign != null)
                    {
                        var startTimeStr = campaignResponse.GetCampaign.StartTime;
                        var endTimeStr = campaignResponse.GetCampaign.EndTime;
                        if (!string.IsNullOrEmpty(startTimeStr) && !string.IsNullOrEmpty(endTimeStr))
                        {
                            startTime = DateTime.Parse(startTimeStr);
                            endTime = DateTime.Parse(endTimeStr);
                            var now = DateTimes.Now();

                            if (now < startTime)
                            {
                                return new GameSessionResponseDto
                                {
                                    CampaignId = shopSetting.CampaignId,
                                    ApiUrl = _promogameApiUrl,
                                    ThumbnailLink = campaignResponse.GetCampaign.ThumbnailLink,
                                    RequestedAt = requestedAt,
                                    IsAvailable = false,
                                    Status = "NOT_STARTED",
                                    Message = "Sự kiện chưa bắt đầu",
                                    StartTime = startTime,
                                    EndTime = endTime
                                };
                            }

                            if (now > endTime)
                            {
                                return new GameSessionResponseDto
                                {
                                    CampaignId = shopSetting.CampaignId,
                                    ApiUrl = _promogameApiUrl,
                                    ThumbnailLink = campaignResponse.GetCampaign.ThumbnailLink,
                                    RequestedAt = requestedAt,
                                    IsAvailable = false,
                                    Status = "ENDED",
                                    Message = "Sự kiện đã kết thúc",
                                    StartTime = startTime,
                                    EndTime = endTime
                                };
                            }
                        }
                        var checksumInput = $"{user.UserId}:{campaignResponse.GetCampaign.ExternalCampaignSecret}:{requestedAt}";
                        checksum = ConvertToMD5(checksumInput);
                        thumbnailLink = campaignResponse.GetCampaign.ThumbnailLink;
                    }
                }
                catch (Exception ex)
                {
                    _log.Warn($"Failed to fetch campaign thumbnail for campaignId {shopSetting.CampaignId}: {ex.Message}");
                }
            }
            else
            {
                return new GameSessionResponseDto
                {
                    CampaignId = null,
                    ApiUrl = _promogameApiUrl,
                    RequestedAt = requestedAt,
                    IsAvailable = false,
                    Status = "NO_CAMPAIGN",
                    Message = "Chưa có sự kiện game"
                };
            }

            return new GameSessionResponseDto
            {
                CampaignId = shopSetting.CampaignId,
                ApiUrl = _promogameApiUrl,
                Checksum = checksum,
                ThumbnailLink = thumbnailLink,
                RequestedAt = requestedAt,
                IsAvailable = true,
                Status = "AVAILABLE",
                Message = "Game session is available",
                StartTime = startTime,
                EndTime = endTime
            };
        }

        private static string ConvertToMD5(string input)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);
                return Convert.ToHexString(hashBytes).ToLower();
            }
        }
        public async Task<BrandResponseDataDto> CreateGameBrand(string shopId)
        {
            var settings = await _gamificationRepository.GetSettingsAsync() ?? await EnsureValidGamificationTokenAsync(shopId);
            var shopSetting = await _gamificationShopRepository.GetGameBrandIdByShopId(shopId);
            Shop shop = _shopRepository.FindByShopId(shopId);

            if (shop == null)
                throw new Exception(_localizer["SHOP_NOT_FOUND"]);

            // If shopSetting exists, update the brand
            if (shopSetting != null)
            {
                var mutation = @"
                    mutation UpdateBrand($externalBrandId: String!, $brandId: String!, $zaloOaId: String!){
                        updateBrand(input: { id: $brandId, externalBrandId: $externalBrandId, zaloOaId: $zaloOaId}) {
                            id
                            name
                            avatarLink
                            externalBrandId
                            zaloOaId
                        }
                    }
                ";
                var variables = new
                {
                    externalBrandId = shopId,
                    brandId = shopSetting.GameBrandId,
                    zaloOaId = shop.OaId
                };

                return await ExecuteGraphQLRequestWithTokenRefreshAsync(settings, mutation, variables, async (token) =>
                {
                    var responseWrapper = await ExecuteGraphQLRequestAsync<object, UpdateBrandResponseWrapperDto>(mutation, variables, token);
                    return responseWrapper.UpdateBrand;
                });
            }

            // If shopSetting doesn't exist, create a new brand
            return await ExecuteMultipartRequestWithTokenRefreshAsync(settings, async (token) =>
            {
                var mutation = @"
                mutation CreateBrand($name: String!, $externalBrandId: String!, $avatar: Upload!, $zaloOaId: String!){
                    createBrand(
                        input: 
                        { 
                            name: $name, 
                            avatar: {file: $avatar}
                            externalBrandId: $externalBrandId
                            zaloOaId: $zaloOaId}) 
                            {
                                id
                                name
                                avatarLink
                                externalBrandId
                                zaloOaId
                            }
                        }
                ";
                var cleanShopId = shopId.Replace("-", "");
                var shortShopId = cleanShopId.Length > 4 ? cleanShopId.Substring(0, 4) : cleanShopId;
                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                var randomSuffix = Guid.NewGuid().ToString("N").Substring(0, 4);
                var name = $"shop{shortShopId}{timestamp}{randomSuffix}";
                var operationsVariables = new
                {
                    name = name,
                    externalBrandId = shopId,
                    avatar = (object)null,
                    zaloOaId = shop.OaId
                };

                var operationsObject = new
                {
                    Query = mutation,
                    Variables = operationsVariables
                };

                var requestSerializerOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                    Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
                };
                var operationsJson = JsonSerializer.Serialize(operationsObject, requestSerializerOptions);
                var mapJson = "{\"0\": [\"variables.avatar\"]}";

                using var client = _httpClientFactory.CreateClient("PromogameClient");
                client.DefaultRequestHeaders.UserAgent.ParseAdd("ECommerceBackend/1.0");

                if (!string.IsNullOrEmpty(token))
                {
                    client.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                }
                client.DefaultRequestHeaders.Add("X-Apollo-Operation-Name", "CreateBrand");

                using var multipartContent = new MultipartFormDataContent();
                multipartContent.Add(new StringContent(operationsJson, Encoding.UTF8, "application/json"), "operations");
                multipartContent.Add(new StringContent(mapJson, Encoding.UTF8, "application/json"), "map");

                // Create a fake avatar file (1x1 pixel transparent PNG)
                var fakeAvatarBytes = Convert.FromBase64String("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
                multipartContent.Add(new ByteArrayContent(fakeAvatarBytes), "0", "default-avatar.png");

                _log.Debug($"Promogame API Request (Multipart) to {_promogameApiUrl}. Operations: {operationsJson}, Map: {mapJson}");
                HttpResponseMessage responseMessage;
                try
                {
                    responseMessage = await client.PostAsync(_promogameApiUrl, multipartContent);
                }
                catch (HttpRequestException ex)
                {
                    _log.Error($"HTTP request to Promogame API failed. URL: {_promogameApiUrl}", ex);
                    throw new Exception("Failed to connect to gamification service.", ex);
                }

                var responseContent = await responseMessage.Content.ReadAsStringAsync();
                _log.Debug($"Promogame API Response: {responseContent}");

                if (!responseMessage.IsSuccessStatusCode)
                {
                    _log.Error($"Failed GraphQL request. Status: {responseMessage.StatusCode}, URL: {_promogameApiUrl}, Details: {responseContent}");
                    throw new Exception($"Failed GraphQL request. Status: {responseMessage.StatusCode}, Details: {responseContent}");
                }

                var responseDeserializerOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
                };
                var gqlResponse = JsonSerializer.Deserialize<PromogameGraphQLResponse<CreateBrandResponseWrapperDto>>(responseContent, responseDeserializerOptions);

                if (gqlResponse?.Errors != null && gqlResponse.Errors.Any())
                {
                    var errorMessages = string.Join("; ", gqlResponse.Errors.Select(e => e.Message));
                    _log.Error($"GraphQL error: {errorMessages}. Query: {mutation}");
                    throw new Exception($"GraphQL error(s) from Promogame: {errorMessages}");
                }

                var gamificationShop = new GamificationShop
                {
                    ShopId = shopId,
                    GameBrandId = gqlResponse.Data.CreateBrand.Id
                };
                await _gamificationShopRepository.Insert(gamificationShop);
                return gqlResponse.Data.CreateBrand;
            });
        }

        private async Task<T> ExecuteMultipartRequestWithTokenRefreshAsync<T>(Gamification settings, Func<string, Task<T>> executeWithToken)
        {
            try
            {
                return await executeWithToken(settings.AccessToken);
            }
            catch (Exception ex) when (IsTokenExpiredError(ex.Message))
            {
                _log.Info("Token expired, attempting to refresh and retry...");
                var refreshedToken = await RefreshTokenAndRetryAsync();
                if (!string.IsNullOrEmpty(refreshedToken))
                {
                    return await executeWithToken(refreshedToken);
                }
                throw;
            }
        }

        public async Task<string> CreateCampaign(GameCampaignDto gameCampaign)
        {
            var settings = await _gamificationRepository.GetSettingsAsync() ?? await EnsureValidGamificationTokenAsync(gameCampaign.ShopId);
            var shopSetting = await _gamificationShopRepository.GetGameBrandIdByShopId(gameCampaign.ShopId);
            if (shopSetting == null)
            {
                _log.Error("Gamification settings not found");
                return null;
            }
            var shop = _shopRepository.FindByShopId(gameCampaign.ShopId);
            if (shop == null)
            {
                _log.Error("Shop not found");
                return null;
            }
            string env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
            bool isProduction = env.Equals("Production", StringComparison.OrdinalIgnoreCase);
            var cleanShopId = shop.ShopId.Replace("-", "");
            var shortShopId = cleanShopId.Length > 4 ? cleanShopId.Substring(0, 4) : cleanShopId;
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var randomSuffix = Guid.NewGuid().ToString("N").Substring(0, 4);
            var campaign = $"campaign{shortShopId}{timestamp}{randomSuffix}";
            return await ExecuteMultipartRequestWithTokenRefreshAsync(settings, async (token) =>
            {
                var mutation = @"
                        mutation CreateCampaign($name: String!, $gameId: String!, $brandId: String!, $thumbnail: Upload!, 
                        $startTime: DateTime!, $endTime: DateTime!, $externalGiftLink: String!, $ingestEventUrl: String!, $externalCampaignId: String!) 
                        {createCampaign(input: { name: $name, thumbnail: { file: $thumbnail }, 
                        gameId: $gameId, stackScore: true, initialTickets: 1, dailyTickets: 1, authenticatePhase: AUTHENTICATE_PHASE_BEFORE_PLAY, 
                        brandId: $brandId, allowEnterTicketCode: false, gamePrizeEnable: true, freeToPlay: false, leaderboardEnable: true, 
                        parameters: {}, startTime: $startTime, endTime: $endTime, externalGiftLink: $externalGiftLink, 
                        ingestEventUrl: $ingestEventUrl, externalCampaignId: $externalCampaignId }) { id } }
                    ";

                var operationsVariables = new
                {
                    name = gameCampaign.Name,
                    thumbnail = (object)null,
                    gameId = gameCampaign.GameId,
                    brandId = shopSetting.GameBrandId,
                    startTime = gameCampaign.StartTime.ToString("yyyy-MM-ddTHH:mm:ss"),
                    endTime = gameCampaign.EndTime.ToString("yyyy-MM-ddTHH:mm:ss"),
                    externalGiftLink = "gift_event",
                    ingestEventUrl = _domainWebhook,
                    externalCampaignId = campaign
                };

                var operationsObject = new
                {
                    Query = mutation,
                    Variables = operationsVariables
                };

                var requestSerializerOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                    Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
                };
                var operationsJson = JsonSerializer.Serialize(operationsObject, requestSerializerOptions);
                var mapJson = "{\"0\": [\"variables.thumbnail\"]}";

                using var client = _httpClientFactory.CreateClient("PromogameClient");
                client.DefaultRequestHeaders.UserAgent.ParseAdd("ECommerceBackend/1.0");

                if (!string.IsNullOrEmpty(token))
                {
                    client.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                }
                client.DefaultRequestHeaders.Add("X-Apollo-Operation-Name", "CreateCampaign");

                using var multipartContent = new MultipartFormDataContent();
                multipartContent.Add(new StringContent(operationsJson, Encoding.UTF8, "application/json"), "operations");
                multipartContent.Add(new StringContent(mapJson, Encoding.UTF8, "application/json"), "map");

                if (gameCampaign.Thumbnail != null && gameCampaign.Thumbnail != null && gameCampaign.Thumbnail.Length > 0)
                {
                    multipartContent.Add(new StreamContent(gameCampaign.Thumbnail.OpenReadStream()), "0", gameCampaign.Thumbnail.FileName);
                }

                _log.Debug($"Promogame API Request (Multipart) to {_promogameApiUrl}. Operations: {operationsJson}, Map: {mapJson}");
                HttpResponseMessage responseMessage;
                try
                {
                    responseMessage = await client.PostAsync(_promogameApiUrl, multipartContent);
                }
                catch (HttpRequestException ex)
                {
                    _log.Error($"HTTP request to Promogame API failed. URL: {_promogameApiUrl}", ex);
                    throw new Exception("Failed to connect to gamification service.", ex);
                }

                var responseContent = await responseMessage.Content.ReadAsStringAsync();
                _log.Debug($"Promogame API Response: {responseContent}");

                if (!responseMessage.IsSuccessStatusCode)
                {
                    _log.Error($"Failed GraphQL request. Status: {responseMessage.StatusCode}, URL: {_promogameApiUrl}, Details: {responseContent}");
                    throw new Exception($"Failed GraphQL request. Status: {responseMessage.StatusCode}, Details: {responseContent}");
                }

                var responseDeserializerOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
                };
                var gqlResponse = JsonSerializer.Deserialize<PromogameGraphQLResponse<CreateCampaignResponseWrapperDto>>(responseContent, responseDeserializerOptions);

                if (gqlResponse?.Errors != null && gqlResponse.Errors.Any())
                {
                    var errorMessages = string.Join("; ", gqlResponse.Errors.Select(e => e.Message));
                    _log.Error($"GraphQL error: {errorMessages}. Query: {mutation}");
                    throw new Exception($"GraphQL error(s) from Promogame: {errorMessages}");
                }

                return gqlResponse.Data.CreateCampaign.Id;
            });
        }

        public async Task<CampaignResponseDto> GetCampaign(string campaignId)
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return null;
            }

            // Ensure we have valid tokens by attempting a refresh if needed
            try
            {
                await RefreshTokenAndRetryAsync();
                // Get the updated settings after potential refresh
                settings = await _gamificationRepository.GetSettingsAsync();
            }
            catch (Exception ex)
            {
                _log.Warn($"Failed to refresh token for GetCampaign, using existing tokens: {ex.Message}");
            }

            return new CampaignResponseDto
            {
                AccessToken = settings.AccessToken,
                RefreshToken = settings.RefreshToken,
                DashboardUrl = _dashboardUrl,
                CampaignId = campaignId
            };
        }

        public async Task<List<GameCampaignDto>> GetListCampaign(string shopId)
        {
            var settings = await _gamificationRepository.GetSettingsAsync() ?? await EnsureValidGamificationTokenAsync(shopId);

            var shopSetting = await _gamificationShopRepository.GetGameBrandIdByShopId(shopId);
            if (shopSetting == null)
            {
                _log.Error("Gamification settings not found");
                return null;
            }

            var brandId = shopSetting.GameBrandId;
            var activeCampaignId = shopSetting.CampaignId;
            var isActived = shopSetting.IsActived;

            var query = @"
                        query GetAllCampaigns($brandId: String!, $start: Int!, $count: Int!){
                            getAllCampaigns(filter: {brandId: $brandId}, input: {start: $start, count: $count}){
                            campaigns {
                                name
                                id
                                thumbnailLink
                                externalCampaignId
                            }
                            }
                        }
                    ";

            var variables = new
            {
                brandId = brandId,
                start = 0,
                count = 100
            };

            var response = await ExecuteGraphQLRequestWithTokenRefreshAsync<object, GetAllCampaignsResponseWrapperDto>(
                settings,
                query,
                variables,
                async (token) => await ExecuteGraphQLRequestAsync<object, GetAllCampaignsResponseWrapperDto>(query, variables, token)
            );

            if (response?.GetAllCampaigns?.Campaigns == null)
                return new List<GameCampaignDto>();

            // Gán trường Active - chỉ active khi cả campaignId khớp và IsActived = true
            foreach (var campaign in response.GetAllCampaigns.Campaigns)
            {
                campaign.Active = (campaign.Id == activeCampaignId && isActived);
            }
            return response.GetAllCampaigns.Campaigns;
        }

        public async Task<GameCampaignDto> GetCampaignActiveByShopId(string shopId)
        {
            var campaigns = await GetListCampaign(shopId);
            return campaigns?.FirstOrDefault(c => c.Active == true);
        }

        public async Task ActiveCampaign(string shopId, string campaignId)
        {
            await _gamificationShopRepository.UpdateCampaignIdAsync(shopId, campaignId);
            await _gamificationShopRepository.UpdateCampaignStatusAsync(shopId, true);
        }

        public async Task Deactivate(string shopId)
        {
            // Tắt chiến dịch bằng cách set IsActived thành false
            await _gamificationShopRepository.UpdateCampaignStatusAsync(shopId, false);
        }

        public async Task<List<GameDto>> GetAllGamesAsync()
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return null;
            }
            var query = @"
                query {
                  getAllGames {
                    id
                    name
                  }
                }
            ";
            var response = await ExecuteGraphQLRequestWithTokenRefreshAsync<object, GetAllGamesResponseWrapperDto>(
                settings,
                query,
                null,
                async (token) => await ExecuteGraphQLRequestAsync<object, GetAllGamesResponseWrapperDto>(query, null, token)
            );
            return response?.GetAllGames ?? new List<GameDto>();
        }
    }
}