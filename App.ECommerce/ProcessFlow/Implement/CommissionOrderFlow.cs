﻿using App.ECommerce.Helpers.Interface;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos.ResultDtos;
using App.ECommerce.Resource.Model;

using AutoMapper;

using log4net;

using MongoDB.Bson;
using MongoDB.Driver;

using Newtonsoft.Json;

using static App.ECommerce.Resource.Enums.AffiliationEnum;

namespace App.ECommerce.ProcessFlow.Implement
{
    public class CommissionOrderFlow(
        IMapper _mapper,
        IUserRepository _userRepository,
        IOrderRepository _orderRepository,
        ICommissionsConfigHelpers _commissionsConfigHelpers
        ) : ICommissionOrderFlow
    {
        private readonly ILog _log = log4net.LogManager.GetLogger(typeof(CommissionOrderFlow));

        private AdvancedCommissionsConfig _advancedCommissionsConfig;
        private BasicCommissionsConfig _basicCommissionsConfig;

        public async Task<PagingResult<CommissionOrderOutputDto>> GetCommissionOrders(string shopId, QueryParametersExtend query)
        {
            await GetCommissonConfig(shopId);
            // get orders
            var orders = await GetShopOrders(shopId, query);

            var preResult = _mapper.Map<List<CommissionOrderOutputDto>>(orders.Result);

            return new PagingResult<CommissionOrderOutputDto> { Result = preResult, Total = orders.Total };
        }

        public async Task<CommissionBreakUp> GetCommissionBreakUp(string requestId, string shopId, Order order)
        {
            try
            {
                _log.Info($"{requestId} GetCommissionBreakUp shopId: {shopId}");
                var config = await _commissionsConfigHelpers.GetCommissonConfig(shopId);
                _log.Info($"{requestId} GetCommissionBreakUp config: {JsonConvert.SerializeObject(config)}");

                if (config != null)
                {
                    _advancedCommissionsConfig = config.AdvancedCommissionsConfig;
                    _basicCommissionsConfig = config.BasicCommissionsConfig;

                    if (order.Creator != null)
                    {
                        var creatorId = order.Creator.UserId;

                        // check creator f0 or f1 or f2 >> if(f2) >> get f1Id, f2Id
                        var creatorLevel = await CheckOrderCreatorLevel(creatorId);

                        var orderSmall = _mapper.Map<CommissionOrderOutputDto>(order);
                        // check the commisson rate base on items >> commission group >> commission base
                        return CalculateCommissionValue(orderSmall, creatorLevel);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _log.Error($"{requestId} GetCommissionBreakUp failed: {ex.Message}", ex);

                return null;
            }
        }

        public long GetTotalCommissionOrders(List<Order> orders, CommissionsConfig commissionsConfig = null, string? shopId = null)
        {
            if (shopId != null)
            {
                orders = orders.Where(o => o.ShopId == shopId).ToList();
            }
            if (commissionsConfig != null)
            {
                _basicCommissionsConfig = commissionsConfig.BasicCommissionsConfig;
                _advancedCommissionsConfig = commissionsConfig.AdvancedCommissionsConfig;
            }
            var preResult = _mapper.Map<List<CommissionOrderOutputDto>>(orders);

            long totalCommission = 0;

            foreach (var order in preResult)
            {
                if (order.CommissionBreakUp != null)
                {
                    totalCommission = (long)Math.Round(order.CommissionBreakUp.CommissionDistribution.Where(d => d.IsActive).Sum(d => d.Value), MidpointRounding.AwayFromZero);
                }
            }
            return totalCommission;
        }

        private async Task<PagingResult<Order>> GetShopOrders(string shopId, QueryParametersExtend query)
        {
            var filterBuilder = Builders<Order>.Filter;
            var filters = new List<FilterDefinition<Order>> { };
            var shopFilter = filterBuilder.Eq(o => o.ShopId, shopId);

            filters.Add(shopFilter);

            var statusFilter = filterBuilder.Eq(o => o.StatusOrder, TypeOrderStatus.Success);
            filters.Add(statusFilter);

            // Add filter for CommissionBreakUp not null or empty
            var commissionFilter = filterBuilder.And(
                filterBuilder.Ne(o => o.CommissionBreakUp, null),
                filterBuilder.Not(filterBuilder.Size(o => o.CommissionBreakUp, 0))
            );
            filters.Add(commissionFilter);

            //var originFilter = filterBuilder.Eq(o => o.OrderOrigin, TypeOrigin.Affiliation);
            //filters.Add(originFilter);

            if (!string.IsNullOrEmpty(query.SearchTerm))
            {
                var searchFilter = filterBuilder.Or(
                    filterBuilder.Regex(o => o.Creator.FullName, new BsonRegularExpression(query.SearchTerm, "i")),
                    filterBuilder.Eq(o => o.Creator.UserId, query.SearchTerm)
                );
                filters.Add(searchFilter);
            }

            if (query.StartDate.HasValue && query.EndDate.HasValue)
            {
                var dateFilter = filterBuilder.And(
                    filterBuilder.Gte(o => o.Created, query.StartDate.Value),
                    filterBuilder.Lte(o => o.Created, query.EndDate.Value)
                );
                filters.Add(dateFilter);
            }
            return await _orderRepository.GetOrdersAsync(filters, new PagingConfig { PageIndex = query.PageNumber, PageSize = query.PageSize, SkipedPaging = query.SkipedPaging });
        }

        private async Task<CommissionBreakUp> CheckOrderCreatorLevel(string userId)
        {
            var currUser = await _userRepository.FindUserByIdAsync(userId);

            if (currUser == null)
            {
                throw new Exception("Not found Order creator");
            }

            var result = new CommissionBreakUp()
            {
                OrderCreatorLevel = OrderCreatorLevel.F0,
                CommissionDistribution = new List<CommissionDistribution>()
            };

            bool isAllowCmmF0 = CheckAllowCommisson(currUser, _advancedCommissionsConfig.PartnerCommExpiry);

            if (currUser.ParentId == null & !isAllowCmmF0)
            {
                throw new Exception("Creator is not in commission plan");
            }
            // phân bố hoa hồng cho nó
            var breakF0 = new CommissionDistribution()
            {
                CommissionLevel = CommissionLevel.F0,
                UserId = userId,
                FullName = currUser.Fullname,
                Value = 0,
                IsActive = isAllowCmmF0
            };

            result.CommissionDistribution.Add(breakF0);

            if (string.IsNullOrEmpty(currUser.ParentId))
            {
                result.OrderCreatorLevel = OrderCreatorLevel.F0;
                return result;
            }
            // phân bố hoa hồng cho cha
            var parent = await _userRepository.FindUserByIdAsync(currUser.ParentId);

            var breakF1 = new CommissionDistribution()
            {
                CommissionLevel = CommissionLevel.F1,
                UserId = parent.UserId,
                FullName = parent.Fullname,
                Value = 0,
                IsActive = CheckAllowCommisson(parent, _advancedCommissionsConfig.PartnerCommExpiry)
            };
            result.CommissionDistribution.Add(breakF1);

            if (string.IsNullOrEmpty(parent.ParentId))
            {
                result.OrderCreatorLevel = OrderCreatorLevel.F1;
                return result;

            }

            var f0User = await _userRepository.FindUserByIdAsync(parent.ParentId);
            // phân bố hoa hồng cho cha và ông nội

            var breakF2 = new CommissionDistribution()
            {
                CommissionLevel = CommissionLevel.F2,
                UserId = f0User.UserId,
                FullName = f0User.Fullname,
                Value = 0,
                IsActive = CheckAllowCommisson(f0User, _advancedCommissionsConfig.PartnerCommExpiry)
            };
            result.CommissionDistribution.Add(breakF2);
            result.OrderCreatorLevel = OrderCreatorLevel.F2;
            return result;
        }

        private CommissionBreakUp CalculateCommissionValue(CommissionOrderOutputDto order, CommissionBreakUp commissionBreakUp)
        {
            if (order == null || commissionBreakUp == null)
            {
                throw new ArgumentNullException("Order or CommissionBreakDown cannot be null");
            }

            CommissionDistribution calF0 = commissionBreakUp.CommissionDistribution
                                                            .FirstOrDefault(d => d.CommissionLevel == CommissionLevel.F0 && d.IsActive);
            CommissionDistribution calF1 = commissionBreakUp.CommissionDistribution
                                                            .FirstOrDefault(d => d.CommissionLevel == CommissionLevel.F1 && d.IsActive);
            CommissionDistribution calF2 = commissionBreakUp.CommissionDistribution
                                                            .FirstOrDefault(d => d.CommissionLevel == CommissionLevel.F2 && d.IsActive);

            switch (commissionBreakUp.OrderCreatorLevel)
            {
                case OrderCreatorLevel.F0:
                    if (calF0 != null)
                    {
                        calF0.Value = CalculateF0Commission(order);
                    }
                    break;
                case OrderCreatorLevel.F1:
                    if (calF0 != null)
                    {
                        calF0.Value = CalculateF0Commission(order);
                    }
                    if (calF1 != null)
                    {
                        calF1.Value = CalculateF1Commission(order, calF1);
                    }
                    break;
                case OrderCreatorLevel.F2:

                    if (calF0 != null)
                    {
                        calF0.Value = CalculateF0Commission(order);
                    }
                    if (calF1 != null)
                    {
                        calF1.Value = CalculateF1Commission(order, calF1);
                    }
                    if (calF2 != null)
                    {
                        calF2.Value = CalculateF2Commission(order, calF2);
                    }
                    break;
            }
            return commissionBreakUp;
        }

        private double CalculateF0Commission(CommissionOrderOutputDto order)
        {
            if (_advancedCommissionsConfig.IsSelfReferralCommission)
            {
                //  return (double)order.ListItems
                //     .Where(item => item != null)
                //     .Sum(item => item.Price * item.Quantity * (_advancedCommissionsConfig.SelfReferralCommissionPercentage / 100));

                var baseAmount = order.Price;
                var commission = baseAmount * (_advancedCommissionsConfig.SelfReferralCommissionPercentage / 100);
                return (double)commission;
            }
            return 0;
        }

        // private double CalculateF1Commission(CommissionOrderOutputDto order, CommissionDistribution commissionDistribution)
        // {
        //     return (double)order.ListItems
        //         .Where(item => item != null)
        //         .Sum(item => item.Price * item.Quantity * _commissionsConfigHelpers.GetPercentageItem(item.ItemsCode, commissionDistribution.UserId, _basicCommissionsConfig).PercentLv1 / 100);
        // }

        private double CalculateF1Commission(CommissionOrderOutputDto order, CommissionDistribution commissionDistribution)
        {
            var hasItemOrGroupConfig = HasItemOrGroupCommissionConfig(order, commissionDistribution.UserId);

            if (hasItemOrGroupConfig)
            {
                return (double)order.ListItems
               .Where(item => item != null)
               .Sum(item => item.Price * item.Quantity * _commissionsConfigHelpers.GetPercentageItem(item.ItemsCode, commissionDistribution.UserId, _basicCommissionsConfig).PercentLv1 / 100);
            }
            else
            {
                var baseAmount = order.Price;
                return baseAmount * _basicCommissionsConfig.LevelOneCommissionPercentage / 100;
            }
        }

        private double CalculateF2Commission(CommissionOrderOutputDto order, CommissionDistribution commissionDistribution)
        {
            if (!_basicCommissionsConfig.IsActiveLevelTwo)
            {
                return 0;
            }

            var hasItemOrGroupConfig = HasItemOrGroupCommissionConfig(order, commissionDistribution.UserId);

            if (hasItemOrGroupConfig)
            {
                return (double)order.ListItems
                     .Where(item => item != null)
                     .Sum(item => item.Price * item.Quantity * _commissionsConfigHelpers.GetPercentageItem(item.ItemsCode, commissionDistribution.UserId, _basicCommissionsConfig).PercentLv2 / 100);
            }
            else
            {
                var baseAmount = order.Price;
                return baseAmount * (_basicCommissionsConfig.LevelTwoCommissionPercentage ?? 0) / 100;
            }
        }

        // private double CalculateF2Commission(CommissionOrderOutputDto order, CommissionDistribution commissionDistribution)
        // {
        //     double commissionF2 = (double)order.ListItems
        //         .Where(item => item != null)
        //         .Sum(item => item.Price * item.Quantity * _commissionsConfigHelpers.GetPercentageItem(item.ItemsCode, commissionDistribution.UserId, _basicCommissionsConfig).PercentLv2 / 100);

        //     if (!_basicCommissionsConfig.IsActiveLevelTwo)
        //     {
        //         commissionF2 = 0;
        //     }

        //     return commissionF2;
        // }

        private bool HasItemOrGroupCommissionConfig(CommissionOrderOutputDto order, string userId)
        {
            return order.ListItems.Any(item =>
            {
                if (item == null) return false;

                bool itemConfigExists = _basicCommissionsConfig.ItemGroupCommissionsConfigs
                    .Any(cfg => cfg.ItemsCode == item.ItemsCode);

                bool groupConfigExists = _basicCommissionsConfig.UserGroupCommissionsConfigs
                    .Any(cfg => cfg.UserIds.Contains(userId));

                return itemConfigExists || groupConfigExists;
            });
        }

        public async Task GetCommissonConfig(string shopId)
        {
            var config = await _commissionsConfigHelpers.GetCommissonConfig(shopId);
            // set data cho advanced and basic commission
            _advancedCommissionsConfig = config.AdvancedCommissionsConfig;
            _basicCommissionsConfig = config.BasicCommissionsConfig;
        }

        public async Task<bool> CheckCommissionStatus(string shopId)
        {
            try
            {
                await GetCommissonConfig(shopId);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        private bool CheckAllowCommisson(Repository.Entities.User user, double expYear)
        {
            if (user == null) { return false; }
            if (user.AffiliationStatus != AffiliationTypeStatus.Actived)
            {
                return false;
            }
            if (!user.ApprovalDate.HasValue)
            {
                return false;
            }
            return _commissionsConfigHelpers.IsNotExpired(expYear, user.ApprovalDate ?? DateTime.Now);

        }
    }
}
