using System;
using System.Net;
using App.Base.Repository.Interface;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.ProcessFlow.Models;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Services.Transport;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units;
using App.ECommerce.Units.Enums;
using AutoMapper;
using log4net;
using Microsoft.Extensions.Localization;
using App.ECommerce.Units.Enums.Order;
using App.ECommerce.Repository.Implement;
using Newtonsoft.Json;
using MongoDB.Bson;
using App.ECommerce.Resource.Model;
using System.Globalization;

namespace App.ECommerce.ProcessFlow.Implement;

public class OrderFlow : IOrderFlow
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(OrderFlow));
    private readonly IUserRepository _userRepository;
    private readonly IPartnerRepository _partnerRepository;
    private readonly IShippingAddressRepository _addressRepository;
    private readonly ITagRepository _tagRepository;
    private readonly IItemsRepository _itemsRepository;
    private readonly ICartRepository _cartRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IItemOptionRepository _itemOptionRepository;
    private readonly IItemOptionGroupRepository _itemOptionGroupRepository;
    private readonly ITransportService _transportService;
    private readonly ICalcMemberLevelFlow _calcMemberLevelFlow;
    private readonly IMembershipLevelRepository _membershipLevelRepository;
    private readonly IPaymentRepository _paymentRepository;
    private readonly ITriggerEventFlow _triggerEventFlow;
    private readonly ICommissionOrderFlow _commissionOrderFlow;
    private readonly IInvoiceFlow _invoiceFlow;
    private readonly IMapper _mapper;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IOrderValidator _orderValidator;
    private readonly IOrderCalculator _orderCalculator;
    private readonly IVoucherFlow _voucherFlow;
    private readonly IAffiliationPartnerRepository _affiliationPartnerRepository;
    private readonly IItemsFlow _itemsFlow;
    private readonly ICartFlow _cartFlow;
    private readonly ISyncServiceConfigRepository _syncServiceConfigRepository;
    private readonly ISyncServiceFlow _syncServiceFlow;

    public OrderFlow(
        IMapper mapper,
        IServiceScopeFactory serviceScopeFactory,
        IUserRepository userRepository,
        IPartnerRepository partnerRepository,
        IShippingAddressRepository addressRepository,
        ITagRepository tagRepository,
        IItemsRepository itemsRepository,
        ICartRepository cartRepository,
        IOrderRepository orderRepository,
        IItemOptionRepository itemOptionRepository,
        IItemOptionGroupRepository itemOptionGroupRepository,
        ITransportService transportService,
        ICalcMemberLevelFlow calcMemberLevelFlow,
        IMembershipLevelRepository membershipLevelRepository,
        IPaymentRepository paymentRepository,
        ITriggerEventFlow triggerEventFlow,
        ICommissionOrderFlow commissionOrderFlow,
        IInvoiceFlow invoiceFlow,
        IOrderValidator orderValidator,
        IOrderCalculator orderCalculator,
        IVoucherFlow voucherFlow,
        IAffiliationPartnerRepository affiliationPartnerRepository,
        IItemsFlow itemsFlow,
        ICartFlow cartFlow,
        ISyncServiceConfigRepository syncServiceConfigRepository,
        ISyncServiceFlow syncServiceFlow
    )
    {
        _mapper = mapper;
        _serviceScopeFactory = serviceScopeFactory;
        _userRepository = userRepository;
        _partnerRepository = partnerRepository;
        _addressRepository = addressRepository;
        _tagRepository = tagRepository;
        _itemsRepository = itemsRepository;
        _cartRepository = cartRepository;
        _orderRepository = orderRepository;
        _itemOptionRepository = itemOptionRepository;
        _itemOptionGroupRepository = itemOptionGroupRepository;
        _transportService = transportService;
        _calcMemberLevelFlow = calcMemberLevelFlow;
        _membershipLevelRepository = membershipLevelRepository;
        _paymentRepository = paymentRepository;
        _triggerEventFlow = triggerEventFlow;
        _commissionOrderFlow = commissionOrderFlow;
        _invoiceFlow = invoiceFlow;
        _orderValidator = orderValidator;
        _orderCalculator = orderCalculator;
        _voucherFlow = voucherFlow;
        _affiliationPartnerRepository = affiliationPartnerRepository;
        _itemsFlow = itemsFlow;
        _cartFlow = cartFlow;
        _syncServiceConfigRepository = syncServiceConfigRepository;
        _syncServiceFlow = syncServiceFlow;
    }

    public async Task<Result<OrderDto>> CreateOrder(OrderFlowModel obj)
    {
        _log.Info($"CreateOrder CartId: {obj.CartDto?.CartId}");
        // Validate Partner chỉ khi đơn hàng được tạo bởi partner
        if (!string.IsNullOrEmpty(obj.PartnerId))
        {
            var partner = await ValidatePartner(obj.PartnerId);
            if (!partner.IsSuccess)
                return Result<OrderDto>.Failure(partner.Errors);
        }

        // Validate Cart
        var objCart = ValidateCart(obj.CartDto.CartId);
        if (!objCart.IsSuccess)
            return Result<OrderDto>.Failure(objCart.Errors);

        // Validate Payment
        var paymentValidation = ValidatePayment(objCart.Data);
        if (!paymentValidation.IsSuccess)
            return Result<OrderDto>.Failure(paymentValidation.Errors);

        // Validate TransportService
        var transportServiceValidation = _orderValidator.ValidateTransportService(objCart.Data);
        if (!transportServiceValidation.IsSuccess)
            return Result<OrderDto>.Failure(transportServiceValidation.Errors);

        if (objCart.Data.TransportService != transportServiceValidation.Data)
        {
            objCart.Data.TransportService = transportServiceValidation.Data;
            await _cartRepository.UpdateCart(objCart.Data);
        }

        // Validate User
        var user = ValidateUser(objCart.Data.UserId);

        if (objCart.Data.CartOrigin == TypeOrigin.ZaloMiniApp || !string.IsNullOrEmpty(objCart.Data.UserId))
        {
            if (!user.IsSuccess)
                return Result<OrderDto>.Failure(user.Errors);
        }

        // Validate Shop
        var shopValidation = _orderValidator.ValidateShop(objCart.Data.ShopId, obj.PartnerId);
        if (!shopValidation.IsSuccess)
            return Result<OrderDto>.Failure(shopValidation.Errors);
        var shop = shopValidation.Data;

        // Validate Items
        var itemsList = _itemsRepository.FindByItemsIds(string.Join(",", objCart.Data.ListItems.Select(x => x.ItemsId)));
        var itemsValidation = _orderValidator.ValidateItems(user.Data.UserId, objCart.Data.ListItems, itemsList);
        if (!itemsValidation.IsSuccess)
            return Result<OrderDto>.Failure(itemsValidation.Errors, itemsValidation.Message);

        // Process Item Options
        var itemOptionIds = objCart.Data.ListItems.SelectMany(i => i.ExtraOptions).ToList();
        await ProcessItemOptions(objCart.Data.ListItems, itemOptionIds);

        // Validate Vouchers
        if (objCart.Data.CartOrigin == TypeOrigin.ZaloMiniApp || !string.IsNullOrEmpty(objCart.Data.UserId))
        {
            var groupNames = _tagRepository.GetTagsForUser(user.Data.UserId);

            var promotionValidation = _orderValidator.ValidateVoucherPromotion(
                user.Data, groupNames, objCart.Data.VoucherPromotionIds,
                objCart.Data.ListItems, itemsList, objCart.Data.VoucherPromotion, objCart.Data.VoucherCodes);
            if (!promotionValidation.IsSuccess)
                return Result<OrderDto>.Failure(promotionValidation.Errors);

            var transportValidation = _orderValidator.ValidateVoucherTransport(
                user.Data, groupNames, objCart.Data.VoucherTransportIds,
                objCart.Data.ListItems, itemsList, objCart.Data.VoucherTransport, objCart.Data.VoucherCodes);
            if (!transportValidation.IsSuccess)
                return Result<OrderDto>.Failure(transportValidation.Errors);
        }

        // Address handling
        var address = await GetShippingAddress(objCart.Data);

        // Tính phí vận chuyển và giảm giá
        var (shippingFee, branchId) = await _orderCalculator.CalculateShippingFee(
            objCart.Data,
            address,
            shop,
            objCart.Data.VoucherTransport // Truyền voucher vận chuyển để tính giảm giá
        );

        // Tính giá đơn hàng (không bao gồm voucher vận chuyển vì đã xử lý ở trên)
        var orderCalculation = await _orderCalculator.CalculateOrderPrice(
            objCart.Data,
            objCart.Data.VoucherPromotion,
            null, // Không truyền voucher vận chuyển vào đây
            obj.CartDto.ExchangePoints ?? 0
        );

        _log.Info($"CreateOrder TotalTaxAmount: {orderCalculation.TotalTaxAmount}");
        _log.Info($"CreateOrder TotalAfterTax: {orderCalculation.TotalAfterTax}");

        // Cập nhật thông tin vận chuyển và giảm giá
        orderCalculation.TotalShippingFee = shippingFee.TotalShippingFee; // Phí vận chuyển gốc
        orderCalculation.TotalShippingDiscount = shippingFee.TotalShippingDiscount; // Số tiền được giảm từ voucher
        orderCalculation.TotalAfterTax = orderCalculation.TotalAfterTax + Math.Max(0, orderCalculation.TotalShippingFee - orderCalculation.TotalShippingDiscount); // Tổng tiền sau thuế + phí vận chuyển thực tế
        // Validate calculation results
        var calculationValidation = _orderValidator.ValidateOrderCalculation(objCart.Data, orderCalculation);
        if (!calculationValidation.IsSuccess)
            return Result<OrderDto>.Failure(calculationValidation.Errors);

        (string? messagePointAfterCompleteOrder, int pointAfterCompleteOrder) = await _calcMemberLevelFlow.CalcPointGainAfterComplete(objCart.Data.CartId);

        objCart.Data.PointAfterCompleteOrder = pointAfterCompleteOrder;
        
        _log.Info($"CreateOrder orderCalculation: {JsonConvert.SerializeObject(orderCalculation)}");

        // Create Order
        var order = await CreateOrderTransaction(
            cart: objCart.Data,
            calculation: orderCalculation,
            shop: shop,
            userShippingAddress: address,
            creator: address,
            branchId: branchId,
            notes: objCart.Data.Notes
        );
        if (!order.IsSuccess)
            return Result<OrderDto>.Failure(order.Errors);

        // Process Commission (Run background)
        _ = ProcessCommission(order.Data, obj.RequestId)
            .ContinueWith(t => _log.Error($"ProcessCommission error: {t.Exception}"), TaskContinuationOptions.OnlyOnFaulted);

        // Process After Create (Run background)
        _ = ProcessAfterCreateOrder(order.Data, user.Data, shop, obj.PartnerId)
            .ContinueWith(t => _log.Error($"ProcessAfterCreateOrder error: {t.Exception}"), TaskContinuationOptions.OnlyOnFaulted);

        return Result<OrderDto>.Success(_mapper.Map<OrderDto>(order.Data));
    }

    private async Task<ShippingAddress?> GetShippingAddress(Cart cart)
    {
        ShippingAddress? address = null;

        if (!string.IsNullOrEmpty(cart.AddressId))
            address = _addressRepository.FindByShippingAddressId(cart.AddressId);

        if (!string.IsNullOrEmpty(cart.UserId) && address == null)
        {
            User? objUser = _userRepository.FindByUserId(cart.UserId);

            address = new ShippingAddress()
            {
                Id = objUser.Id,
                UserId = objUser?.UserId,
                FullName = objUser?.Fullname,
                PhoneNumber = objUser?.PhoneNumber,
                ProvinceId = objUser?.ProvinceId,
                ProvinceName = objUser?.ProvinceName,
                DistrictId = objUser?.DistrictId,
                DistrictName = objUser?.DistrictName,
                WardId = objUser?.WardId,
                WardName = objUser?.WardName,
                Address = objUser?.Address
            };
        }

        return address;
    }

    private async Task<Result<Partner>> ValidatePartner(string partnerId)
    {
        var partner = await _partnerRepository.FindByPartnerId(partnerId);
        if (partner == null)
            return Result<Partner>.Failure("BASE_USER_AUTH_NOT_FOUND");

        return Result<Partner>.Success(partner);
    }

    private Result<Cart> ValidateCart(string cartId)
    {
        var cart = _cartRepository.FindByCartId(cartId);
        if (cart == null)
            return Result<Cart>.Failure("CART_NOT_FOUND");
        return Result<Cart>.Success(cart);
    }

    private Result<User> ValidateUser(string userId)
    {
        var user = _userRepository.FindByUserId(userId);
        if (user == null)
            return Result<User>.Failure("CART_USER_NOT_FOUND");
        return Result<User>.Success(user);
    }

    private Result<bool> ValidatePayment(Cart cart)
    {
        if (cart.TypePay == null)
            return Result<bool>.Failure("ORDER_TYPE_PAY_INVALID");

        // Validate giới hạn thanh toán online
        if ((cart.TypePay == TypePayment.Momo || cart.TypePay == TypePayment.Zalo) &&
            (cart.Price < 1000 || cart.Price > 50000000))
        {
            return Result<bool>.Failure("ORDER_AMOUNT_REJECT");
        }

        return Result<bool>.Success(true);
    }

    private async Task<Result<Order>> CreateOrderTransaction(
        Cart cart,
        OrderCalculationModel calculation,
        Shop shop,
        ShippingAddress userShippingAddress,
        ShippingAddress creator,
        string? branchId = null,
        string? notes = null)
    {
        try
        {
            var order = new Order
            {
                OrderId = Guid.NewGuid().ToString(),
                TransactionId = calculation.TransactionId,
                PartnerId = cart.PartnerId ?? null,
                ShopId = cart.ShopId ?? "",
                ShopName = shop.ShopName,
                ShopAddress = shop.Address,
                UserShippingAddress = userShippingAddress,
                Creator = creator,
                Notes = notes,
                OrderOrigin = cart.CartOrigin,
                ListItems = cart.ListItems,
                Price = (long)calculation.TotalBeforeTax,
                ExchangePoints = calculation.TotalPoints,
                PointAfterCompleteOrder = cart.PointAfterCompleteOrder ?? 0,
                PointPrice = calculation.TotalPointDiscount,
                VoucherPromotionPrice = (long)calculation.TotalPromotionDiscount,
                VoucherTransportPrice = (long)calculation.TotalShippingDiscount,
                TransportPrice = (long)calculation.TotalShippingFee,
                TotalTaxAmount = calculation.TotalTaxAmount,
                TotalAfterTax = calculation.TotalAfterTax,
                TaxInvoice = cart.TaxInvoice,
                TaxSummary = calculation.TaxSummary,
                TransportService = cart.TransportService,
                StatusTransport = TypeTransportStatus.Created,
                StatusDelivery = cart.StatusDelivery,
                StatusOrder = TypeOrderStatus.Pending,
                TypePay = cart.TypePay ?? TypePayment.COD,
                StatusPay = TypePayStatus.NotPaid,
                Status = TypeStatus.Actived,
                BranchId = branchId ?? "",
                VoucherPromotion = cart.VoucherPromotion,
                VoucherTransport = cart.VoucherTransport,
                VoucherCodes = cart.VoucherCodes ?? new List<string>()
            };

            var createdOrder = await _orderRepository.CreateOrder(order);
            if (createdOrder == null)
            {
                _log.Error($"CreateOrderTransaction: Không thể tạo đơn hàng");
                return Result<Order>.Failure("Không thể tạo đơn hàng");
            }

            // Xóa giỏ hàng
            _cartFlow.DeleteCart(cart.CartId);

            _log.Info($"CreateOrderTransaction: Tích điểm từ đơn hàng {order.OrderNo} - {order.PointAfterCompleteOrder}");

            (string errorMessage, User objUser) = await _calcMemberLevelFlow.CalcUserPointByOrder(order.OrderId);

            return Result<Order>.Success(createdOrder);
        }
        catch (Exception ex)
        {
            _log.Error($"CreateOrderTransaction: {ex}");
            return Result<Order>.Failure(ex.Message);
        }
    }

    private async Task ProcessAfterCreateOrder(Order order, User user, Shop shop, string partnerId)
    {
        // Cập nhật số lượng sản phẩm (quantity và sold)
        _itemsFlow.ProductProcessing(new List<Order> { order }, TypeOrderStatus.Success);

        // Xử lý điểm thành viên
        await ProcessMemberPoints(order, user, partnerId);
        // Xử lý voucher
        if (user != null)
        {
            await _voucherFlow.ProcessVoucherForOrder(new List<Order> { order }, TypeOrderStatus.Success);
        }

        // Tạo đơn vận chuyển nếu cần
        if (order.TransportService == TypeTransportService.AHAMOVE ||
            order.TransportService == TypeTransportService.JTEXPRESS)
        {
            var transportOrderResponse = await _transportService.CreateOrder(order);
            if (transportOrderResponse.Success)
            {
                order.TransportOrderId = transportOrderResponse.Data.order_id;
                _orderRepository.UpdateOrder(order);
            }
            else
            {
                _log.Error($"CreateOrder: Không thể tạo đơn vận chuyển {order.TransportService}: {transportOrderResponse.Message}");
            }
        }

        // Đồng bộ đơn hàng lên external services (chạy background)
        // _ = SyncOrderToExternalServices(order)
        //     .ContinueWith(t => _log.Error($"SyncOrderToExternalServices error: {t.Exception}"), TaskContinuationOptions.OnlyOnFaulted);

        // Tạo hóa đơn
        // await _invoiceFlow.IssueInvoiceAsync($"OrderPartner_{Guid.NewGuid()}", order.OrderId);

        // Trigger event
        await _triggerEventFlow.TriggerEventSendMessage(
            TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_SUCCESS,
            order.ShopId,
            order.OrderId
        );
    }

    private async Task ProcessMemberPoints(Order order, User user, string partnerId)
    {
        if (order.ExchangePoints > 0)
        {
            string userId = user?.UserId ?? partnerId;
            string shopId = order.ShopId;

            // Trừ điểm khi sử dụng
            _membershipLevelRepository.UpdateUserPoints(
                shopId: shopId,
                userId: userId,
                note: "Đổi điểm",
                point: -(order.ExchangePoints ?? 0),
                IsAdd: false,
                type: TypeTransaction.Discount,
                orderId: order.OrderId,
                detail: $"Sử dụng điểm để giảm giá mua sắm đơn hàng #{order.OrderNo}"
            );

            // Tính điểm tích lũy
            (string errorMessage, User objUser) = await _calcMemberLevelFlow.CalcUserPointByOrder(order.OrderId);

            _log.Info($"ProcessMemberPoints: Tính điểm cho đơn hàng {order.OrderId} - {errorMessage}");
        }
    }

    private async Task ProcessCommission(Order order, string requestId)
    {
        // Tính commission cho đơn hàng
        order.CommissionBreakUp = await _commissionOrderFlow.GetCommissionBreakUp(
            requestId,
            order.ShopId,
            order
        );
        _log.Info($"Hoa hồng cho đơn hàng: {order.CommissionBreakUp.ToJson()}");

        // Cập nhật commission vào đơn hàng
        _orderRepository.UpdateOrder(order);
    }

    private async Task ProcessItemOptions(List<ItemsOrder> items, List<string> itemOptionIds)
    {
        if (!itemOptionIds.Any()) return;

        // Lấy thông tin options
        var itemOptions = await _itemOptionRepository.GetItemOptionsByIds(itemOptionIds);
        var itemOptionGroupIds = itemOptions.Select(i => i.ItemOptionGroupId).Distinct().ToList();

        // Lấy thông tin chi tiết
        var itemOptionsGroup = await _itemOptionGroupRepository.GetItemOptionGroupByIds(itemOptionGroupIds);

        // Lưu vào từng item trong đơn hàng
        foreach (var item in items)
        {
            var itemExtraOptions = item.ExtraOptions;
            if (!itemExtraOptions.Any()) continue;

            var itemOptionsDetail = itemOptionsGroup
                .Where(g => itemOptions.Any(o => o.ItemOptionGroupId == g.ItemOptionGroupId && itemExtraOptions.Contains(o.ItemOptionId)))
                .Select(g => new ItemOptionGroupDetail
                {
                    Id = new BsonObjectId(ObjectId.GenerateNewId()).ToString(), // Khởi tạo Id cho ItemOptionGroupDetail
                    ItemOptionGroupId = g.ItemOptionGroupId,
                    Name = g.Name,
                    Require = g.Require,
                    IsMultiSelect = g.IsMultiSelect,
                    ItemOptions = itemOptions
                        .Where(o => o.ItemOptionGroupId == g.ItemOptionGroupId && itemExtraOptions.Contains(o.ItemOptionId))
                        .Select(o => new ItemOption
                        {
                            Id = new BsonObjectId(ObjectId.GenerateNewId()).ToString(), // Khởi tạo Id cho ItemOption
                            ItemOptionId = o.ItemOptionId,
                            Name = o.Name,
                            Price = o.Price
                        }).ToList()
                }).ToList();

            item.ExtraOptionsDetail.AddRange(itemOptionsDetail);
        }
    }

    public (bool Success, string Message) ValidateOrderUpdate(Order order, UpdateOrderInfoActionEnum updateAction)
    {
        // Validate không update đơn đã hoàn thành hoặc đã hủy
        if (order.StatusOrder == TypeOrderStatus.Failed || order.StatusOrder == TypeOrderStatus.Success)
            return (false, "ORDER_IS_DONE");

        switch (updateAction)
        {
            case UpdateOrderInfoActionEnum.StatusDelivery:
                if (order.StatusPay == TypePayStatus.NotPaid)
                    return (false, "ORDER_NOT_PAID");
                break;

            case UpdateOrderInfoActionEnum.CancelOrder:
                if (order.StatusTransport == TypeTransportStatus.Delivering)
                    return (false, "ORDER_IS_BEING_DELIVERED");
                break;

            case UpdateOrderInfoActionEnum.StatusPay:
                if (order.StatusPay == TypePayStatus.Paid || order.StatusPay == TypePayStatus.Refund)
                    return (false, "ORDER_PAID_OR_REFUNDED");
                break;
        }

        return (true, string.Empty);
    }

    public async Task<(bool Success, string Message)> UpdateOrderStatus(Order order, TypeTransportStatus transportStatus, TypeOrderStatus? orderStatus = null)
    {
        try
        {
            order.StatusTransport = transportStatus;
            if (orderStatus.HasValue)
            {
                order.StatusOrder = orderStatus.Value;
                if (orderStatus.Value == TypeOrderStatus.Success)
                {
                    order.CompletedAt = DateTimes.Now();
                }
                _orderRepository.UpdateOrder(order);

                if (orderStatus.Value == TypeOrderStatus.Success)
                {
                    order.CompletedAt = DateTimes.Now();

                    (string errorMessage, User objUser) = await _calcMemberLevelFlow.CalcUserPointByOrder(order.OrderId);

                    _log.Info($"UpdateOrderStatus: Tính điểm cho đơn hàng {order.OrderId} - {errorMessage}");

                    await ActionAfterCompleteOrder(order.OrderId);

                    // _itemsFlow.ProductProcessing([order], TypeOrderStatus.Success);

                    if (order.Creator?.UserId != null)
                    {
                        await _calcMemberLevelFlow.CalcUserLevel(order.Creator?.UserId);
                        await _calcMemberLevelFlow.CalcUserPointsForReferralOrder(order.OrderId);
                    }
                }
            }
            else
                _orderRepository.UpdateOrder(order);

            // Gửi thông báo
            if (transportStatus == TypeTransportStatus.Delivering)
            {
                await _triggerEventFlow.TriggerEventSendMessage(
                    TriggerEventConst.TRANSACTION_NOTIFICATION_DELIVERY,
                    order.ShopId,
                    order.OrderId
                );
            }
            else if (transportStatus == TypeTransportStatus.Success)
            {
                await _triggerEventFlow.TriggerEventSendMessage(
                    TriggerEventConst.TRANSACTION_NOTIFICATION_DELIVERY_SUCCESS,
                    order.ShopId,
                    order.OrderId
                );
            }

            return (true, "Cập nhật trạng thái đơn hàng thành công");
        }
        catch (Exception ex)
        {
            _log.Error($"UpdateOrderStatus: {ex.Message}");
            return (false, ex.Message);
        }
    }

    public async Task<(bool Success, string Message)> UpdateOrderNotes(Order order, string notes)
    {
        try
        {
            order.Notes = notes;
            _orderRepository.UpdateOrder(order);
            return (true, "Cập nhật ghi chú đơn hàng thành công");
        }
        catch (Exception ex)
        {
            _log.Error($"UpdateOrderNotes: {ex.Message}");
            return (false, ex.Message);
        }
    }

    public async Task<(bool Success, string Message)> CancelOrder(Order order)
    {
        try
        {
            // Cập nhật số lượng sản phẩm (quantity và sold)
            _itemsFlow.ProductProcessing(new List<Order> { order }, TypeOrderStatus.Failed);

            var cancelTransportResult = await _transportService.CancelTransportService(order);
            if (!cancelTransportResult.Success)
            {
                return (false, "Không thể hủy đơn vận chuyển");
            }

            order.StatusOrder = TypeOrderStatus.Failed;
            order.StatusTransport = TypeTransportStatus.Cancel;
            _orderRepository.UpdateOrder(order);

            var orderList = new List<Order> { order };
            if (order.Creator != null)
            {
                await _voucherFlow.ProcessVoucherForOrder(orderList, TypeOrderStatus.Failed);
            }

            await _calcMemberLevelFlow.RefundPointToUser(order);

            await _triggerEventFlow.TriggerEventSendMessage(
                TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_CANCELED,
                order.ShopId,
                order.OrderId
            );

            return (true, "Hủy đơn hàng thành công");
        }
        catch (Exception ex)
        {
            _log.Error($"CancelOrder: {ex.Message}");
            return (false, ex.Message);
        }
    }

    public async Task<(bool Success, string Message)> UpdateOrderPayment(Order order)
    {
        try
        {
            order.StatusPay = TypePayStatus.Paid;
            _orderRepository.UpdateOrder(order);

            (string errorMessage, User objUser) = await _calcMemberLevelFlow.CalcUserPointByOrder(order.OrderId);

            _log.Info($"UpdateOrderPayment: Tính điểm cho đơn hàng {order.OrderId} - {errorMessage}");

            // Tạo đơn vận chuyển
            if (order.TransportService == TypeTransportService.AHAMOVE ||
                order.TransportService == TypeTransportService.JTEXPRESS)
            {
                var transportOrderResponse = await _transportService.CreateOrder(order);
                if (transportOrderResponse.Success)
                {
                    var transportOrderData = transportOrderResponse.Data;
                    order.TransportOrderId = transportOrderData.order_id;
                    _orderRepository.UpdateOrder(order);
                }
            }

            await _triggerEventFlow.TriggerEventSendMessage(
                TriggerEventConst.TRANSACTION_NOTIFICATION_PAYMENT_SUCCESS,
                order.ShopId,
                order.OrderId
            );

            await _invoiceFlow.IssueInvoiceAsync($"OrderPartner_{Guid.NewGuid()}", order.OrderId);

            return (true, "Cập nhật trạng thái thanh toán thành công");
        }
        catch (Exception ex)
        {
            _log.Error($"UpdateOrderPayment: {ex.Message}");
            return (false, ex.Message);
        }
    }

    public async Task DeleteAllOrder(string shopId)
    {
        PagingResult<Order> listOrder = _orderRepository.ListOrder(Constants.MaxPaging, shopId: shopId);

        foreach (var order in listOrder.Result)
        {
            _orderRepository.DeleteOrder(order.OrderId);
        }
    }

    public async Task DeleteOrder(string orderId)
    {
        Order? order = _orderRepository.FindByOrderId(orderId);

        if (order != null)
            _orderRepository.DeleteOrder(order.OrderId);
    }

    public async Task OrdersProcessing(List<Order> listOrder, TypeOrderStatus typeStatusOrder)
    {
        _orderRepository.UpdateStatusListOrder(listOrder, typeStatusOrder);
    }

    public async Task ActionAfterCompleteOrder(string orderId)
    {
        Order? order = _orderRepository.FindByOrderId(orderId);

        if (order != null && order.StatusOrder == TypeOrderStatus.Success)
        {
            if (order?.Creator?.UserId != null)
                _affiliationPartnerRepository.UpdateUserAffiliationStatus(order.Creator.UserId);
        }
    }

    /// <summary>
    /// Đồng bộ đơn hàng lên các external services đã được cấu hình
    /// </summary>
    private async Task SyncOrderToExternalServices(Order order)
    {
        try
        {
            // Lấy tất cả các sync service config đang active cho shop này
            var syncConfigs = await _syncServiceConfigRepository.GetAllConfigsByShopId(order.ShopId);
            var activeConfigs = syncConfigs?.Where(c => c.Status == TypeStatus.Actived).ToList();
            Console.WriteLine($"SyncOrderToExternalServices: activeConfigs: {activeConfigs?.Count}");

            if (activeConfigs == null || !activeConfigs.Any())
            {
                _log.Info($"SyncOrderToExternalServices: Không có external service nào được cấu hình cho shop {order.ShopId}");
                return;
            }

            // Đồng bộ đơn hàng lên từng external service
            foreach (var config in activeConfigs)
            {
                try
                {
                    _log.Info($"SyncOrderToExternalServices: Đồng bộ đơn hàng {order.OrderNo} lên {config.SyncService}");

                    var result = await _syncServiceFlow.CreateOrderToExternalService(config.SyncService, order, order.ShopId);

                    if (result.IsSuccess)
                    {
                        _log.Info($"SyncOrderToExternalServices: Đồng bộ thành công đơn hàng {order.OrderNo} lên {config.SyncService}");
                    }
                    else
                    {
                        _log.Error($"SyncOrderToExternalServices: Lỗi đồng bộ đơn hàng {order.OrderNo} lên {config.SyncService}: {string.Join(", ", result.Errors)}");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error($"SyncOrderToExternalServices: Exception khi đồng bộ đơn hàng {order.OrderNo} lên {config.SyncService}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            _log.Error($"SyncOrderToExternalServices: Exception chung: {ex.Message}");
        }
    }
}
