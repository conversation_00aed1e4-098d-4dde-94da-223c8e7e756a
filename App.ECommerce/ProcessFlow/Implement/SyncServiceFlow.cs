using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Enums;
using App.ECommerce.ProcessFlow.Interface;
using AutoMapper;
using Microsoft.Extensions.Caching.Memory;
using App.ECommerce.Helpers;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.Resource.Dtos.Webhooks;
using App.ECommerce.Resource.Dtos.NhanhDtos;

namespace App.ECommerce.ProcessFlow.Implement;

public class SyncServiceFlow : ISyncServiceFlow
{
    protected readonly IItemsRepository _itemsRepository;
    protected readonly ISyncServiceConfigRepository _syncConfigRepository;
    protected readonly INhanhHelper _nhanhHelper;
    protected readonly ISapoHelper _sapoHelper;
    protected readonly IKiotVietHelper _kiotVietHelper;
    protected readonly IOdooHelper _odooHelper;

    public SyncServiceFlow(
        IItemsRepository itemsRepository,
        ISyncServiceConfigRepository syncConfigRepository,
        INhanhHelper nhanhHelper,
        ISapoHelper sapoHelper,
        IKiotVietHelper kiotVietHelper,
        IOdooHelper odooHelper)
    {
        _itemsRepository = itemsRepository;
        _syncConfigRepository = syncConfigRepository;
        _nhanhHelper = nhanhHelper;
        _sapoHelper = sapoHelper;
        _kiotVietHelper = kiotVietHelper;
        _odooHelper = odooHelper;
    }

    public async Task<Result<SyncServiceConfig>> SaveConfig(SyncServiceConfigDto dto)
    {
        return dto.SyncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.SaveNhanhConfig(dto),
            SyncServiceEnum.Sapo => await _sapoHelper.SaveSapoConfig(dto),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.SaveKiotVietConfig(dto),
            SyncServiceEnum.Odoo => await _odooHelper.SaveOdooConfig(dto),
            _ => Result<SyncServiceConfig>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<SyncServiceConfig> GetConfig(SyncServiceEnum syncService, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.GetNhanhConfig(shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.GetSapoConfig(shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.GetKiotVietConfig(shopId),
            SyncServiceEnum.Odoo => await _odooHelper.GetOdooConfig(shopId),
            _ => throw new ArgumentException("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> DeleteConfig(SyncServiceEnum syncService, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.DeleteNhanhConfig(shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.DeleteSapoConfig(shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.DeleteKiotVietConfig(shopId),
            SyncServiceEnum.Odoo => await _odooHelper.DeleteOdooConfig(shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> SyncProductFromWebhook(SyncServiceEnum syncService, object productData, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.SyncNhanhProductFromWebhook(productData, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.SyncSapoProductFromWebhook(productData, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.SyncKiotVietProductFromWebhook(productData, shopId),
            SyncServiceEnum.Odoo => await _odooHelper.SyncOdooProductFromWebhook(productData, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> SyncOrderFromWebhook(SyncServiceEnum syncService, object orderData, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.SyncNhanhOrderFromWebhook(orderData, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.SyncSapoOrderFromWebhook(orderData, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.SyncKiotVietOrderFromWebhook(orderData, shopId),
            SyncServiceEnum.Odoo => await _odooHelper.SyncOdooOrderFromWebhook(orderData, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> SyncCustomerFromWebhook(SyncServiceEnum syncService, object customerData, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.SyncNhanhCustomerFromWebhook(customerData, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.SyncSapoCustomerFromWebhook(customerData, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.SyncKiotVietCustomerFromWebhook(customerData, shopId),
            SyncServiceEnum.Odoo => await _odooHelper.SyncOdooCustomerFromWebhook(customerData, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> DeleteProductsFromWebhook(SyncServiceEnum syncService, object productIds, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.DeleteNhanhProductsFromWebhook(productIds, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.DeleteSapoProductsFromWebhook(productIds),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.DeleteKiotVietProductsFromWebhook(productIds),
            SyncServiceEnum.Odoo => await _odooHelper.DeleteOdooProductsFromWebhook(productIds),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> DeleteOrderFromWebhook(SyncServiceEnum syncService, object orderData, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.DeleteNhanhOrderFromWebhook(orderData, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.DeleteSapoOrderFromWebhook(orderData, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.DeleteKiotVietOrderFromWebhook(orderData, shopId),
            SyncServiceEnum.Odoo => await _odooHelper.DeleteOdooOrderFromWebhook(orderData, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> CreateOrderToExternalService(SyncServiceEnum syncService, Order order, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.CreateOrderToNhanh(order, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.UpdateOrderToSapo(order, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.UpdateOrderToKiotViet(order, shopId),
            SyncServiceEnum.Odoo => await _odooHelper.UpdateOrderToOdoo(order, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<SyncServiceConfig>> UpdateAccessCode(UpdateAccessCodeDto dto)
    {
        return dto.SyncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.UpdateNhanhAccessCode(dto.ShopId, dto.AccessCode),
            SyncServiceEnum.Sapo => await _sapoHelper.UpdateSapoAccessCode(dto.ShopId, dto.AccessCode),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.UpdateKiotVietAccessCode(dto.ShopId, dto.AccessCode),
            SyncServiceEnum.Odoo => await _odooHelper.UpdateOdooAccessCode(dto.ShopId, dto.AccessCode),
            _ => Result<SyncServiceConfig>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }


    public async Task<SyncServiceConfig> FindByBusinessId(int businessId)
    {
        return await _syncConfigRepository.FindByBusinessId(businessId, SyncServiceEnum.NhanhVN);
    }
}
