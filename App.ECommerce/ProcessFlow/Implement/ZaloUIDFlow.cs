using System;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using log4net;
using Newtonsoft.Json;

namespace App.ECommerce.ProcessFlow.Implement;

public class ZaloUIDFlow : IZaloUIDFlow
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(ZaloUIDFlow));
    private readonly IPartnerRepository _partnerRepository;
    private readonly IShopSettingRepository _shopSettingRepository;
    private readonly IZaloRepository _zaloRepository;
    private readonly IZalo_TypeMessageRepository _zalo_TypeMessageRepository;
    private readonly IPartner_Balance_LogRepository _partnerBalanceLogRepository;
    private readonly ITriggerEventRepository _triggerEventRepository;
    private readonly ITriggerEventHistoryRepository _triggerEventHistoryRepository;
    private readonly IUserRepository _userRepository;

    public ZaloUIDFlow(
        IPartnerRepository partnerRepository,
        IShopSettingRepository shopSettingRepository,
        IZaloRepository zaloRepository,
        IZalo_TypeMessageRepository zalo_TypeMessageRepository,
        IPartner_Balance_LogRepository partnerBalanceLogRepository,
        ITriggerEventRepository triggerEventRepository,
        ITriggerEventHistoryRepository triggerEventHistoryRepository,
        IUserRepository userRepository
    )
    {
        _partnerRepository = partnerRepository;
        _shopSettingRepository = shopSettingRepository;
        _zaloRepository = zaloRepository;
        _zalo_TypeMessageRepository = zalo_TypeMessageRepository;
        _partnerBalanceLogRepository = partnerBalanceLogRepository;
        _triggerEventRepository = triggerEventRepository;
        _triggerEventHistoryRepository = triggerEventHistoryRepository;
        _userRepository = userRepository;
    }

    public async Task ProcessMessageAsync(string requestId, Zalo_Template objTemplate, TriggerEventHistory objSend)
    {
        try
        {
            // #if DEBUG
            //     _log.Info($"{requestId} ProcessMessageAsync DEBUG");

            //     return;
            // #else
            _log.Info($"{requestId} ZaloUIDFlow ProcessMessageAsync START");

            var objPartner = await _partnerRepository.FindByPartnerId(objSend.PartnerId);
            _log.Info($"{requestId} ZaloUIDFlow ProcessMessageAsync objPartner: {JsonConvert.SerializeObject(objPartner)}");
            if (objPartner == null || objPartner.Balance <= 0)
            {
                objSend.Status = TriggerEventHistoryStatusEnum.Failed;
                objSend.Description = $"Hết hạn mức";

                await _triggerEventHistoryRepository.UpdateHistory(objSend);

                _log.Info($"{requestId} ZaloUIDFlow ProcessMessageAsync PartnerId: {objSend.PartnerId} has insufficient balance or not found.");

                return;
            }

            var objTypeMessage = await _zalo_TypeMessageRepository.GetByTypeMessage(objTemplate.Type);
            var objOA = _shopSettingRepository.FindByShopId(objSend.ShopId);

            _log.Info($"{requestId} ZaloUIDFlow ProcessMessageAsync MessageType: {objTemplate.Type} | objOA: {JsonConvert.SerializeObject(objOA)}");

            if (objOA != null)
            {
                var objRequest = JsonConvert.DeserializeObject<Zalo_Transaction>(objSend.Request);

                var objQuota = await _zaloRepository.GetUserMessageQuota(requestId, objOA, objRequest.Recipient.UserId);
                _log.Info($"{requestId} ZaloUIDFlow ProcessMessageAsync MessageType: {objTemplate.Type} | Quota: {JsonConvert.SerializeObject(objQuota)}");

                SendMessageUIDInputDto obj = new SendMessageUIDInputDto()
                {
                    requestId = requestId,
                    objTemplate = objTemplate,
                    objSend = objSend,
                    objOA = objOA,
                    objQuota = objQuota,
                };

                Zalo_MessageResponseDto objResponse = await SendMessageWithRetryAsync(obj);

                if (objResponse != null)
                {
                    if (objResponse.error == 0)
                        objSend.Status = TriggerEventHistoryStatusEnum.Success;
                    else
                    {
                        objSend.Status = TriggerEventHistoryStatusEnum.Failed;
                        objSend.Description = objResponse.message;
                    }

                    objSend.Responses = JsonConvert.SerializeObject(objResponse);

                    await UpdateMessageStatusAsync(requestId, objSend, objResponse, objPartner, objTypeMessage);
                }
                else
                {
                    objSend.Status = TriggerEventHistoryStatusEnum.Failed;
                    await _triggerEventHistoryRepository.UpdateHistory(objSend);
                }
            }
            else
                _log.Info($"{requestId} ZaloUIDFlow ProcessMessageAsync MessageType: {objTemplate.Type} | objOA: null");
            // #endif
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} ZaloUIDFlow ProcessMessageAsync: {ex.Message}", ex);

            objSend.Status = TriggerEventHistoryStatusEnum.Failed;

            await _triggerEventHistoryRepository.UpdateHistory(objSend);
        }
    }

    private async Task<Zalo_MessageResponseDto> SendMessageWithRetryAsync(SendMessageUIDInputDto obj)
    {
        while (obj.objSend.RetryCount < CommonConst.MAX_RETRY_COUNT)
        {
            try
            {
                var objRequest = JsonConvert.DeserializeObject<Zalo_Transaction>(obj.objSend.Request);

                if (obj.objTemplate.Type == Zalo_Template_Type_Enum.Promotion)
                {
                    if (obj.objQuota?.data?.promotion?.daily_remain <= 0)
                    {
                        _log.Info($"{obj.requestId} SendMessageWithRetryAsync MessageType: {obj.objTemplate.Type} | daily_remain <= 0");

                        obj.objSend.Status = TriggerEventHistoryStatusEnum.Failed;
                        obj.objSend.Description = $"Hết hạn mức";

                        await _triggerEventHistoryRepository.UpdateHistory(obj.objSend);

                        return null;
                    }

                    return await _zaloRepository.SendPromotionMessage(obj.requestId, obj.objOA, objRequest);
                }

                if (obj.objTemplate.Type == Zalo_Template_Type_Enum.Consultation)
                    return await _zaloRepository.SendConsultation(obj.requestId, obj.objOA, objRequest);

                if (obj.objTemplate.Type == Zalo_Template_Type_Enum.Transaction)
                    return await _zaloRepository.SendTransactionMessage(obj.requestId, obj.objOA, objRequest);
            }
            catch (Exception ex)
            {
                _log.Error($"{obj.requestId} SendMessageWithRetryAsync Retry {obj.objSend.RetryCount + 1}/{CommonConst.MAX_RETRY_COUNT} failed: {ex.Message}", ex);
                obj.objSend.RetryCount++;
                await Task.Delay(1000 * obj.objSend.RetryCount);
            }
        }
        return null;
    }

    private async Task UpdateMessageStatusAsync(string requestId, TriggerEventHistory objSend, Zalo_MessageResponseDto objResponse, Partner objPartner, Zalo_TypeMessage objTypeMessage)
    {
        if (objResponse.error == 0)
        {
            await UpdateBalance(requestId, objSend, objPartner, objTypeMessage);
        }

        objSend.SetModifiedInfo(objSend.ShopId);

        await _triggerEventHistoryRepository.UpdateHistory(objSend);
    }

    private async Task UpdateBalance(string requestId, TriggerEventHistory objSend, Partner objPartner, Zalo_TypeMessage objTypeMessage)
    {
        try
        {
            var objRequest = JsonConvert.DeserializeObject<Zalo_Transaction>(objSend.Request);

            objPartner.Balance -= objTypeMessage.Cost;
            _partnerRepository.UpdatePartnerBalance(objPartner.PartnerId, objPartner.Balance);

            var objTrigger = await _triggerEventRepository.GetTriggerEventInfo(objSend.TriggerEventId);
            var objUser = _userRepository.FindByZaloId(objRequest?.Recipient?.UserId);

            string eventName = objTrigger?.EventName ?? "Không xác định";
            string userFullname = objUser?.Fullname ?? "Không xác định";

            var objLog = new Partner_Balance_Log
            {
                PartnerId = objSend.PartnerId,
                RefType = PartnerBalanceRefEnum.UIDFee,
                RefId = objSend.Id.ToString(),
                Type = PartnerBalanceEnum.Deduction,
                Amount = objTypeMessage.Cost,
                BalanceAfterTransaction = objPartner.Balance,
                Message = $"Sự kiện {eventName} gửi tin nhắn Zalo UID cho tới {userFullname}",
                Status = PartnerBalanceStatus.Success
            };

            await _partnerBalanceLogRepository.Create(objLog);
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} UpdateBalance: {ex.Message}", ex);
        }
    }       
}