using App.ECommerce.Models.Webhooks;
using Microsoft.AspNetCore.Mvc;
using App.ECommerce.Repository.Entities;
using Microsoft.Extensions.Localization;
using App.ECommerce.Units.Enums;
using App.ECommerce.Setting;
using App.ECommerce.Resource.Dtos.InputDtos;
using AutoMapper;
using App.ECommerce.Resource.Dtos.Webhooks;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Interface;
using Newtonsoft.Json.Linq;
using App.Base.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using System.Security.Cryptography;
using System.Text;
using App.ECommerce.Resource.Dtos.SapoOmniDtos;
using Newtonsoft.Json;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Route(RoutePrefix.WEBHOOK)]
[ApiExplorerSettings(GroupName = "webhook-v1")]
public class ThirdPartySapoOmniController : BaseController
{
    private readonly IMapper _mapper;
    private readonly IOrderFlow _orderFlow;
    private readonly ISapoOmniFlow _sapoOmniFlow;
    private readonly IItemsRepository _itemsRepository;
    private readonly ICategoryRepository _categoryRepository;
    private readonly ISapoOmniConfigRepository _sapoOmniRepository;
    private readonly IConfiguration _configuration;

    public ThirdPartySapoOmniController(
        IStringLocalizer localizer,
        IMapper mapper,
        IOrderFlow orderFlow,
        ISapoOmniFlow sapoOmniFlow,
        IItemsRepository itemsRepository,
        ICategoryRepository categoryRepository,
        ISapoOmniConfigRepository sapoOmniRepository,
        IConfiguration configuration
    ) : base(localizer)
    {
        _mapper = mapper;
        _categoryRepository = categoryRepository;
        _orderFlow = orderFlow;
        _sapoOmniFlow = sapoOmniFlow;
        _itemsRepository = itemsRepository;
        _sapoOmniRepository = sapoOmniRepository;
        _configuration = configuration;
    }

    [HttpPost]
    public async Task<IActionResult> HandleSapoOmniEvent([FromBody] object rawPayload)
    {
        string webhookId = Guid.NewGuid().ToString();
        string shopDomain = "";
        string topic = "";

        try
        {
            // Lấy thông tin từ SapoOmni headers
            shopDomain = Request.Headers["X-Sapo-Tenant-Domain"].FirstOrDefault() ?? "";
            topic = Request.Headers["X-Sapo-Topic"].FirstOrDefault() ?? "";
            webhookId = Request.Headers["X-Sapo-Webhook-Id"].FirstOrDefault() ??
                       Request.Headers["X-Request-Id"].FirstOrDefault() ??
                       webhookId;

            var normalizedTopic = NormalizeTopic(topic);

            // Log webhook info
            LogEvent(new EventLogDto
            {
                RefId = webhookId,
                RefType = TypeFor.Other,
                Action = LogActionEnum.Webhook,
                Status = LogStatusEnum.Info,
                ActionAPI = $"{RoutePrefix.WEBHOOK}/sapo-omni",
                Message = $"SapoOmni webhook received - Topic: {topic}, Domain: {shopDomain}",
                DataObject = new
                {
                    Headers = new { TenantDomain = shopDomain, Topic = topic, WebhookId = webhookId },
                    PayloadType = rawPayload?.GetType()?.Name
                }
            });

            // Validate headers
            if (string.IsNullOrEmpty(shopDomain))
                return Ok(WebhookResponseDto.CreateErrorResponse("MISSING_DOMAIN", "X-Sapo-Tenant-Domain header is required"));

            if (string.IsNullOrEmpty(topic))
                return Ok(WebhookResponseDto.CreateErrorResponse("MISSING_TOPIC", "X-Sapo-Topic header is required"));

            // Tìm config
            var sapoOmniConfig = await _sapoOmniRepository.FindByDomainApi(shopDomain);
            if (sapoOmniConfig == null)
            {
                LogEvent(new EventLogDto
                {
                    RefId = webhookId,
                    RefType = TypeFor.Other,
                    Action = LogActionEnum.Webhook,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.WEBHOOK}",
                    Message = $"SapoOmni configuration not found for domain: {shopDomain}",
                    DataObject = new { RequestedDomain = shopDomain, Topic = topic }
                });
                return Ok(WebhookResponseDto.CreateErrorResponse("CONFIG_NOT_FOUND", $"Configuration not found for domain: {shopDomain}"));
            }

            // Parse topic
            if (!Enum.TryParse<SapoOmniWebhookTopic>(normalizedTopic, true, out var topicType))
            {
                LogEvent(new EventLogDto
                {
                    RefId = webhookId,
                    Status = LogStatusEnum.Error,
                    Message = $"Unknown topic: {topic}",
                    DataObject = new { OriginalTopic = topic, NormalizedTopic = normalizedTopic }
                });
                return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_TOPIC", $"Unknown topic: {topic}"));
            }

            // Convert rawPayload to JToken for processing
            var jsonPayload = JToken.FromObject(rawPayload);

            // Xử lý từng loại webhook
            switch (topicType)
            {
                case SapoOmniWebhookTopic.products_create:
                case SapoOmniWebhookTopic.products_update:
                    var productData = jsonPayload.ToObject<SapoOmniProductDto>();
                    if (productData == null)
                    {
                        LogEvent(new EventLogDto
                        {
                            RefId = webhookId,
                            Status = LogStatusEnum.Error,
                            Message = "Failed to convert payload to SapoOmniProductWebhookDto",
                            DataObject = jsonPayload.ToString(Formatting.Indented)
                        });
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Product data is invalid"));
                    }

                    var productResult = await _sapoOmniFlow.AddOrUpdateProductFromSapoOmniWebhook(
                        productData, sapoOmniConfig.DomainApi, sapoOmniConfig.AccessToken);

                    return productResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("PRODUCT_ERROR", string.Join(", ", productResult.Errors)));

                case SapoOmniWebhookTopic.products_delete:
                    var productDeleteData = jsonPayload.ToObject<SapoOmniProductWebhookDto>();
                    if (productDeleteData == null || productDeleteData.Id == 0)
                    {
                        LogEvent(new EventLogDto
                        {
                            RefId = webhookId,
                            Status = LogStatusEnum.Error,
                            Message = "Product ID not found in delete payload",
                            DataObject = jsonPayload.ToString(Formatting.Indented)
                        });
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Product ID not found"));
                    }

                    var deleteResult = await _sapoOmniFlow.DeleteProductFromSapoOmniWebhook(sapoOmniConfig.ShopId, productDeleteData.Id);

                    return deleteResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("PRODUCT_DELETE_ERROR", string.Join(", ", deleteResult.Errors)));

                case SapoOmniWebhookTopic.orders_create:
                case SapoOmniWebhookTopic.orders_update:
                    var orderData = jsonPayload.ToObject<SapoOmniOrderDto>();
                    if (orderData == null)
                    {
                        LogEvent(new EventLogDto
                        {
                            RefId = webhookId,
                            Status = LogStatusEnum.Error,
                            Message = "Failed to convert payload to SapoOmniOrderWebhookDto",
                            DataObject = jsonPayload.ToString(Formatting.Indented)
                        });
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Order data is invalid"));
                    }

                    var orderResult = await _sapoOmniFlow.AddOrUpdateOrderFromSapoOmniWebhook(
                        orderData, sapoOmniConfig.DomainApi, sapoOmniConfig.AccessToken);

                    return orderResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("ORDER_ERROR", string.Join(", ", orderResult.Errors)));

                case SapoOmniWebhookTopic.orders_cancelled:
                case SapoOmniWebhookTopic.orders_delete:
                    var orderDeleteData = jsonPayload.ToObject<SapoOmniOrderWebhookDto>();
                    if (orderDeleteData?.Id == null || orderDeleteData.Id == 0)
                    {
                        LogEvent(new EventLogDto
                        {
                            RefId = webhookId,
                            Status = LogStatusEnum.Error,
                            Message = "Order ID not found in delete payload",
                            DataObject = jsonPayload.ToString(Formatting.Indented)
                        });
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Order ID not found"));
                    }

                    var orderDeleteResult = await _sapoOmniFlow.DeleteOrderFromSapoOmniWebhook(sapoOmniConfig.ShopId,
                        orderDeleteData.Id, sapoOmniConfig.DomainApi);

                    return orderDeleteResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("ORDER_DELETE_ERROR", string.Join(", ", orderDeleteResult.Errors)));

                case SapoOmniWebhookTopic.inventory_levels_update:
                    var inventoryData = jsonPayload.ToObject<SapoOmniInventoryWebhookDto>();
                    if (inventoryData == null)
                    {
                        LogEvent(new EventLogDto
                        {
                            RefId = webhookId,
                            Status = LogStatusEnum.Error,
                            Message = "Failed to convert payload to SapoOmniInventoryWebhookDto",
                            DataObject = jsonPayload.ToString(Formatting.Indented)
                        });
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Inventory data is invalid"));
                    }

                    var inventoryResult = await _sapoOmniFlow.UpdateInventoryFromSapoOmniWebhook(
                        inventoryData, sapoOmniConfig.DomainApi, sapoOmniConfig.AccessToken);

                    return inventoryResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("INVENTORY_ERROR", string.Join(", ", inventoryResult.Errors)));

                case SapoOmniWebhookTopic.customers_create:
                case SapoOmniWebhookTopic.customers_update:
                    var customerData = jsonPayload.ToObject<SapoOmniCustomerDto>();
                    if (customerData == null)
                    {
                        LogEvent(new EventLogDto
                        {
                            RefId = webhookId,
                            Status = LogStatusEnum.Error,
                            Message = "Failed to convert payload to SapoOmniCustomerWebhookDto",
                            DataObject = jsonPayload.ToString(Formatting.Indented)
                        });
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Customer data is invalid"));
                    }

                    var customerResult = await _sapoOmniFlow.AddOrUpdateCustomerFromSapoOmniWebhook(
                        customerData, sapoOmniConfig.DomainApi, sapoOmniConfig.AccessToken);

                    return customerResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("CUSTOMER_ERROR", string.Join(", ", customerResult.Errors)));

                default:
                    LogEvent(new EventLogDto
                    {
                        RefId = webhookId,
                        Status = LogStatusEnum.Warning,
                        Message = $"Unhandled webhook topic: {topicType}",
                        DataObject = new { Topic = topic, NormalizedTopic = normalizedTopic }
                    });
                    return Ok(WebhookResponseDto.CreateSuccessResponse());
            }
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = webhookId,
                RefType = TypeFor.Other,
                Action = LogActionEnum.Webhook,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.WEBHOOK}",
                Message = $"Webhook processing error: {ex.Message}",
                Exception = ex,
                DataObject = new
                {
                    Headers = new { TenantDomain = shopDomain, Topic = topic },
                    RawPayload = rawPayload
                }
            });
            return Ok(WebhookResponseDto.CreateErrorResponse("INTERNAL_ERROR", "Internal server error"));
        }
    }

    private string NormalizeTopic(string topic)
    {
        if (string.IsNullOrEmpty(topic)) return "";

        return topic
            .ToLower()
            .Replace("/", "_")
            .Replace("-", "_")
            .Replace(".", "_")
            .Replace(" ", "_");


    }
}