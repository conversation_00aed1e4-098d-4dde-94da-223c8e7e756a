using App.Base.Middleware;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Dtos.ResultDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Enums;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using static App.ECommerce.Resource.Enums.AffiliationEnum;

using Action = App.ECommerce.Resource.Model.Action;

namespace App.ECommerce.Controllers.API.Affiliate
{
    [ApiController]
    [Produces("application/json")]
    [Route(RoutePrefix.API_AFFILIATION)]
    [ApiExplorerSettings(GroupName = "affiliation-v1")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [MultiPolicysAuthorize(Policys = RolePrefix.User)]
    public class AffiliationUserController : BaseController
    {
        private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(AffiliationUserController));
        private readonly IAffiliationPartnerRepository _affiliationPartnerRepository;
        private readonly IPartnerRepository _partnerRepository;
        private readonly IUserRepository _userRepository;
        private readonly IShopRepository _shopRepository;
        private readonly ICommissionsConfigRepository _commissionsConfigRepository;
        private readonly ICommissionsReportRepository _commissionsReportRepository;
        private readonly IOrderRepository _orderRepository;
        private readonly ICommissionsConfigHelpers _commissionsConfigHelpers;

        public AffiliationUserController(IStringLocalizer localizer,
            IAffiliationPartnerRepository affiliationPartner,
            IPartnerRepository partnerRepository,
            IShopRepository shopRepository,
            ICommissionsConfigRepository commissionsConfigRepository,
            IUserRepository userRepository,
            ICommissionsReportRepository commissionsReportRepository,
            IOrderRepository orderRepository,
            ICommissionsConfigHelpers commissionsConfigHelpers
            )
            : base(localizer)
        {
            _affiliationPartnerRepository = affiliationPartner;
            _partnerRepository = partnerRepository;
            _shopRepository = shopRepository;
            _commissionsConfigRepository = commissionsConfigRepository;
            _userRepository = userRepository;
            _commissionsReportRepository = commissionsReportRepository;
            _orderRepository = orderRepository;
            _commissionsConfigHelpers = commissionsConfigHelpers;
        }

        public class RegisterAffiliateInputDto
        {
            public string FullName { get; set; }
            public string Email { get; set; }
            public string PhoneNumber { get; set; }
            public string IdentityCardNumber { get; set; }
            public string BankName { get; set; }
            public string BankAccountNumber { get; set; }
            public string BankAccountName { get; set; }

            public string? ReferrerCode { get; set; }
            public string? TaxCode { get; set; }
        }
        /// <summary>
        /// User đăng ký trở thành đối tác
        /// </summary>
        /// <param name="model"></param>
        /// <returns>Result list of user register Affiliate</returns>
        // GET: api/affiliation/users
        [HttpPost("registerAffiliate")]
        public async Task<IActionResult> RegisterAffiliate([FromBody] RegisterAffiliateInputDto model)
        {
            string requestId = Guid.NewGuid().ToString();
            try
            {

                string userId = GetUserIdAuth();
                User? user = _userRepository.FindByUserId(userId);
                if (user == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                        this.ControllerContext));

                Shop? shop = _shopRepository.FindByShopId(user.ShopId);
                if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

                if (user.AffiliationStatus == AffiliationTypeStatus.Actived)
                {
                    return ResponseUnauthorized(new CustomBadRequest("User is already partner",
                                           this.ControllerContext));

                }
                if (!string.IsNullOrWhiteSpace(model.ReferrerCode))
                {
                    User? referrer = _userRepository.FindByReferralCode(model.ReferrerCode, user.ShopId);
                    if (referrer == null || referrer.UserId == user.UserId) return ResponseBadRequest(new CustomBadRequest(localizer("REFERRER_CODE_INVALID"), this.ControllerContext));

                    // Thêm kiểm tra vòng lặp
                    if (_affiliationPartnerRepository.HasReferralCycle(user.UserId, referrer.UserId))
                    {
                        return ResponseBadRequest(new CustomBadRequest(localizer("REFERRER_CODE_INVALID_CYCLE"), this.ControllerContext));
                    }

                    user.ReferrerCode = model.ReferrerCode;
                    user.ParentId = referrer.UserId;
                }

                user.Fullname = model.FullName;
                user.AffiliationFullName = model.FullName;
                user.AffiliationEmail = model.Email;
                user.AffiliationPhoneNumber = model.PhoneNumber;
                user.BankName = model.BankName;
                user.BankAccountName = model.BankAccountName;
                user.BankAccountNumber = model.BankAccountNumber;
                user.IdentityCardNumber = model.IdentityCardNumber;
                user.PaymentType = TypePayment.Transfer;
                if (!string.IsNullOrWhiteSpace(model.TaxCode)) user.TaxCode = model.TaxCode;
                user.AffiliationStatus = AffiliationTypeStatus.InActived;
                var commissionsConfig = await _commissionsConfigRepository.FindCommissionsConfig(user.ShopId);
                if (commissionsConfig?.AdvancedCommissionsConfig?.IsAutoApproved == true)
                {
                    var canAfford = await _affiliationPartnerRepository.CanUserAffordPurchaseAsync(user.UserId, user.ShopId);
                    if (canAfford)
                    {
                        user.AffiliationStatus = AffiliationTypeStatus.Actived;
                        user.ApprovalDate = DateTime.Now;
                        user.AffiliationExpireDate = _commissionsConfigHelpers.CalculateExpirationDate(commissionsConfig?.AdvancedCommissionsConfig?.PartnerCommExpiry ?? 1, user.ApprovalDate ?? DateTime.Now);
                    }
                }
                user = _userRepository.UpdateUser(user);

                return ResponseData(new { data = user.AffiliationStatus });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.User,
                    RequestId = requestId,
                    Action = LogActionEnum.Create,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationuser/registerAffiliate",
                    Message = $"Error user RegisterAffiliate",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/User", ex, model);
            }
        }

        /// <summary>
        /// Get affiliate report for a partner (Lấy báo cáo nhanh của đối tác)
        /// </summary>
        /// <returns>The quick report data</returns>
        // GET: api/affiliation/affiliationuser/
        [HttpGet("affiliateReport")]
        public async Task<IActionResult> AffiliateReport()
        {
            string requestId = Guid.NewGuid().ToString();
            try
            {
                string userId = GetUserIdAuth();
                User? user = _userRepository.FindByUserId(userId);
                if (user == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

                var quickReport = await _affiliationPartnerRepository.GetUserAffiliateReport(userId, user.ShopId);

                return ResponseData(new { data = quickReport });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.User,
                    RequestId = requestId,
                    Action = LogActionEnum.Create,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationuser/affiliateReport",
                    Message = $"Error user get AffiliateReport",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/affiliationuser/affiliateReport", ex);
            }
        }

        public class MyTeamInputDto
        {
            public string? Search { get; set; }
            public TypeMyTeam? Type { get; set; }

        }

        /// <summary>
        /// Get my team member
        /// </summary>
        /// <returns>Return MyTeam data</returns>
        // GET: pi/affiliation/MyTeam
        [HttpGet("MyTeam")]
        public async Task<IActionResult> MyTeam([FromQuery] MyTeamInputDto model)
        {
            string requestId = Guid.NewGuid().ToString();
            try
            {
                string userId = GetUserIdAuth();
                User? user = _userRepository.FindByUserId(userId);
                if (user == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));


                var result = await _affiliationPartnerRepository.GetMyTeam(user: user, search: model.Search, type: model.Type);

                return ResponseData(new { data = result });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.User,
                    RequestId = requestId,
                    Action = LogActionEnum.Load,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationuser/MyTeam",
                    Message = $"Error User Get MyTeam",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/affiliationuser/affiliateReport", ex);
            }
        }

        public class MyCommissionInputDto
        {
            public TypeCommission Type { get; set; }

        }

        /// <summary>
        /// Get my commission
        /// </summary>
        /// <returns>Return MyCommission data</returns>
        // GET: /api/affiliation/affiliationuser/MyCommission
        [HttpGet("MyCommission")]
        public async Task<IActionResult> MyCommission([FromQuery] MyCommissionInputDto model)
        {
            string requestId = Guid.NewGuid().ToString();
            try
            {
                string userId = GetUserIdAuth();
                User? user = _userRepository.FindByUserId(userId);
                if (user == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
                var result = await _affiliationPartnerRepository.GetUserListCommission(user: user, type: model.Type);

                return ResponseData(new { data = result });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.User,
                    RequestId = requestId,
                    Action = LogActionEnum.Load,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationuser/MyCommissionv",
                    Message = $"Error User Get MyCommission",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/affiliationuser/MyCommission", ex);
            }
        }

        /// <summary>
        /// Get list commission report (chỉ những report đã được thanh toán - Paid status)
        /// </summary>
        /// <returns>Return MyCommission List Commission report (Paid only)</returns>
        // GET: /api/affiliation/affiliationuser/MyCommissionReport
        [HttpGet("MyCommissionReport")]
        public async Task<IActionResult> MyCommissionReport([FromQuery] int skip = 0, [FromQuery] int limit = 99)
        {
            string requestId = Guid.NewGuid().ToString();
            try
            {
                string userId = GetUserIdAuth();
                User? user = _userRepository.FindByUserId(userId);
                if (user == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
                // Cấu hình thông tin phân trang
                Paging paging = new Paging()
                {
                    Search = "",
                    PageIndex = skip / (limit == 0 ? 1 : limit),
                    PageSize = limit,
                    NameType = TypeSortName.Created,
                    SortType = TypeSort.desc,
                };
                var result = _commissionsReportRepository.ListUserCommissionReport(paging, user.UserId);
                List<CommissionsReportUserDto> listCRDto = _mapper.Map<List<CommissionsReportUserDto>>(result.Result);

                return ResponseData(new
                {
                    data = listCRDto,
                    skip,
                    limit,
                    total = result.Total
                });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.User,
                    RequestId = requestId,
                    Action = LogActionEnum.Create,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationuser/MyCommissionReport",
                    Message = $"Error User Get MyCommissionReport",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/affiliationuser/MyCommissionReport", ex);
            }
        }

        /// <summary>
        /// Get list order
        /// </summary>
        /// <returns>Return ListMyOrders</returns>
        // GET: /api/affiliation/affiliationuser/MyOrders
        [HttpGet("MyOrders")]
        public async Task<IActionResult> MyOrders([FromQuery] int skip = 0, [FromQuery] int limit = 99)
        {
            string requestId = Guid.NewGuid().ToString();
            try
            {
                string userId = GetUserIdAuth();
                User? user = _userRepository.FindByUserId(userId);
                if (user == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
                // Cấu hình thông tin phân trang
                Paging paging = new Paging()
                {
                    Search = "",
                    PageIndex = skip / (limit == 0 ? 1 : limit),
                    PageSize = limit,
                    NameType = TypeSortName.Created,
                    SortType = TypeSort.desc,
                };
                var listSubUserIds = await _affiliationPartnerRepository.ListSubUserIds(user);
                var subUserIds = listSubUserIds.f1UserIds.Concat(listSubUserIds.f2UserIds).ToList();
                var result = await _orderRepository.ListOrderSuccess(paging, userId, subUserIds, user.ApprovalDate);
                List<OrderUserAffiliateDto> listOrderDto = _mapper.Map<List<OrderUserAffiliateDto>>(result.Result);

                return ResponseData(new
                {
                    data = listOrderDto,
                    skip,
                    limit,
                    total = result.Total
                });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.User,
                    RequestId = requestId,
                    Action = LogActionEnum.Create,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationuser/MyOrders",
                    Message = $"Error User Get MyOrders",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/affiliationuser/MyOrders", ex);
            }
        }

        /// <summary>
        /// check IsCommissionActive
        /// </summary>
        /// <returns>Return result of IsCommissionActive</returns>
        [AllowAnonymous]
        [HttpGet("IsCommissionActive")]
        public async Task<IActionResult> IsCommissionActive(string shopId)
        {

            string requestId = Guid.NewGuid().ToString();
            try
            {
                object data = new
                {
                    IsActive = false
                };
                var commissionConfig = await _commissionsConfigRepository.FindCommissionsConfig(shopId);
                if (commissionConfig == null || commissionConfig.IsActive == false)
                {
                    return ResponseData(new
                    {
                        data
                    });
                }
                data = new
                {
                    IsActive = true

                };


                return ResponseData(new
                {
                    data
                });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.User,
                    RequestId = requestId,
                    Action = LogActionEnum.Create,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationuser/IsCommissionActive",
                    Message = $"Error User Get IsCommissionActive",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/affiliationuser/IsCommissionActive", ex);
            }
        }

        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> GetCommissionsConfig([FromQuery] string shopId)
        {
            string requestId = Guid.NewGuid().ToString();
            try
            {
                // string userId = GetUserIdAuth();
                // User? user = _userRepository.FindByUserId(userId);

                // if (user == null)
                //     return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

                Shop? shop = _shopRepository.FindByShopId(shopId);

                if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
                // if (user.ShopId != shop.ShopId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

                var commissionsConfig = await _commissionsConfigRepository.FindCommissionsConfig(shopId);

                // Nếu không có commissionsConfig thì tạo mới
                if (commissionsConfig == null)
                {
                    commissionsConfig = new CommissionsConfig
                    {
                        CommissionsConfigId = Guid.NewGuid().ToString(),
                        ShopId = shopId,
                        BasicCommissionsConfig = new BasicCommissionsConfig(),
                        AdvancedCommissionsConfig = new AdvancedCommissionsConfig(),
                        IsActive = false
                    };

                    _commissionsConfigRepository.InsertCommissionsConfig(commissionsConfig);
                }

                var responseConmissionsConfig = _mapper.Map<CommissionsConfigResultDto>(commissionsConfig);

                return ResponseData(new { data = responseConmissionsConfig });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.User,
                    RequestId = requestId,
                    Action = LogActionEnum.Load,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/commissionsconfig/{shopId}",
                    Message = $"Error User GetCommissionsConfig",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/commissionsConfig/CommissionsConfig/{shopId}", ex);
            }
        }
    }
}
