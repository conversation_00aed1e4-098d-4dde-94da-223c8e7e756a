using App.Base.Middleware;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Dtos.ViettelInvoiceDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Enums.Invoice;
using App.ECommerce.ProcessFlow.Interface;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class PriceListPartnerController : BaseController
{
    private readonly ILog _log4net = LogManager.GetLogger(typeof(PriceListPartnerController));
    private readonly IPriceListRepository _priceListRepository;
    private readonly IShopRepository _shopRepository;
    private readonly IBaseFlow _baseFlow;
    private readonly IPriceListFlow _priceListFlow;

    public PriceListPartnerController(
        IStringLocalizer localizer,
        IPriceListRepository priceListRepository,
        IShopRepository shopRepository,
        IBaseFlow baseFlow,
        IPriceListFlow priceListFlow
    ) : base(localizer)
    {
        _priceListRepository = priceListRepository;
        _shopRepository = shopRepository;
        _baseFlow = baseFlow;
        _priceListFlow = priceListFlow;
    }

    /// <summary>
    /// Get list pricelists (Lấy danh sách bảng giá)
    /// </summary>
    /// <param name="obj">Thông tin tìm kiếm</param>
    /// <returns>Result list price lists</returns>
    [HttpGet()]
    public async Task<IActionResult> GetListPriceLists([FromQuery] PriceListFilterDto obj)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), obj.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var priceLists = await _priceListRepository.GetAllAsync(obj);
            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = priceLists
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in GetListPriceLists: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Get price list detail (Lấy thông tin chi tiết bảng giá)
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="priceListId">ID của bảng giá</param>
    /// <returns>Result info price list</returns>
    [HttpGet("{shopId}/{priceListId}")]
    public async Task<IActionResult> GetInfoPriceListById(string shopId, string priceListId)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var priceList = await _priceListRepository.GetByIdAsync(shopId, priceListId);
            if (priceList == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = priceList
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in GetInfoPriceListById: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Create new price list (Tạo mới bảng giá)
    /// </summary>
    /// <param name="obj">Thông tin chi tiết bảng giá</param>
    /// <returns>Result info price list</returns>
    [HttpPost]
    public async Task<IActionResult> CreatePriceList([FromBody] PriceListInputDto obj)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), obj.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            if (obj.AdjustmentValue <= 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("BASE_MUST_NUMBER_POSITIVE"), this.ControllerContext));

            // Kiểm tra rank đã tồn tại trong bảng giá khác chưa
            var conflictingRanks = await _priceListFlow.ValidateRankConflicts(obj.ShopId, obj.AppliedRankIds);
            if (conflictingRanks.Any())
            {
                return ResponseBadRequest(new CustomBadRequest(
                    localizer("RANK_ALREADY_EXISTS_IN_PRICE_LIST"),
                    conflictingRanks,
                    this.ControllerContext
                ));
            }

            var objPriceList = _mapper.Map<PriceList>(obj);

            objPriceList.PriceListId = Guid.NewGuid().ToString();
            objPriceList.SetCreatedInfo(baseDto.Partner.PartnerId);

            var result = await _priceListRepository.CreateAsync(objPriceList);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = result
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in CreatePriceList: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Update price list (Cập nhật thông tin bảng giá)
    /// </summary>
    /// <param name="obj">Thông tin chi tiết bảng giá</param>
    /// <returns>Result update price list</returns>
    [HttpPut()]
    public async Task<IActionResult> UpdatePriceList([FromBody] PriceListInputDto obj)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), obj.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            if (obj.AdjustmentValue <= 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("BASE_MUST_NUMBER_POSITIVE"), this.ControllerContext));

            var existingPriceList = await _priceListRepository.GetByIdAsync(obj.ShopId, obj.PriceListId);
            if (existingPriceList == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            // Kiểm tra rank đã tồn tại trong bảng giá khác chưa (trừ bảng giá hiện tại)
            var conflictingRanks = await _priceListFlow.ValidateRankConflicts(obj.ShopId, obj.AppliedRankIds, obj.PriceListId);
            if (conflictingRanks.Any())
            {
                return ResponseBadRequest(new CustomBadRequest(
                    localizer("RANK_ALREADY_EXISTS_IN_PRICE_LIST"),
                    conflictingRanks,
                    this.ControllerContext
                ));
            }

            var objPriceList = _mapper.Map<PriceList>(obj);
            objPriceList.SetModifiedInfo(baseDto.Partner.PartnerId);

            await _priceListRepository.UpdateAsync(objPriceList);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = (object)null
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in UpdatePriceList: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Delete price list (Xóa bảng giá)
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="priceListId">ID của bảng giá</param>
    /// <returns>Result delete price list</returns>
    [HttpDelete("{shopId}/{priceListId}")]
    public async Task<IActionResult> DeletePriceList(string shopId, string priceListId)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var existingPriceList = await _priceListRepository.GetByIdAsync(shopId, priceListId);
            if (existingPriceList == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            await _priceListRepository.DeleteAsync(shopId, priceListId);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = (object)null
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in DeletePriceList: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Delete multiple price lists (Xóa nhiều bảng giá)
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="priceListIds">Danh sách ID của các bảng giá cần xóa</param>
    /// <returns>Result delete multiple price lists</returns>
    [HttpDelete("DeleteMany/{shopId}")]
    public async Task<IActionResult> DeleteManyPriceLists(string shopId, [FromBody] List<string> priceListIds)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            if (priceListIds == null || !priceListIds.Any())
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_INPUT"), this.ControllerContext));

            Shop shop = _shopRepository.FindByShopId(shopId);
            if (shop == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

            if (shop.PartnerId != baseDto.Partner.PartnerId && shop.PartnerId != baseDto.Partner.ParentId)
                return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            // Kiểm tra xem tất cả các bảng giá có tồn tại không
            foreach (var priceListId in priceListIds)
            {
                var existingPriceList = await _priceListRepository.GetByIdAsync(shopId, priceListId);
                if (existingPriceList == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PRICE_LIST_NOT_FOUND"), this.ControllerContext));
            }

            bool deleteResult = await _priceListRepository.DeleteManyAsync(shopId, priceListIds);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = deleteResult ? localizer("DELETED_SUCCESSFULLY") : localizer("FAIL"),
                Data = deleteResult
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in DeleteManyPriceLists: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Add items for price list (Thêm sản phẩm/dịch vụ cho bảng giá)
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="priceListId">ID của bảng giá</param>
    /// <param name="items">Danh sách sản phẩm/dịch vụ</param>
    /// <returns>Result add items</returns>
    [HttpPost("items/{shopId}/{priceListId}")]
    public async Task<IActionResult> AddPriceListItems(string shopId, string priceListId, [FromBody] List<string> items)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            await _priceListRepository.AddItemsAsync(shopId, priceListId, items);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = (object)null
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in AddPriceListItems: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Add items for price list (Thêm sản phẩm/dịch vụ cho bảng giá)
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="priceListId">ID của bảng giá</param>
    /// <param name="items">Danh sách sản phẩm/dịch vụ</param>
    /// <returns>Result add items</returns>
    [HttpDelete("items/{shopId}/{priceListId}")]
    public async Task<IActionResult> RemoveItemsAsync(string shopId, string priceListId, [FromBody] List<string> items)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            await _priceListRepository.RemoveItemsAsync(shopId, priceListId, items);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = (object)null
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in UpdatePriceListItems: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Update branches for price list (Cập nhật chi nhánh cho bảng giá)
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="priceListId">ID của bảng giá</param>
    /// <param name="branchIds">Danh sách ID chi nhánh</param>
    /// <returns>Result update branches</returns>
    [HttpPut("branches/{shopId}/{priceListId}")]
    public async Task<IActionResult> UpdatePriceListBranches(string shopId, string priceListId, [FromBody] List<string> branchIds)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            await _priceListRepository.UpdateBranchAsync(shopId, priceListId, branchIds);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = (object)null
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in UpdatePriceListBranches: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Update ranks for price list (Cập nhật hạng khách hàng cho bảng giá)
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="priceListId">ID của bảng giá</param>
    /// <param name="rankIds">Danh sách ID hạng khách hàng</param>
    /// <returns>Result update ranks</returns>
    [HttpPut("ranks/{shopId}/{priceListId}")]
    public async Task<IActionResult> UpdatePriceListRanks(string shopId, string priceListId, [FromBody] List<string> rankIds)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            await _priceListRepository.UpdateRankAsync(shopId, priceListId, rankIds);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = (object)null
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in UpdatePriceListRanks: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Cập nhật bảng giá theo số lượng sản phẩm/dịch vụ cho bảng giá
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="priceListId">ID của bảng giá</param>
    /// <param name="itemQuantityTiers">Danh sách cấu hình số lượng theo sản phẩm</param>
    /// <returns>Kết quả cập nhật</returns>
    [HttpPut("items/tiers/{shopId}/{priceListId}")]
    public async Task<IActionResult> UpdateItemQuantityTiers(string shopId, string priceListId, [FromBody] List<ItemQuantityTier> itemQuantityTiers)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            await _priceListRepository.UpdateItemQuantityTiersAsync(shopId, priceListId, itemQuantityTiers);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = (object)null
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in UpdateItemQuantityTiers: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Set active status for price list (Thay đổi trạng thái active của bảng giá)
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="priceListId">ID của bảng giá</param>
    /// <param name="isActive">Trạng thái active (true: kích hoạt, false: vô hiệu hóa)</param>
    /// <returns>Result set active status</returns>
    [HttpPut("active/{shopId}/{priceListId}")]
    public async Task<IActionResult> SetActivePriceList(string shopId, string priceListId, [FromQuery] bool isActive)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            // Kiểm tra bảng giá có tồn tại không
            var existingPriceList = await _priceListRepository.GetByIdAsync(shopId, priceListId);
            if (existingPriceList == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            bool updateResult = await _priceListRepository.SetActiveAsync(shopId, priceListId, isActive);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = updateResult ? localizer("SUCCESS") : localizer("FAIL"),
                Data = new
                {
                    PriceListId = priceListId,
                    IsActive = isActive
                }
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in SetActivePriceList: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Lấy danh sách sản phẩm theo priceListId
    /// </summary>
    /// <param name="filter">Thông tin lọc</param>
    /// <returns>Danh sách sản phẩm với giá đã điều chỉnh</returns>
    [HttpGet("Items/ByPriceListId")]
    public async Task<IActionResult> GetItemsByPriceListId([FromQuery] PriceListItemsFilterDto filter)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), filter.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var result = await _priceListFlow.GetItemsByPriceListId(filter);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = result
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in GetItemsByPriceListId: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Lấy danh sách sản phẩm chưa thuộc bất kỳ bảng giá nào 
    /// </summary>
    [HttpGet("Items/NotInAnyPriceList")]
    public async Task<IActionResult> GetItemsNotInAnyPriceList([FromQuery] PriceListItemsFilterDto filter)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), filter.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var result = await _priceListFlow.GetItemsNotInAnyPriceList(filter);
            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = result
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in GetItemsNotInAnyPriceList: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Lấy danh sách sản phẩm/dịch vụ theo bậc số lượng
    /// </summary>
    /// <param name="filter">Thông tin lọc</param>
    /// <returns>Danh sách sản phẩm/dịch vụ theo bậc số lượng</returns>
    [HttpGet("Items/Tiers")]
    public async Task<IActionResult> GetItemQuantityTiers([FromQuery] PriceListItemsFilterDto filter)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), filter.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var result = await _priceListFlow.GetItemQuantityTiers(filter);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = result
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in GetItemQuantityTiers: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }
    
    /// <summary>
    /// Lấy danh sách rank chưa thuộc bất kỳ bảng giá nào 
    /// </summary>
    /// <param name="filter">Thông tin lọc</param>
    /// <returns>Danh sách hạng thành viên chưa thuộc bất kỳ bảng giá nào </returns>
    [HttpGet("ranks")]
    public async Task<IActionResult> GetRanksNotInAnyPriceList([FromQuery] PriceListItemsFilterDto filter)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), filter.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            PagingResult<MembershipLevel> listMembershipLevel = await _priceListFlow.GetRanksNotInAnyPriceList(filter);
            List<MembershipLevelDto> result = _mapper.Map<List<MembershipLevelDto>>(listMembershipLevel.Result);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = result
            });
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error in GetRanksNotInAnyPriceList: {ex.Message}", ex);
            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }
}