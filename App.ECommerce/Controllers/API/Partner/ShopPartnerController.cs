using App.Base.Middleware;
using App.Base.Repository.Interface;
using App.Base.Utilities;
using App.ECommerce.ProcessFlow;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Services.ViettelPost;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Attribute;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Action = App.ECommerce.Resource.Model.Action;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class ShopPartnerController : BaseController
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(ShopPartnerController));
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IConfigurationsRepository<SettingSys> _configurationsRepository;
    private readonly IPartnerRepository _partnerRepository;
    private readonly IShopRepository _shopRepository;
    private readonly IPartnerFunctionRepository _partnerFunctionRepository;
    private readonly IShopFlow _shopFlow;
    private readonly IDomainNameRepository _domainNameRepository;

    public ShopPartnerController(
        IStringLocalizer localizer,
        IServiceScopeFactory serviceScopeFactory,
        IConfigurationsRepository<SettingSys> configurationsRepository,
        IPartnerRepository partnerRepository,
        IShopRepository shopRepository,
        IPartnerFunctionRepository partnerFunctionRepository,
        IShopFlow shopFlow,
        IDomainNameRepository domainNameRepository
    ) : base(localizer)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _configurationsRepository = configurationsRepository;
        _partnerRepository = partnerRepository;
        _shopRepository = shopRepository;
        _partnerFunctionRepository = partnerFunctionRepository;
        _shopFlow = shopFlow;
        _domainNameRepository = domainNameRepository;
    }

    /// <summary>
    /// Get list shop for Partner (Lấy danh sách cửa hàng của đối tác)
    /// </summary>
    /// <param name="model"></param>
    /// <param name="skip"></param>
    /// <param name="limit"></param>
    /// <returns>Result list list shop for Partner</returns>
    // GET: api/partner/ShopPartner/ListShop
    [HttpGet("ListShop")]
    public async Task<IActionResult> ListShop([FromQuery] SearchDto model, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            string shopOwnerId = string.IsNullOrEmpty(partner.ParentId) ? partner.PartnerId : partner.ParentId;
            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            PagingResult<Shop> listShop = _shopRepository.ListShop(paging, shopOwnerId, TypeActive.Actived);
            List<ShopDto> listShopDto = _mapper.Map<List<ShopDto>>(listShop.Result);

            return ResponseData(listShopDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/ListShop",
                Message = $"Error Partner get list shop",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ShopPartner/ListShop", ex);
        }
    }

    private async Task<CustomBadRequest?> ValidatePackageShopLimit(string partnerId)
    {
        var package = await _partnerFunctionRepository.GetPackageHistoryAsync(partnerId);

        if (package == null)
            return new CustomBadRequest(localizer("PACKAGE_NOT_FOUND"), this.ControllerContext);
        
        var currentTotal = await _shopRepository.TotalShop(partnerId);
        var totalAfter = currentTotal + 1;

        if (totalAfter > package.ShopQuantity)
            return new CustomBadRequest(localizer("SHOP_LIMIT_CREATE"), this.ControllerContext);
        
        return null;
    }

    /// <summary>
    /// Create Shop (Tạo mới cửa hàng cho đối tác)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CreateShop</returns>
    // POST: api/partner/ShopPartner/CreateShop
    [HttpPost("CreateShop")]
    public async Task<IActionResult> CreateShop(ShopDto model)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            if (partner.PartnerId != model.PartnerId) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            CustomBadRequest customBadRequest = ViettelPostData.IsValidAddress(model.ProvinceId, model.ProvinceName, model.DistrictId, model.DistrictName, model.WardsId, model.WardsName, _stringLocalizer, this.HttpContext, this.ControllerContext);
            if (customBadRequest != null) return ResponseBadRequest(customBadRequest);

            var packageLimitValidation = await ValidatePackageShopLimit(partnerId);
            if (packageLimitValidation != null)
                return ResponseBadRequest(packageLimitValidation);

            Shop shop = _mapper.Map<Shop>(model);
            shop.ShopLogo = model.ShopLogo == null ? new MediaInfo() { 
                                MediaFileId = "", 
                                Type = TypeMedia.IMAGE,
                                Link = CommonConst.NO_IMAGE_SHOP
                            } : model.ShopLogo;
            shop.StartDate = DateTimes.Now();
            shop.EndDate = null;
            shop.Status = TypeStatus.Actived;
            shop.Created = DateTimes.Now();
            shop.Updated = DateTimes.Now();
            shop = _shopRepository.CreateShop(shop);
            ShopDto shopDto = _mapper.Map<ShopDto>(shop);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/CreateShop",
                Message = $"Partner create Shop",
                DataObject = shop
            });

            return ResponseData(shopDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/CreateShop",
                Message = $"Error Partner create Shop",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ShopPartner/CreateShop", ex, model);
        }
    }

    /// <summary>
    /// Update Shop (Cập nhật thông tin cửa hàng cho đối tác)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result update Shop</returns>
    // POST: api/partner/ShopPartner/UpdateShop
    [HttpPost("UpdateShop")]
    public async Task<IActionResult> UpdateShop(ShopDto model)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            if (partner.PartnerId != model.PartnerId && partner.ParentId != model.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            // Validate
            CustomBadRequest customBadRequest = ViettelPostData.IsValidAddress(model.ProvinceId, model.ProvinceName, model.DistrictId, model.DistrictName, model.WardsId, model.WardsName, _stringLocalizer, this.HttpContext, this.ControllerContext);
            if (customBadRequest != null) return ResponseBadRequest(customBadRequest);

            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (model.OaId != null) shop.OaId = model.OaId;
            if (model.BusinessType != null) shop.BusinessType = model.BusinessType;
            if (!string.IsNullOrEmpty(model.ShopName)) shop.ShopName = model.ShopName;
            if (!string.IsNullOrEmpty(model.ShopDesc)) shop.ShopDesc = model.ShopDesc;
            if (!string.IsNullOrEmpty(model.ShopInfo)) shop.ShopInfo = model.ShopInfo;
            if (!string.IsNullOrEmpty(model.ShopDeeplink)) shop.ShopDeeplink = model.ShopDeeplink;
            if (!string.IsNullOrEmpty(model.PrefixCode)) shop.PrefixCode = model.PrefixCode;
            if (!string.IsNullOrEmpty(model.ReferralCode)) shop.ReferralCode = model.ReferralCode;
            if (model.OpenTime != null) shop.OpenTime = model.OpenTime;
            if (model.CloseTime != null) shop.CloseTime = model.CloseTime;
            if (model.ShopTheme != null) shop.ShopTheme = model.ShopTheme;
            if (!string.IsNullOrEmpty(model.ProvinceId)) shop.ProvinceId = model.ProvinceId;
            if (!string.IsNullOrEmpty(model.ProvinceName)) shop.ProvinceName = model.ProvinceName;
            if (!string.IsNullOrEmpty(model.DistrictId)) shop.DistrictId = model.DistrictId;
            if (!string.IsNullOrEmpty(model.DistrictName)) shop.DistrictName = model.DistrictName;
            if (!string.IsNullOrEmpty(model.WardsId)) shop.WardsId = model.WardsId;
            if (!string.IsNullOrEmpty(model.WardsName)) shop.WardsName = model.WardsName;
            if (!string.IsNullOrEmpty(model.Address)) shop.Address = model.Address;
            if (model.TransportPrice != null) shop.TransportPrice = model.TransportPrice;
            if (model.Active != null) shop.Active = model.Active;
            if (model.ShopLogo != null) shop.ShopLogo = model.ShopLogo;
            shop.ShopSlogan = model.ShopSlogan;

            shop.Updated = DateTimes.Now();
            shop = _shopRepository.UpdateShop(shop);
            ShopDto shopDto = _mapper.Map<ShopDto>(shop);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/UpdateShop",
                Message = $"Partner update Shop",
                DataObject = shop
            });

            return ResponseData(shopDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/UpdateShop",
                Message = $"Error Partner update Shop",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ShopPartner/UpdateShop", ex, model);
        }
    }

    /// <summary>
    /// Update Shop (Cập nhật thông tin policy cửa hàng cho đối tác)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result update Shop Policy</returns>
    // POST: api/partner/ShopPartner/UpsertShopPolicy
    [HttpPost("UpsertShopPolicy")]
    public async Task<IActionResult> UpsertShopPolicy(ShopPolicyDto model)
    {
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

            shop = _shopRepository.UpsertShopPolicy(model);

            ShopPolicyDto shopPolicyDto = _mapper.Map<ShopPolicyDto>(shop);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/UpsertShopPolicy",
                Message = $"Partner Update ShopPolicy Success",
                Exception = null,
                DataObject = null
            });
            return ResponseData(shopPolicyDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/UpsertShopPolicy",
                Message = $"Error Partner Update ShopPolicy",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ShopPartner/UpsertShopPolicy", ex, model);
        }
    }

    /// <summary>
    /// Delete Shop (Xóa cửa hàng, chỉ hỗ trợ cho quản trị)
    /// </summary>
    /// <param name="shopId"></param>
    /// <returns>The result delete Shop</returns>
    // DELETE: api/partner/ShopPartner/DeleteShop
    [HttpDelete("DeleteShop")]
    [MultiPolicysAuthorizeAttribute(Policys = RolePrefix.Partner, Rules = "")]
    public async Task<IActionResult> DeleteShop(string shopId)
    {
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            // remove shop
            await _shopFlow.DeleteShop(shop.ShopId);

            // remove Shop - change InActived
            //shop.Active = TypeShop.InActived;
            //shop = _shopRepository.UpdateShop(shop);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/DeleteShop",
                Message = $"Partner DeleteShop Success",
                DataObject = shop
            });

            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true, Message = localizer("SHOP_DELETE_SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/DeleteShop",
                Message = $"Error Partner DeleteShop",
                Exception = ex
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ShopPartner/DeleteShop", ex);
        }
    }

    /// <summary>
    /// Get detail Shop (Lấy thông tin chi tiết cửa hàng)
    /// </summary>
    /// <param name="shopId"></param>
    /// <returns>The result detail Shop</returns>
    // GET: api/partner/ShopPartner/DetailShop
    [HttpGet("DetailShop")]
    public async Task<IActionResult> DetailShop(string shopId)
    {
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

            ShopDto shopDto = _mapper.Map<ShopDto>(shop);

            // Lấy danh sách domain của shop
            PagingResult<DomainName> listDomainName = _domainNameRepository.FindListByShopId(shopId, Constants.MaxPaging);
            string shopDomain = listDomainName.Result.FirstOrDefault()?.Domain ?? "";
            shopDto.ShopDomain = shopDomain;

            //LogUserEvent(_log4net, Action.Load, Status.Success, $"{RoutePrefix.PARTNER}/ShopPartner/DetailShop", $"Partner get detail Shop", null, null);
            return ResponseData(shopDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/DetailShop",
                Message = $"Error Partner get detail Shop",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ShopPartner/DetailShop", ex);
        }
    }

    [HttpGet("listshoppolicy")]
    public async Task<IActionResult> GetListShopPolicy(string shopId, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Paging paging = new Paging()
            {
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            PagingResult<ShopPolicyDto> listShopPolicy = _shopRepository.ListShopPolicy(paging, shopId);
            List<ShopPolicyDto> listShopPolicyDto = _mapper.Map<List<ShopPolicyDto>>(listShopPolicy.Result);

            //LogUserEvent(_log4net, Action.Load, Status.Success, $"{RoutePrefix.PARTNER}/Branch/ListBranch", $"Partner get list Branch", null, null);
            return ResponseData(new { data = listShopPolicyDto, skip, limit, total = listShopPolicy.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/listshoppolicy",
                Message = $"Error Partner GetListShopPolicy",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/Branch/ListBranch", ex);
        }
    }

    public class UpdateShopDeliveryModel
    {
        [Required]
        public string ShopId { get; set; }

        public bool? EnableInShop { get; set; }

        public bool? EnableExpressDelivery { get; set; }
        public long? TransportPrice { get; set; }
    }

    /// <summary>
    /// Update Shop Configuration (Cập nhật cấu hình cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result update Shop Configuration</returns>
    // POST: api/partner/ShopPartner/UpdateShopDelivery
    [HttpPost("UpdateShopDelivery")]
    public async Task<IActionResult> UpdateShopDelivery(UpdateShopDeliveryModel model)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            // Update shop configuration
            if (model.EnableInShop.HasValue)
                shop.EnableInShop = model.EnableInShop.Value;
            if (model.EnableExpressDelivery.HasValue)
                shop.EnableExpressDelivery = model.EnableExpressDelivery.Value;
            if (model.TransportPrice.HasValue)
                shop.TransportPrice = model.TransportPrice.Value;

            shop = _shopRepository.UpdateShop(shop);
            ShopDto shopDto = _mapper.Map<ShopDto>(shop);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/UpdateShopDelivery",
                Message = $"Partner update Shop Configuration",
                DataObject = shop
            });

            return ResponseData(new { shopDto.ShopId, shopDto.EnableInShop, shopDto.EnableExpressDelivery, shopDto.TransportPrice });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ShopPartner/UpdateShopDelivery",
                Message = $"Error Partner update Shop Configuration",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ShopPartner/UpdateShopDelivery", ex, model);
        }
    }

    /// <summary>
    /// Update Shop TaxRate (Cập nhật thuế của cửa hàng)
    /// </summary>
    /// <param name="shopId">ID cửa hàng</param>
    /// <param name="taxRate">Phần trăm thuế</param>
    // POST: api/partner/shoppartner/taxrate/{shopId}
    [HttpPost("taxrate/{shopId}")]
    public async Task<IActionResult> UpdateTaxRate(string shopId,[FromBody] decimal taxRate)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));
            
            if (taxRate < 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("TAX_RATE_CANNOT_BE_NEGATIVE"), this.ControllerContext));

            var isCheck = await _shopRepository.UpdateTaxRate(shopId, taxRate);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/shoppartner/taxrate/{shopId}",
                Message = $"Partner Update Shop TaxRate: {taxRate}",
                DataObject = shop
            });

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = isCheck
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/shoppartner/taxrate/{shopId}",
                Message = $"Error Update Shop TaxRate: {taxRate}",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/shoppartner/taxrate/{shopId}", ex, taxRate);
        }
    }
}