using App.Base.Middleware;
using App.Base.Repository.Entities;
using App.Base.Repository.Interface;
using App.Base.Utilities;
using App.ECommerce.ProcessFlow;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Resource.Dtos.ResultDtos;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Implement;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using OfficeOpenXml;

using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

using Action = App.ECommerce.Resource.Model.Action;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class ItemsPartnerController : BaseController
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(ItemsPartnerController));
    private readonly IServiceScopeFactory _serviceScopeFactory;

    private readonly IPartnerRepository _partnerRepository;
    private readonly IShopRepository _shopRepository;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IItemsRepository _itemsRepository;
    private readonly IItemOptionGroupRepository _itemOptionGroupRepository;
    private readonly IItemOptionRepository _itemOptionRepository;
    private readonly IGroupFileRepository _groupFileRepository;
    private readonly IStorageRepository _storageRepository;
    private readonly IWarehouseRepository _warehouseRepository;
    private readonly ITempFilesRepository _tempFilesRepository;
    private readonly ITempFilesFlow _tempFilesFlow;
    private readonly IPartnerFunctionRepository _partnerFunctionRepository;
    private readonly IItemsFlow _itemsFlow;
    private readonly IBaseFlow _baseFlow;

    public ItemsPartnerController(
        IStringLocalizer localizer,
        IServiceScopeFactory serviceScopeFactory,
        IPartnerRepository partnerRepository,
        IShopRepository shopRepository,
        ICategoryRepository categoryRepository,
        IItemsRepository itemsRepository,
        IItemOptionGroupRepository itemOptionGroupRepository,
        IItemOptionRepository itemOptionRepository,
        IGroupFileRepository groupFileRepository,
        IStorageRepository storageRepository,
        IWarehouseRepository warehouseRepository,
        ITempFilesRepository tempFilesRepository,
        ITempFilesFlow tempFilesFlow,
        IPartnerFunctionRepository partnerFunctionRepository,
        IItemsFlow itemsFlow,
        IBaseFlow baseFlow
    ) : base(localizer)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _partnerRepository = partnerRepository;
        _shopRepository = shopRepository;
        _categoryRepository = categoryRepository;
        _itemsRepository = itemsRepository;
        _itemOptionGroupRepository = itemOptionGroupRepository;
        _itemOptionRepository = itemOptionRepository;
        _groupFileRepository = groupFileRepository;
        _storageRepository = storageRepository;
        _warehouseRepository = warehouseRepository;
        _tempFilesRepository = tempFilesRepository;
        _tempFilesFlow = tempFilesFlow;
        _partnerFunctionRepository = partnerFunctionRepository;
        _itemsFlow = itemsFlow;
        _baseFlow = baseFlow;
    }

    #region Product

    /// <summary>
    /// Create product for shop (Tạo mới sản phẩm cho cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CreateProduct for shop</returns>
    // POST: api/partner/ItemsPartner/CreateProduct
    [HttpPost("CreateProduct")]
    public async Task<IActionResult> CreateProduct(ProductDto model)
    {
        try
        {
            _log4net.Info($"CreateProduct: \n{JsonConvert.SerializeObject(model, JsonSettings.SettingForNewtonsoftPretty)}");

            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            if (string.IsNullOrEmpty(model.ItemsName))
                return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_NAME_INVALID"), this.ControllerContext));
            if (model.Images == null || model.Images.Count == 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_IMAGES_INVALID"), this.ControllerContext));

            foreach (var image in model.Images)
            {
                if (string.IsNullOrEmpty(image.Link))
                    return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_IMAGES_LINK_INVALID"), this.ControllerContext));
            }

            if (model.ItemsType == TypeItems.Product && string.IsNullOrEmpty(model.WarehouseId))
                return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_WAREHOUSE_INVALID"), this.ControllerContext));

            // Validate-Shop
            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != baseDto.Partner.PartnerId && shop.PartnerId != baseDto.Partner.ParentId)
                return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));


            CategoryFilterDto filterCategory = new CategoryFilterDto
            {
                PartnerId = baseDto.Partner.PartnerId,
                ShopId = shop.ShopId,
                CategoryType = TypeCategory.Product,
                Paging = Constants.MaxPaging
            };

            var treeCategory = await _categoryRepository.FindTree(filterCategory);

            List<string> categoryIdsNew = new List<string>();
            foreach (var categoryId in model.CategoryIds)
            {
                var category = await _categoryRepository.FindByCategoryId(categoryId);

                if (category != null && category.CategoryType == TypeCategory.Product)
                    categoryIdsNew.Add(category.CategoryId);
            }

            if (!categoryIdsNew.Any())
                return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_CATEGORY_INVALID"), this.ControllerContext));

            model.CategoryIds = categoryIdsNew;
            model.ItemsInfo = CleanHtmlContent(model.ItemsInfo);

            if (model.CustomTaxRate != null && model.CustomTaxRate < 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("TAX_RATE_CANNOT_BE_NEGATIVE"), this.ControllerContext));

            var packageLimitValidation = await ValidatePackageItemsLimit(baseDto.Partner, shop.ShopId, TypeItems.Product);
            if (packageLimitValidation != null)
                return ResponseBadRequest(packageLimitValidation);

            var validateItemOptionGroup = await ValidateItemOptions(model);
            if (validateItemOptionGroup != null) return ResponseBadRequest(validateItemOptionGroup);

            string itemsCode = Id64.Generator();

            if (model.IsVariant == false)
            {
                if (model.Price > model.PriceReal)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_PRICE_LARGER_REAL"), this.ControllerContext));

                Items items = _mapper.Map<Items>(model);

                items.ItemsCode = $"{itemsCode}";
                items.ItemsType = TypeItems.Product;
                items.Status = TypeStatus.Actived;
                items.ExtraItemOptionGroups = model.ExtraItemOptionGroups;
                items = _itemsRepository.CreateItems(items);

                if (items.Images != null && items.Images.Count > 0)
                {
                    foreach (var image in items.Images)
                    {
                        _groupFileRepository.UpdateRefIdByMediaFileId(items.ShopId, image.MediaFileId, items.ItemsId);
                    }
                }
            }
            else
            {
                if (model.ListVariant == null || model.ListVariant.Count == 0)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_LIST_VARIANT_INVALID"), this.ControllerContext));

                foreach (var variant in model.ListVariant ?? new List<VariantBase>())
                {
                    if (!string.IsNullOrEmpty(variant.VariantNameOne) && string.IsNullOrEmpty(variant.VariantValueOne))
                        return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_VARIANT_INVALID"), this.ControllerContext));
                    if (!string.IsNullOrEmpty(variant.VariantNameTwo) && string.IsNullOrEmpty(variant.VariantValueTwo))
                        return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_VARIANT_INVALID"), this.ControllerContext));
                    if (!string.IsNullOrEmpty(variant.VariantNameThree) && string.IsNullOrEmpty(variant.VariantValueThree))
                        return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_VARIANT_INVALID"), this.ControllerContext));

                    if (variant.Price > variant.PriceReal)
                        return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_PRICE_LARGER_REAL"), this.ControllerContext));
                }

                foreach (var variant in model.ListVariant ?? new List<VariantBase>())
                {
                    Items items = _mapper.Map<Items>(model);
                    items.ItemsCode = $"{itemsCode}";
                    items.ItemsType = TypeItems.Product;
                    items.VariantImage = variant.VariantImage == null
                        ? null
                        : new MediaInfo()
                        {
                            MediaFileId = variant.VariantImage.MediaFileId,
                            Type = variant.VariantImage.Type,
                            Link = variant.VariantImage.Link.Replace($"{S3Upload.GetDomainS3()}", "")
                        };
                    items.VariantNameOne = variant.VariantNameOne;
                    items.VariantValueOne = variant.VariantValueOne;
                    items.VariantNameTwo = variant.VariantNameTwo;
                    items.VariantValueTwo = variant.VariantValueTwo;
                    items.VariantNameThree = variant.VariantNameThree;
                    items.VariantValueThree = variant.VariantValueThree;
                    items.PriceCapital = variant.PriceCapital;
                    items.PriceReal = variant.PriceReal;
                    items.Price = variant.Price;
                    items.Quantity = variant.Quantity;
                    items.QuantityPurchase = variant.QuantityPurchase;
                    items.Status = TypeStatus.Actived;
                    items.ExtraItemOptionGroups = model.ExtraItemOptionGroups;
                    items.TransportType = model.TransportType;
                    items.CustomTaxRate = model.CustomTaxRate;
                    items = _itemsRepository.CreateItems(items);

                    if (items.VariantImage != null)
                        _groupFileRepository.UpdateRefIdByMediaFileId(items.ShopId, items.VariantImage.MediaFileId, items.ItemsId);
                }
            }

            ItemsGroupBy groupBy = _itemsRepository.GroupByVariant(itemsCode);
            if (groupBy != null && !groupBy.IsVariant && groupBy.ListVariant?.Count > 0)
            {
                groupBy.ItemsId = groupBy.ListVariant.FirstOrDefault()?.ItemsId ?? "";
                groupBy.VariantImage = groupBy.ListVariant.FirstOrDefault()?.VariantImage ?? new MediaInfo();
                groupBy.VariantNameOne = groupBy.ListVariant.FirstOrDefault()?.VariantNameOne ?? "";
                groupBy.VariantValueOne = groupBy.ListVariant.FirstOrDefault()?.VariantValueOne ?? "";
                groupBy.VariantNameTwo = groupBy.ListVariant.FirstOrDefault()?.VariantNameTwo ?? "";
                groupBy.VariantValueTwo = groupBy.ListVariant.FirstOrDefault()?.VariantValueTwo ?? "";
                groupBy.VariantNameThree = groupBy.ListVariant.FirstOrDefault()?.VariantNameThree ?? "";
                groupBy.VariantValueThree = groupBy.ListVariant.FirstOrDefault()?.VariantValueThree ?? "";
                groupBy.PriceCapital = groupBy.ListVariant.FirstOrDefault()?.PriceCapital ?? 0;
                groupBy.PriceReal = groupBy.ListVariant.FirstOrDefault()?.PriceReal ?? 0;
                groupBy.Price = groupBy.ListVariant.FirstOrDefault()?.Price ?? 0;
                groupBy.Quantity = groupBy.ListVariant.FirstOrDefault()?.Quantity ?? 0;
                groupBy.QuantityPurchase = groupBy.ListVariant.FirstOrDefault()?.QuantityPurchase ?? 0;
                groupBy.SellOver = groupBy.ListVariant.FirstOrDefault()?.SellOver ?? false;
                groupBy.ListVariant = new List<VariantBase>();
                groupBy.ExtraItemOptionGroups = groupBy.ExtraItemOptionGroups;
                groupBy.TransportType = groupBy.TransportType;
                groupBy.CategoryIds = groupBy.CategoryIds;
            }

            LogEvent(new EventLogDto
            {
                RefId = baseDto.Partner.PartnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/CreateProduct",
                Message = $"Partner create Product",
                Exception = null,
                DataObject = null
            });

            ItemsGroupByDto groupByDto = _mapper.Map<ItemsGroupByDto>(groupBy);

            return ResponseData(groupByDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/CreateProduct",
                Message = $"Error Partner create Product",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ItemsPartner/CreateProduct", ex, model);
        }
    }

    /// <summary>
    /// Update product for shop (Cập nhật sản phẩm cho cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result UpdateProduct for shop</returns>
    // PUT: api/partner/ItemsPartner/UpdateProduct
    [HttpPut("UpdateProduct")]
    public async Task<IActionResult> UpdateProduct(ProductDto model)
    {
        try
        {
            _log4net.Info($"UpdateProduct: {JsonConvert.SerializeObject(model, JsonSettings.SettingForNewtonsoftPretty)}");

            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var error = ValidateProductInput(model);
            if (error != null) return ResponseBadRequest(error);
            var validateItemOptionGroup = await ValidateItemOptions(model);
            if (validateItemOptionGroup != null) return ResponseBadRequest(validateItemOptionGroup);

            ItemsGroupBy groupItems = _itemsRepository.GroupByVariant(model.ItemsCode);
            if (groupItems == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("ITEMS_NOT_FOUND"), this.ControllerContext));

            List<string> categoryIdsNew = new List<string>();
            foreach (var categoryId in model.CategoryIds)
            {
                var category = await _categoryRepository.FindByCategoryId(categoryId);

                if (category != null && category.CategoryType == TypeCategory.Product)
                    categoryIdsNew.Add(category.CategoryId);
            }

            if (!categoryIdsNew.Any())
                return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_CATEGORY_INVALID"), this.ControllerContext));

            model.CategoryIds = categoryIdsNew;
            model.ItemsInfo = CleanHtmlContent(model.ItemsInfo);

            if (model.IsVariant == false)
            {
                if (model.Price > model.PriceReal)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_PRICE_LARGER_REAL"), this.ControllerContext));
                if (model.ListVariant is { Count: > 0 })
                    return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_VARIANT_INVALID"), this.ControllerContext));

                if (groupItems.IsVariant == model.IsVariant)
                {
                    _log4net.Info($"UpdateProduct | Cập nhật cùng loại biến thể false --> false");
                    Items? items = _itemsRepository.FindByItemsCode(groupItems.ItemsCode).FirstOrDefault();
                    if (items == null)
                        return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_NOT_FOUND"), this.ControllerContext));

                    _itemsFlow.UpdateProductFields(items, model);
                    _itemsRepository.UpdateItems(items);
                    _itemsFlow.SyncProductImages(items, items.Images);
                }
                else
                {
                    _log4net.Info($"UpdateProduct | Cập nhật khác loại biến thể true --> false");
                    Items? items = _itemsRepository.FindByItemsId(groupItems.ListVariant?[0].ItemsId ?? "");
                    if (items == null)
                        return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_NOT_FOUND"), this.ControllerContext));

                    if (groupItems.ListVariant != null && groupItems.ListVariant.Count > 1)
                        _itemsFlow.DeleteVariants(groupItems.ListVariant.Skip(1).OfType<Items>());

                    _itemsFlow.UpdateProductFields(items, model);

                    items.VariantImage = null;
                    items.VariantNameOne = "";
                    items.VariantValueOne = "";
                    items.VariantNameTwo = "";
                    items.VariantValueTwo = "";
                    items.VariantNameThree = "";
                    items.VariantValueThree = "";
                    _itemsRepository.UpdateItems(items);
                    _itemsFlow.SyncProductImages(items, items.Images);
                }
            }
            else // IsVariant == true
            {
                _log4net.Info($"UpdateProduct | Cập nhật loại biến thể true --> true");
                if (model.ListVariant == null || model.ListVariant.Count == 0)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_LIST_VARIANT_INVALID"), this.ControllerContext));

                var oldVariants = groupItems.ListVariant?.ToDictionary(x => x.ItemsId, x => x) ?? new Dictionary<string, VariantBase>();

                foreach (var variant in model.ListVariant)
                {
                    if (!string.IsNullOrEmpty(variant.VariantNameOne) && string.IsNullOrEmpty(variant.VariantValueOne))
                        return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_VARIANT_INVALID"), this.ControllerContext));
                    if (!string.IsNullOrEmpty(variant.VariantNameTwo) && string.IsNullOrEmpty(variant.VariantValueTwo))
                        return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_VARIANT_INVALID"), this.ControllerContext));
                    if (!string.IsNullOrEmpty(variant.VariantNameThree) && string.IsNullOrEmpty(variant.VariantValueThree))
                        return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_VARIANT_INVALID"), this.ControllerContext));
                    if (variant.Price > variant.PriceReal)
                        return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_PRICE_LARGER_REAL"), this.ControllerContext));

                    if (string.IsNullOrEmpty(variant.ItemsId))
                    {
                        Items newItem = _mapper.Map<Items>(model);
                        newItem.ItemsCode = groupItems.ItemsCode;
                        newItem.ItemsType = TypeItems.Product;
                        _itemsFlow.UpdateVariantFields(newItem, variant);
                        newItem.Status = TypeStatus.Actived;
                        newItem.ExtraItemOptionGroups = model.ExtraItemOptionGroups;
                        newItem.TransportType = model.TransportType;
                        newItem.CustomTaxRate = model.CustomTaxRate;
                        _itemsRepository.CreateItems(newItem);
                    }
                    else
                    {
                        Items? updateItems = _itemsRepository.FindByItemsId(variant.ItemsId);
                        if (updateItems != null)
                        {
                            _itemsFlow.UpdateProductFields(updateItems, model);
                            _itemsFlow.UpdateVariantFields(updateItems, variant);
                            _itemsRepository.UpdateItems(updateItems);
                            _itemsFlow.SyncProductImages(updateItems, updateItems.Images);
                            oldVariants.Remove(variant.ItemsId); // Đã xử lý, không xóa
                        }
                    }
                }

                foreach (var toDelete in oldVariants.Values)
                {
                    await _itemsFlow.DeleteItems(toDelete.ItemsId);
                }
            }

            ItemsGroupBy groupBy = _itemsRepository.GroupByVariant(groupItems.ItemsCode);
            if (groupBy != null && !groupBy.IsVariant && groupBy.ListVariant?.Count > 0)
            {
                groupBy.ItemsId = groupBy.ListVariant.FirstOrDefault()?.ItemsId ?? "";
                groupBy.VariantImage = groupBy.ListVariant.FirstOrDefault()?.VariantImage ?? new MediaInfo();
                groupBy.VariantNameOne = groupBy.ListVariant.FirstOrDefault()?.VariantNameOne ?? "";
                groupBy.VariantValueOne = groupBy.ListVariant.FirstOrDefault()?.VariantValueOne ?? "";
                groupBy.VariantNameTwo = groupBy.ListVariant.FirstOrDefault()?.VariantNameTwo ?? "";
                groupBy.VariantValueTwo = groupBy.ListVariant.FirstOrDefault()?.VariantValueTwo ?? "";
                groupBy.VariantNameThree = groupBy.ListVariant.FirstOrDefault()?.VariantNameThree ?? "";
                groupBy.VariantValueThree = groupBy.ListVariant.FirstOrDefault()?.VariantValueThree ?? "";
                groupBy.PriceCapital = groupBy.ListVariant.FirstOrDefault()?.PriceCapital ?? 0;
                groupBy.PriceReal = groupBy.ListVariant.FirstOrDefault()?.PriceReal ?? 0;
                groupBy.Price = groupBy.ListVariant.FirstOrDefault()?.Price ?? 0;
                groupBy.Quantity = groupBy.ListVariant.FirstOrDefault()?.Quantity ?? 0;
                groupBy.QuantityPurchase = groupBy.ListVariant.FirstOrDefault()?.QuantityPurchase ?? 0;
                groupBy.SellOver = groupBy.ListVariant.FirstOrDefault()?.SellOver ?? false;
                groupBy.ListVariant = new List<VariantBase>();
            }

            ItemsGroupByDto groupByDto = _mapper.Map<ItemsGroupByDto>(groupBy);

            LogEvent(new EventLogDto
            {
                RefId = baseDto.Partner.PartnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/UpdateProduct",
                Message = $"Partner update Product",
                Exception = null,
                DataObject = model
            });

            // Trả về kết quả thành công (có thể trả về dữ liệu mới nếu cần)
            return ResponseData(groupByDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/UpdateProduct",
                Message = $"Error Partner update Product",
                Exception = ex,
                DataObject = null
            });
            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ItemsPartner/UpdateProduct", ex, model);
        }
    }

    /// <summary>
    /// Delete product for shop (Xoá sản phẩm cho cửa hàng, xoá tấc cả biến thể đi kèm)
    /// </summary>
    /// <param name="itemsCode"></param>
    /// <returns>The result DeleteProduct for shop</returns>
    // DELETE: api/partner/ItemsPartner/DeleteProduct
    [HttpDelete("DeleteProduct/{shopId}")]
    [MultiPolicysAuthorizeAttribute(Policys = RolePrefix.Partner, Rules = "")]
    public async Task<IActionResult> DeleteProduct(string shopId, string itemsCode)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            List<Items> itemsList = _itemsRepository.FindByItemsCode(itemsCode);
            if (itemsList.Count == 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("PRODUCT_NOT_FOUND"), this.ControllerContext));

            foreach (var items in itemsList)
            {
                await _itemsFlow.DeleteItems(items.ItemsId);
            }

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/DeleteProduct/{itemsCode}",
                Message = $"Partner delete Product",
                Exception = null,
                DataObject = null
            });

            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true, Message = localizer("PRODUCT_DELETE_SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/DeleteProduct/{itemsCode}",
                Message = $"Error Partner delete Product",
                Exception = null,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ItemsPartner/DeleteProduct", ex);
        }
    }
    #endregion Product

    #region Service

    /// <summary>
    /// Create service for shop (Tạo mới dịch vụ cho cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CreateService for shop</returns>
    // POST: api/partner/ServicePartner/CreateService
    [HttpPost("CreateService")]
    public async Task<IActionResult> CreateService(ServiceDto model)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            CategoryFilterDto filterCategory = new CategoryFilterDto
            {
                PartnerId = baseDto.Partner.PartnerId,
                ShopId = baseDto.Shop.ShopId,
                CategoryType = TypeCategory.Service,
                Paging = Constants.MaxPaging
            };

            var treeCategory = await _categoryRepository.FindTree(filterCategory);

            List<string> categoryIdsNew = new List<string>();
            foreach (var categoryId in model.CategoryIds)
            {
                var category = await _categoryRepository.FindByCategoryId(categoryId);

                if (category != null && category.CategoryType == TypeCategory.Service)
                    categoryIdsNew.Add(category.CategoryId);
            }

            if (!categoryIdsNew.Any())
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_CATEGORY_INVALID"), this.ControllerContext));

            model.CategoryIds = categoryIdsNew;
            model.ItemsInfo = CleanHtmlContent(model.ItemsInfo);

            if (string.IsNullOrEmpty(model.ItemsName))
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_NAME_INVALID"),
                    this.ControllerContext));
            if (model.Images == null || model.Images.Count == 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_IMAGES_INVALID"),
                    this.ControllerContext));
            foreach (var image in model.Images)
            {
                if (string.IsNullOrEmpty(image.Link))
                    return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_IMAGES_LINK_INVALID"),
                        this.ControllerContext));
            }

            if (model.Price < model.PriceCapital)
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_PRICE_LARGER_CAPITAL"),
                    this.ControllerContext));
            if (model.PriceReal < model.PriceCapital)
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_REAL_LARGER_CAPITAL"),
                    this.ControllerContext));
            if (model.Price > model.PriceReal)
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_PRICE_LARGER_REAL"),
                    this.ControllerContext));

            if (model.CustomTaxRate != null && model.CustomTaxRate < 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("TAX_RATE_CANNOT_BE_NEGATIVE"),
                    this.ControllerContext));

            var packageLimitValidation = await ValidatePackageItemsLimit(baseDto.Partner, baseDto.Shop.ShopId, TypeItems.Service);
            if (packageLimitValidation != null)
                return ResponseBadRequest(packageLimitValidation);

            Items items = _mapper.Map<Items>(model);
            string itemsCode = string.IsNullOrEmpty(model.ItemsCode) ? Id64.Generator() : model.ItemsCode;
            items.ItemsCode = $"{itemsCode}";
            items.IsVariant = false;
            items.ItemsType = TypeItems.Service;
            items.Status = TypeStatus.Actived;
            items = _itemsRepository.CreateItems(items);
            ServiceDto serviceDto = _mapper.Map<ServiceDto>(items);

            if (items.Images != null && items.Images.Count > 0)
            {
                foreach (var image in items.Images)
                {
                    _groupFileRepository.UpdateRefIdByMediaFileId(items.ShopId, image.MediaFileId, items.ItemsId);
                }
            }

            return ResponseData(serviceDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/CreateService",
                Message = $"Error Partner create Service",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ServicePartner/CreateService", ex, model);
        }
    }

    /// <summary>
    /// Update service for shop (Cập nhật dịch vụ cho cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result UpdateService for shop</returns>
    // PUT: api/partner/ServicePartner/UpdateService
    [HttpPut("UpdateService")]
    public async Task<IActionResult> UpdateService(ServiceDto model)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            Items? items = _itemsRepository.FindByItemsId(model.ItemsId);
            if (items == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_NOT_FOUND"), this.ControllerContext));

            CategoryFilterDto filterCategory = new CategoryFilterDto
            {
                PartnerId = baseDto.Partner.PartnerId,
                ShopId = baseDto.Shop.ShopId,
                CategoryType = TypeCategory.Service,
                Paging = Constants.MaxPaging
            };

            var treeCategory = await _categoryRepository.FindTree(filterCategory);

            List<string> categoryIdsNew = new List<string>();
            foreach (var categoryId in model.CategoryIds)
            {
                var category = await _categoryRepository.FindByCategoryId(categoryId);

                if (category != null && category.CategoryType == TypeCategory.Service)
                    categoryIdsNew.Add(category.CategoryId);
            }

            if (!categoryIdsNew.Any())
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_CATEGORY_INVALID"), this.ControllerContext));

            model.CategoryIds = categoryIdsNew;

            if (string.IsNullOrEmpty(model.ItemsName))
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_NAME_INVALID"),
                    this.ControllerContext));
            if (model.Images == null || model.Images.Count == 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_IMAGES_INVALID"),
                    this.ControllerContext));
            foreach (var image in model.Images)
            {
                if (string.IsNullOrEmpty(image.Link))
                    return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_IMAGES_LINK_INVALID"),
                        this.ControllerContext));
            }

            if (model.Price < model.PriceCapital)
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_PRICE_LARGER_CAPITAL"),
                    this.ControllerContext));
            if (model.PriceReal < model.PriceCapital)
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_REAL_LARGER_CAPITAL"),
                    this.ControllerContext));
            if (model.Price > model.PriceReal)
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_PRICE_LARGER_REAL"),
                    this.ControllerContext));

            if (model.CustomTaxRate != null && model.CustomTaxRate < 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("TAX_RATE_CANNOT_BE_NEGATIVE"),
                    this.ControllerContext));

            items.CategoryIds = model.CategoryIds;
            items.ItemsName = model.ItemsName;
            items.IsTop = model.IsTop;
            items.ItemsInfo = CleanHtmlContent(model.ItemsInfo);
            items.Images = model.Images;
            items.Sold = model.Sold;
            items.ItemsPosition = model.ItemsPosition;
            // Variant
            items.IsVariant = false;
            // Transport
            items.SeoTags = model.SeoTags;
            items.TypePublish = model.TypePublish;
            items.CustomTaxRate = model.CustomTaxRate;
            // items.Status = model.Status;
            // items.Created = DateTimes.Now();
            items.Updated = DateTimes.Now();
            items = _itemsRepository.UpdateItems(items);
            ServiceDto serviceDto = _mapper.Map<ServiceDto>(items);

            if (items.Images != null && items.Images.Count > 0)
            {
                foreach (var image in items.Images)
                {
                    _groupFileRepository.UpdateRefIdByMediaFileId(items.ShopId, image.MediaFileId, items.ItemsId);
                }

                var excludeMediaFileIds = items.Images?.Select(img => img.MediaFileId).ToList() ?? new List<string>();
                var existingImages = _groupFileRepository.DeleteMediaFilesByRefExcludeList(items.ShopId, items.ItemsId, excludeMediaFileIds);
            }

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/UpdateService",
                Message = $"Partner update Service",
                Exception = null,
                DataObject = null
            });

            return ResponseData(serviceDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/UpdateService",
                Message = $"Error Partner update Service",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ServicePartner/UpdateService", ex, model);
        }
    }

    /// <summary>
    /// Delete service for shop (Xoá dịch vụ cho cửa hàng)
    /// </summary>
    /// <param name="itemsCode"></param>
    /// <returns>The result DeleteService for shop</returns>
    // DELETE: api/partner/ServicePartner/DeleteService
    [HttpDelete("DeleteService/{shopId}")]
    [MultiPolicysAuthorizeAttribute(Policys = RolePrefix.Partner, Rules = "")]
    public async Task<IActionResult> DeleteService(string shopId, string itemsCode)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            List<Items> itemsList = _itemsRepository.FindByItemsCode(itemsCode);
            if (itemsList.Count == 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("SERVICE_NOT_FOUND"), this.ControllerContext));

            foreach (var items in itemsList)
            {
                await _itemsFlow.DeleteItems(items.ItemsId);
            }

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/DeleteService",
                Message = $"Partner delete Product",
                Exception = null,
                DataObject = null
            });

            return ResponseData(new
            { Timestamp = DateTimes.Now(), Result = true, Message = localizer("SERVICE_DELETE_SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/DeleteService",
                Message = $"Error Partner delete Product",
                Exception = null,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ServicePartner/DeleteService", ex);
        }
    }
    #endregion Service ./

    /// <summary>
    /// Get list items for shop (Danh sách sản phẩm hoặc dịch vụ cho cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Result ListItems for shop</returns>
    // GET: api/partner/ItemsPartner/ListItems
    [HttpGet("ListItems")]
    public async Task<IActionResult> ListItems([FromQuery] ItemFilterDto model)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                PageIndex = model.Skip / (model.Limit == 0 ? 1 : model.Limit),
                PageSize = model.Limit,
                NameType = model.SortBy ?? TypeSortName.Created,
                SortType = model.SortOrder ?? TypeSort.desc
            };

            var itemsGroupBy = _itemsRepository.ListQueryGroupByVariantExtend(false, paging, model);

            List<ItemsGroupByDto> itemsGroupByDto = _mapper.Map<List<ItemsGroupByDto>>(itemsGroupBy.Result);

            return ResponseData(new { data = itemsGroupByDto, model.Skip, model.Limit, total = itemsGroupBy.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/ListItems",
                Message = $"Error Partner get list Items",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ItemsPartner/ListItems", ex);
        }
    }

    /// <summary>
    /// Get detail items group variant for shop (Lấy thông tin chi tiết sản phẩm hoặc dịch vụ nhóm theo biến thể)
    /// </summary>
    /// <param name="shopId"></param>
    /// <param name="itemsCode"></param>
    /// <returns>The result DetailGroupItems for shop</returns>
    // GET: api/partner/ItemsPartner/DetailGroupItems
    [HttpGet("DetailGroupItems/{shopId}")]
    public async Task<IActionResult> DetailGroupItems(string shopId, string itemsCode)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            ItemsGroupBy groupBy = _itemsRepository.GroupByVariant(itemsCode);

            if (groupBy == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("ITEMS_NOT_FOUND"), this.ControllerContext));

            if (!groupBy.IsVariant && groupBy.ListVariant?.Count > 0)
            {
                groupBy.ItemsId = groupBy.ListVariant.FirstOrDefault()?.ItemsId ?? "";
                groupBy.VariantImage = groupBy.ListVariant.FirstOrDefault()?.VariantImage ?? new MediaInfo();
                groupBy.VariantNameOne = groupBy.ListVariant.FirstOrDefault()?.VariantNameOne ?? "";
                groupBy.VariantValueOne = groupBy.ListVariant.FirstOrDefault()?.VariantValueOne ?? "";
                groupBy.VariantNameTwo = groupBy.ListVariant.FirstOrDefault()?.VariantNameTwo ?? "";
                groupBy.VariantValueTwo = groupBy.ListVariant.FirstOrDefault()?.VariantValueTwo ?? "";
                groupBy.VariantNameThree = groupBy.ListVariant.FirstOrDefault()?.VariantNameThree ?? "";
                groupBy.VariantValueThree = groupBy.ListVariant.FirstOrDefault()?.VariantValueThree ?? "";
                groupBy.PriceCapital = groupBy.ListVariant.FirstOrDefault()?.PriceCapital ?? 0;
                groupBy.PriceReal = groupBy.ListVariant.FirstOrDefault()?.PriceReal ?? 0;
                groupBy.Price = groupBy.ListVariant.FirstOrDefault()?.Price ?? 0;
                groupBy.Quantity = groupBy.ListVariant.FirstOrDefault()?.Quantity ?? 0;
                groupBy.QuantityPurchase = groupBy.ListVariant.FirstOrDefault()?.QuantityPurchase ?? 0;
                groupBy.SellOver = groupBy.ListVariant.FirstOrDefault()?.SellOver ?? false;
                groupBy.ListVariant = new List<VariantBase>();
                groupBy.CategoryIds = groupBy.CategoryIds;
                groupBy.Sold = groupBy.Sold;
                groupBy.IsTop = groupBy.IsTop;
                groupBy.ItemsPosition = groupBy.ItemsPosition;
            }

            ItemsGroupByDto groupByDto = _mapper.Map<ItemsGroupByDto>(groupBy);

            return ResponseData(groupByDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/DetailGroupItems",
                Message = $"Error Partner get list Items",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ItemsPartner/DetailGroupItems", ex);
        }
    }

    /// <summary>
    /// Get detail items for shop (Lấy thông tin chi sản phẩm hoặc dịch vụ, không phân nhóm theo biến thể)
    /// </summary>
    /// <param name="shopId"></param>
    /// <param name="itemsCode"></param>
    /// <returns>The result DetailItems for shop</returns>
    // GET: api/partner/ItemsPartner/DetailItems
    [HttpGet("DetailItems/{shopId}")]
    public async Task<IActionResult> DetailItems(string shopId, string itemsCode)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            List<Items> itemsList = _itemsRepository.FindByItemsCode(itemsCode);
            if (itemsList.Count == 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("ITEMS_NOT_FOUND"), this.ControllerContext));

            List<ItemsDto> itemsListDto = _mapper.Map<List<ItemsDto>>(itemsList);

            //LogUserEvent(_log4net, Action.Load, Status.Success, $"{RoutePrefix.PARTNER}/ItemsPartner/DetailItems", $"Partner get detail Items", null, null);
            return ResponseData(itemsListDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/DetailItems",
                Message = $"Error Partner get detail Items",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ItemsPartner/DetailGroupItems", ex);
        }
    }

    public class ListItemsByItemsIdDto
    {
        [Required][DefaultValue("")] public string ShopId { get; set; }

        [Required] public List<string> ItemsIds { get; set; }
    }

    /// <summary>
    /// Get list items by itemsIds (Danh sách sản phẩm hoặc dịch vụ cho cửa hàng theo 1 list itemsId)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Result ListItemsByItemsId for shop</returns>
    // POST: api/partner/ItemsPartner/ListItemsByItemsId
    [HttpPost("ListItemsByItemsId")]
    public async Task<IActionResult> ListItemsByItemsIdAsync([FromBody] ListItemsByItemsIdDto model)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            List<Items> listItems = _itemsRepository.FindByItemsIds(string.Join(",", model.ItemsIds));
            List<ItemsDto> listItemsDto = _mapper.Map<List<ItemsDto>>(listItems);

            return ResponseData(new { data = listItems });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/ListItemsByItemsId",
                Message = $"Error Partner get list Items",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ItemsPartner/ListItemsByItemsId", ex);
        }
    }

    public class ListItemsByItemsCodeDto
    {
        [Required][DefaultValue("")] public string ShopId { get; set; }

        [Required] public List<string> ItemsCodes { get; set; }
    }

    /// <summary>
    /// Get list items by itemsCodes (Danh sách sản phẩm hoặc dịch vụ cho cửa hàng theo 1 list itemsCode)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Result ListItemsByItemsCode for shop</returns>
    // POST: api/partner/ItemsPartner/ListItemsByItemsCode
    [HttpPost("ListItemsByItemsCode")]
    public async Task<IActionResult> ListItemsByItemsCode([FromBody] ListItemsByItemsCodeDto model)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            List<Items> listItems = _itemsRepository.FindByItemsCodes(model.ItemsCodes);
            List<ItemsDto> listItemsDto = _mapper.Map<List<ItemsDto>>(listItems);

            return ResponseData(new { data = listItems });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/ListItemsByItemsId",
                Message = $"Error Partner get list Items",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ItemsPartner/ListItemsByItemsCode", ex);
        }
    }


    /// <summary>
    /// Xuất file Excel mẫu để nhập sản phẩm
    /// </summary>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="type">Loại sản phẩm</param>
    /// <returns>File Excel mẫu</returns>
    [HttpGet("ExportProductTemplate")]
    public async Task<IActionResult> ExportProductTemplate(string shopId, TypeItems type = TypeItems.Product)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var fileBytes = await _itemsRepository.ExportItemsTemplate(shopId, type);
            var fileName = type == TypeItems.Product ? $"TemplateProduct.xlsx" : $"TemplateService.xlsx";

            var file = File(fileBytes, ExportConst.EXCEL_CONTENT_TYPE, fileName);

            var link = S3Upload.SendMyFileToS3(fileBytes, ExportConst.EXCEL_CONTENT_TYPE, fileName, ExportConst.PATH_TEMPLATE).Result;

            _log4net.Info($"ExportProductTemplate link: {link}");

            if (!string.IsNullOrEmpty(link))
            {
                MediaFile objMedia = new MediaFile
                {
                    GroupFileId = "",
                    Type = TypeMedia.FILE,
                    Link = link,
                };

                objMedia = _groupFileRepository.CreateMediaFile(objMedia);
                MediaFileDto objMediaDto = _mapper.Map<MediaFileDto>(objMedia);

                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = objMediaDto,
                    Message = localizer("SUCCESS")
                });
            }
            else
            {
                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = new MediaFileDto(),
                    Message = localizer("FAIL")
                });
            }
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Export,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/ExportProductTemplate",
                Message = $"Error exporting product template for shopId: {shopId}",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTime.Now,
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Nhập sản phẩm hoặc dịch vụ từ file Excel
    /// </summary>
    /// <param name="file">File Excel chứa danh sách sản phẩm hoặc dịch vụ</param>
    /// <param name="shopId">ID của cửa hàng</param>
    /// <param name="type">Loại mục (Product/Service)</param>
    /// <returns>Kết quả nhập liệu</returns>
    [HttpPost("Import")]
    public async Task<IActionResult> ImportItems(IFormFile file, [FromForm] string shopId, [FromForm] TypeItems type)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            if (file == null || file.Length == 0 || !file.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                return ResponseBadRequest(new CustomBadRequest(localizer("VALIDATION_FAILED"), this.ControllerContext));

            if (type != TypeItems.Product && type != TypeItems.Service)
                return ResponseBadRequest(new CustomBadRequest(localizer("TYPE_REQUIRED"), this.ControllerContext));

            var packageLimitValidation = await ValidatePackageItemsLimit(baseDto.Partner, baseDto.Shop.ShopId, type);

            if (packageLimitValidation != null)
                return ResponseBadRequest(packageLimitValidation);

            var itemsList = new List<Items>();
            var errors = new List<string>();

            using (var stream = file.OpenReadStream())
            using (var package = new ExcelPackage(stream))
            {
                _log4net.Info($"ImportItems: File opened, Worksheets count: {package.Workbook.Worksheets.Count}");

                if (package.Workbook.Worksheets.Count == 0)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("NO_WORKSHEET_FOUND"), this.ControllerContext));
                }

                // Kiểm tra worksheet có tồn tại không
                if (package.Workbook.Worksheets.Count < 2)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));
                }

                var worksheet = package.Workbook.Worksheets[1]; // Sheet 1 chứa dữ liệu
                _log4net.Info($"ImportItems: Worksheet name: {worksheet.Name}, Dimension: {worksheet.Dimension}");

                // Kiểm tra worksheet có dữ liệu không
                if (worksheet.Dimension == null)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("WORKSHEET_EMPTY"), this.ControllerContext));
                }

                var expectedHeaders = type == TypeItems.Product
                    ? ExportConst.HEADER_TEMPLATE_PRODUCT
                    : ExportConst.HEADER_TEMPLATE_SERVICE;

                int headerRow = 1;
                int columnCount = worksheet.Dimension?.Columns ?? 0;
                if (columnCount < expectedHeaders.Length)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));
                }

                for (int col = 1; col <= expectedHeaders.Length; col++)
                {
                    var cell = worksheet.Cells[headerRow, col];
                    if (cell == null)
                    {
                        return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));
                    }

                    var header = cell.GetValue<string>()?.Trim();
                    if (header != expectedHeaders[col - 1])
                    {
                        _log4net.Warn($"ImportItems: Header mismatch at column {col}. Expected: '{expectedHeaders[col - 1]}', Got: '{header}'");
                        return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));
                    }
                }

                int rowCount = worksheet.Dimension.Rows;
                _log4net.Info($"ImportItems: Total rows in worksheet: {rowCount}");

                List<CategoryDto> categories = await _categoryRepository.GetListCategoryByTypeItem(shopId, type);

                for (int row = 2; row <= rowCount; row++)
                {
                    int rowView = row - 1;

                    // Kiểm tra row có dữ liệu không
                    var firstCell = worksheet.Cells[row, 1];
                    if (firstCell == null || firstCell.Value == null)
                    {
                        _log4net.Info($"ImportItems: Skipping empty row {row}");
                        continue;
                    }

                    var productDto = new ProductDto
                    {
                        ItemsName = firstCell.GetValue<string>(),
                        CategoryIds = new List<string>()
                    };

                    // Validate ItemsName
                    var itemsNameCell = worksheet.Cells[row, 1];
                    if (itemsNameCell.Value == null || string.IsNullOrWhiteSpace(itemsNameCell.GetValue<string>()))
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("ITEMS_NAME")} {localizer("ROW_REQUIRED")}");
                        continue;
                    }
                    productDto.ItemsName = itemsNameCell.GetValue<string>().Trim();
                    productDto.CategoryIds = new List<string>();

                    // Xử lý 3 cột danh mục - kiểm tra an toàn
                    var categoryBCell = worksheet.Cells[row, 2];
                    var categoryCCell = worksheet.Cells[row, 3];
                    var categoryDCell = worksheet.Cells[row, 4];

                    var categoryB = categoryBCell?.GetValue<string>()?.Trim();
                    var categoryC = categoryCCell?.GetValue<string>()?.Trim();
                    var categoryD = categoryDCell?.GetValue<string>()?.Trim();

                    // Kiểm tra trùng lặp danh mục
                    var uniqueCategories = new HashSet<string>();
                    if (!string.IsNullOrEmpty(categoryB))
                    {
                        uniqueCategories.Add(categoryB);
                    }
                    else
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("CATEGORY")} 1 (B) {localizer("ROW_REQUIRED")} - {categoryC}");

                    if (!string.IsNullOrEmpty(categoryC) && !uniqueCategories.Contains(categoryC))
                    {
                        uniqueCategories.Add(categoryC);
                    }
                    else if (!string.IsNullOrEmpty(categoryC))
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("CATEGORY")} 2 (C) {localizer("ROW_EXISTED")} - {categoryC}");
                        continue;
                    }
                    if (!string.IsNullOrEmpty(categoryD) && !uniqueCategories.Contains(categoryD))
                    {
                        uniqueCategories.Add(categoryD);
                    }
                    else if (!string.IsNullOrEmpty(categoryD))
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("CATEGORY")} 3 (D) {localizer("ROW_EXISTED")} - {categoryD}");
                        continue;
                    }

                    // Thêm danh mục vào productDto
                    foreach (var categoryName in uniqueCategories)
                    {
                        var categoryId = categories
                            .FirstOrDefault(c =>
                                RemoveDiacritics(c.CategoryName ?? "").Trim().ToLower() ==
                                RemoveDiacritics(categoryName ?? "").Trim().ToLower()
                            )?.CategoryId;
                        if (categoryId != null)
                            productDto.CategoryIds.Add(categoryId);
                        else
                            errors.Add($"{localizer("ROW")} {rowView}: {localizer("CATEGORY")} {localizer("ROW_REQUIRED")} - {categoryName}");
                    }

                    // Validate Price
                    var priceCell = worksheet.Cells[row, 5];
                    if (priceCell?.Value == null)
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("PRICE")} {localizer("ROW_REQUIRED")}");
                        continue;
                    }
                    if (!long.TryParse(priceCell.GetValue<string>(), out long price))
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("PRICE")} {localizer("MUST_BE_NUMBER")}");
                        continue;
                    }
                    productDto.Price = price;

                    // Validate PriceReal
                    var priceRealCell = worksheet.Cells[row, 6];
                    if (priceRealCell?.Value == null)
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("PRICE_REAL")} {localizer("ROW_REQUIRED")}");
                        continue;
                    }
                    if (!long.TryParse(priceRealCell.GetValue<string>(), out long priceReal))
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("PRICE_REAL")} {localizer("MUST_BE_NUMBER")}");
                        continue;
                    }
                    productDto.PriceReal = priceReal;

                    // Validate PriceCapital
                    var priceCapitalCell = worksheet.Cells[row, 7];
                    if (priceCapitalCell?.Value != null)
                    {
                        if (!long.TryParse(priceCapitalCell.GetValue<string>(), out long priceCapital))
                        {
                            errors.Add($"{localizer("ROW")} {rowView}: {localizer("PRICE_CAPITAL")} {localizer("MUST_BE_NUMBER")}");
                            continue;
                        }
                        productDto.PriceCapital = priceCapital;
                    }

                    // Validate Quantity
                    var quantityCell = worksheet.Cells[row, 8];
                    if (quantityCell?.Value == null)
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("QUANTITY")} {localizer("ROW_REQUIRED")}");
                        continue;
                    }
                    if (!int.TryParse(quantityCell.GetValue<string>(), out int quantity))
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("QUANTITY")} {localizer("MUST_BE_NUMBER")}");
                        continue;
                    }
                    productDto.Quantity = quantity;

                    if (productDto.Price > productDto.PriceReal)
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("PRICE_REAL")} {localizer("ROW_REQUIRED")}");

                    // Validate Images
                    var imageUrlsCell = worksheet.Cells[row, 19];
                    if (imageUrlsCell?.Value == null || string.IsNullOrWhiteSpace(imageUrlsCell.GetValue<string>()))
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("IMAGES")} {localizer("ROW_REQUIRED")}");
                        continue;
                    }

                    var imageList = imageUrlsCell.GetValue<string>()
                        ?.Split(';', StringSplitOptions.RemoveEmptyEntries)
                        .Select(url => url.Trim())
                        .Where(url => !string.IsNullOrEmpty(url))
                        .ToList();

                    if (imageList?.Count > CommonConst.MAX_PRODUCT_IMAGE_COUNT)
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("VALIDATE_MAX_IMAGE_EXCEEDED")} {CommonConst.MAX_PRODUCT_IMAGE_COUNT}");
                        continue;
                    }

                    if (imageList == null || !imageList.Any())
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("IMAGES")} {localizer("ROW_REQUIRED")}");
                        continue;
                    }

                    // Validate ItemsInfo
                    var itemsInfoCell = worksheet.Cells[row, 20];
                    if (itemsInfoCell?.Value == null || string.IsNullOrWhiteSpace(itemsInfoCell.GetValue<string>()))
                    {
                        errors.Add($"{localizer("ROW")} {rowView}: {localizer("ITEMS_INFO")} {localizer("ROW_REQUIRED")}");
                        continue;
                    }

                    // Validate Sold
                    var quantitySold = worksheet.Cells[row, 21];
                    if (quantitySold?.Value != null)
                    {
                        if (!long.TryParse(quantitySold.GetValue<string>(), out long sold))
                        {
                            errors.Add($"{localizer("ROW")} {rowView}: {localizer("QUANTITY_SOLD")} {localizer("MUST_BE_NUMBER")}");
                            continue;
                        }
                        productDto.Sold = sold;
                    }

                    // Validate ItemsPosition
                    var itemsPosition = worksheet.Cells[row, 22];
                    if (itemsPosition?.Value != null)
                    {
                        if (!int.TryParse(itemsPosition.GetValue<string>(), out int position))
                        {
                            errors.Add($"{localizer("ROW")} {rowView}: {localizer("ITEMS_POSITION")} {localizer("MUST_BE_NUMBER")}");
                            continue;
                        }
                        productDto.ItemsPosition = position;
                    }

                    string itemsCode = Id64.Generator();
                    Items item = _mapper.Map<Items>(productDto);
                    item.ItemsCode = $"{itemsCode}";
                    item.ItemsType = type;
                    item.Status = TypeStatus.Actived;

                    if (type == TypeItems.Product)
                    {
                        var weightCell = worksheet.Cells[row, 9];
                        var lengthCell = worksheet.Cells[row, 10];
                        var widthCell = worksheet.Cells[row, 11];
                        var heightCell = worksheet.Cells[row, 12];

                        var weight = weightCell?.GetValue<string>();
                        var length = lengthCell?.GetValue<string>();
                        var width = widthCell?.GetValue<string>();
                        var height = heightCell?.GetValue<string>();

                        if (!string.IsNullOrEmpty(weight) && double.TryParse(weight, out double weightValue))
                            item.ItemsWeight = weightValue;
                        if (!string.IsNullOrEmpty(length) && double.TryParse(length, out double lengthValue))
                            item.ItemsLength = lengthValue;
                        if (!string.IsNullOrEmpty(width) && double.TryParse(width, out double widthValue))
                            item.ItemsWidth = widthValue;
                        if (!string.IsNullOrEmpty(height) && double.TryParse(height, out double heightValue))
                            item.ItemsHeight = heightValue;

                        var variantNameOneCell = worksheet.Cells[row, 13];
                        var variantValueOneCell = worksheet.Cells[row, 14];
                        var variantNameTwoCell = worksheet.Cells[row, 15];
                        var variantValueTwoCell = worksheet.Cells[row, 16];
                        var variantNameThreeCell = worksheet.Cells[row, 17];
                        var variantValueThreeCell = worksheet.Cells[row, 18];

                        string variantNameOne = variantNameOneCell?.GetValue<string>();
                        string variantValueOne = variantValueOneCell?.GetValue<string>();
                        string variantNameTwo = variantNameTwoCell?.GetValue<string>();
                        string variantValueTwo = variantValueTwoCell?.GetValue<string>();
                        string variantNameThree = variantNameThreeCell?.GetValue<string>();
                        string variantValueThree = variantValueThreeCell?.GetValue<string>();

                        if (!string.IsNullOrWhiteSpace(variantNameOne) && !string.IsNullOrWhiteSpace(variantValueOne) ||
                            !string.IsNullOrWhiteSpace(variantNameTwo) && !string.IsNullOrWhiteSpace(variantValueTwo) ||
                            !string.IsNullOrWhiteSpace(variantNameThree) && !string.IsNullOrWhiteSpace(variantValueThree))
                        {
                            item.VariantNameOne = variantNameOne;
                            item.VariantValueOne = variantValueOne;
                            item.VariantNameTwo = variantNameTwo;
                            item.VariantValueTwo = variantValueTwo;
                            item.VariantNameThree = variantNameThree;
                            item.VariantValueThree = variantValueThree;
                            item.IsVariant = true;
                        }
                        else
                        {
                            item.IsVariant = false;
                        }

                        // Xử lý hình ảnh
                        var imageUrlsCellProduct = worksheet.Cells[row, 19];
                        var imageUrls = imageUrlsCellProduct?.GetValue<string>()
                            ?.Split(';', StringSplitOptions.RemoveEmptyEntries)
                            .Select(url => url.Trim())
                            .Where(url => !string.IsNullOrEmpty(url))
                            .ToList() ?? new List<string>();

                        await _tempFilesFlow.CreateTempFilesForUrls(shopId, item.ItemsCode, type == TypeItems.Product ? RefTypeEnum.Product : RefTypeEnum.Service, imageUrls);

                        var itemsInfoCellProduct = worksheet.Cells[row, 20];
                        var isTopCell = worksheet.Cells[row, 23];
                        var isShowCell = worksheet.Cells[row, 24];

                        item.ItemsInfo = itemsInfoCellProduct?.GetValue<string>();
                        item.IsTop = isTopCell?.GetValue<string>()?.ToUpper() == "TRUE";
                        item.IsShow = isShowCell?.GetValue<string>()?.ToUpper() == "TRUE";
                        item.TypePublish = item.IsShow == true ? TypePublish.Publish : TypePublish.UnPublish;

                        var warehouse = await _warehouseRepository.FindByShopId(shopId);

                        if (warehouse != null)
                            item.WarehouseId = warehouse.WarehouseId;
                    }
                    else
                    {
                        item.IsVariant = false;
                    }

                    item.PartnerId = baseDto.Partner.PartnerId;
                    item.ShopId = baseDto.Shop.ShopId;
                    itemsList.Add(item);
                }
            }

            if (errors.Any())
            {
                return ResponseBadRequest(new CustomBadRequest(string.Join("; ", errors), this.ControllerContext));
            }

            // Kiểm tra giới hạn gói sau khi đã validate tất cả các dòng
            var importPackageLimitValidation = await ValidatePackageItemsLimit(baseDto.Partner, baseDto.Shop.ShopId, type, itemsList.Count);
            if (importPackageLimitValidation != null)
                return ResponseBadRequest(importPackageLimitValidation);

            var createdItems = await _itemsRepository.CreateItemsAsync(itemsList);

            LogEvent(new EventLogDto
            {
                RefId = baseDto.Partner.PartnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Import,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/ImportProducts",
                Message = $"Imported {createdItems.Count} {type.ToString().ToLower()}s",
                Exception = null,
                DataObject = null
            });

            return Ok(new
            {
                Timestamp = DateTime.UtcNow,
                Message = localizer("IMPORT_SUCCESSFULLY"),
                Data = new { ImportedCount = createdItems.Count }
            });
        }
        catch (Exception ex)
        {
            _log4net.Error(ex);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Import,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/ImportProducts",
                Message = $"Error importing product template for shopId: {shopId}",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTime.UtcNow,
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Xuất danh sách sản phẩm hoặc dịch vụ dưới dạng file Excel dựa trên bộ lọc
    /// </summary>
    /// <param name="model">Bộ lọc để lấy danh sách sản phẩm hoặc dịch vụ</param>
    /// <returns>File Excel chứa danh sách sản phẩm hoặc dịch vụ</returns>
    // GET: api/partner/ItemsPartner/ExportItems
    [HttpGet("ExportItems")]
    public async Task<IActionResult> ExportItems([FromQuery] ItemPartnerFilterDto model)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc,
                PageIndex = 0,
                PageSize = int.MaxValue
            };

            PagingResult<ItemsGroupBy> itemsGroupBy = _itemsRepository.ListQueryGroupByVariant(paging, model.ShopId,
                model.CategoryId, model.SubCategoryId, model.ItemsType);

            foreach (var groupBy in itemsGroupBy.Result)
            {
                if (!groupBy.IsVariant && groupBy.ListVariant?.Count > 0)
                {
                    groupBy.ItemsId = groupBy.ListVariant.FirstOrDefault()?.ItemsId ?? "";
                    groupBy.VariantImage = groupBy.ListVariant.FirstOrDefault()?.VariantImage ?? new MediaInfo();
                    groupBy.VariantNameOne = groupBy.ListVariant.FirstOrDefault()?.VariantNameOne ?? "";
                    groupBy.VariantValueOne = groupBy.ListVariant.FirstOrDefault()?.VariantValueOne ?? "";
                    groupBy.VariantNameTwo = groupBy.ListVariant.FirstOrDefault()?.VariantNameTwo ?? "";
                    groupBy.VariantValueTwo = groupBy.ListVariant.FirstOrDefault()?.VariantValueTwo ?? "";
                    groupBy.VariantNameThree = groupBy.ListVariant.FirstOrDefault()?.VariantNameThree ?? "";
                    groupBy.VariantValueThree = groupBy.ListVariant.FirstOrDefault()?.VariantValueThree ?? "";
                    groupBy.PriceCapital = groupBy.ListVariant.FirstOrDefault()?.PriceCapital ?? 0;
                    groupBy.PriceReal = groupBy.ListVariant.FirstOrDefault()?.PriceReal ?? 0;
                    groupBy.Price = groupBy.ListVariant.FirstOrDefault()?.Price ?? 0;
                    groupBy.Quantity = groupBy.ListVariant.FirstOrDefault()?.Quantity ?? 0;
                    groupBy.QuantityPurchase = groupBy.ListVariant.FirstOrDefault()?.QuantityPurchase ?? 0;
                    groupBy.SellOver = groupBy.ListVariant.FirstOrDefault()?.SellOver ?? false;
                    groupBy.ListVariant = new List<VariantBase>();
                    groupBy.CategoryIds = groupBy.CategoryIds;
                }
            }

            List<ItemsGroupByDto> itemsGroupByDto = _mapper.Map<List<ItemsGroupByDto>>(itemsGroupBy.Result);

            var fileBytes = await _itemsRepository.ExportItemsToExcel(itemsGroupByDto, model.ItemsType, model.ShopId);
            var fileName = model.ItemsType == TypeItems.Product ? $"ListProducts_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx" : $"ListServices_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx";

            UploadStorageDto obj = new UploadStorageDto
            {
                FileBytes = fileBytes,
                PrefixPath = ExportConst.PATH_IMPORT,
                Type = TypeMedia.FILE,
                FileName = fileName
            };

            MediaFile objMedia = await _storageRepository.UploadFileAsync(obj);

            if (objMedia != null)
            {
                MediaFileDto objMediaDto = _mapper.Map<MediaFileDto>(objMedia);

                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = objMediaDto,
                    Message = localizer("SUCCESS")
                });
            }
            else
            {
                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = new MediaFileDto(),
                    Message = localizer("FAIL")
                });
            }
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Export,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/ExportItems",
                Message = $"Error exporting {model.ItemsType.ToString().ToLower()}s to Excel",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ItemsPartner/ExportItems", ex);
        }
    }

    #region Private methods
    /// <summary>
    /// Kiểm tra giới hạn số lượng sản phẩm/dịch vụ theo gói cho trường hợp import
    /// </summary>
    /// <param name="partner">Thông tin partner</param>
    /// <param name="shopId">ID của shop</param>
    /// <param name="itemsType">Loại items (Product/Service)</param>
    /// <param name="count">Số lượng items mới</param>
    /// <returns>CustomBadRequest nếu vượt quá giới hạn, null nếu hợp lệ</returns>
    private async Task<CustomBadRequest?> ValidatePackageItemsLimit(Partner partner, string shopId, TypeItems itemsType, int count = 1)
    {
        var package = await _partnerFunctionRepository.GetPackageHistoryAsync(partner.PartnerId);

        if (package == null)
            return new CustomBadRequest(localizer("PACKAGE_NOT_FOUND"), this.ControllerContext);

        var currentTotal = _itemsRepository.TotalItems(partner.ParentId, shopId);
        var totalAfter = currentTotal + count;

        if (totalAfter > package.ItemsQuantity)
        {
            var limitMessage = itemsType == TypeItems.Product
                ? localizer("PRODUCT_LIMIT_ITEMS")
                : localizer("SERVICE_LIMIT_ITEMS");
            return new CustomBadRequest(limitMessage, this.ControllerContext);
        }

        return null;
    }

    private async Task<CustomBadRequest?> ValidateItemOptions(ProductDto model)
    {
        if (!model.ExtraItemOptionGroups.Any())
            return null;

        var itemOptionGroupIds = model.ExtraItemOptionGroups.Select(d => d.ItemOptionGroupId).ToList();
        if (!await _itemOptionGroupRepository.CheckExistItemOptionGroup(itemOptionGroupIds))
            return new CustomBadRequest(localizer("ITEM_OPTION_GROUP_NOT_FOUND"), ControllerContext);

        var allItemOptionsGroupBy = await _itemOptionRepository.GetItemOptionsByGroupIds(itemOptionGroupIds);
        var allItemOptionsDict = allItemOptionsGroupBy
            .GroupBy(d => d.ItemOptionGroupId)
            .ToDictionary(g => g.Key, g => g.Select(i => i.ItemOptionId).ToList());

        foreach (var itemOptionGroup in model.ExtraItemOptionGroups)
        {
            if (!allItemOptionsDict.TryGetValue(itemOptionGroup.ItemOptionGroupId, out var listItemOptions))
                return new CustomBadRequest(localizer("ITEM_OPTION_NOT_FOUND"), ControllerContext);

            var notExistedItemOptions = itemOptionGroup.ItemOptionIds.Except(listItemOptions).ToList();
            if (notExistedItemOptions.Any())
                return new CustomBadRequest(localizer("ITEM_OPTION_NOT_FOUND"), ControllerContext);
        }

        return null;
    }

    private CustomBadRequest? ValidateProductInput(ProductDto model)
    {
        if (string.IsNullOrEmpty(model.ItemsName))
            return new CustomBadRequest(localizer("PRODUCT_NAME_INVALID"), this.ControllerContext);
        if (model.Images == null || model.Images.Count == 0)
            return new CustomBadRequest(localizer("PRODUCT_IMAGES_INVALID"), this.ControllerContext);
        foreach (var image in model.Images)
        {
            if (string.IsNullOrEmpty(image.Link))
                return new CustomBadRequest(localizer("PRODUCT_IMAGES_LINK_INVALID"), this.ControllerContext);
        }
        if (model.ItemsType == TypeItems.Product && string.IsNullOrEmpty(model.WarehouseId))
            return new CustomBadRequest(localizer("PRODUCT_WAREHOUSE_INVALID"), this.ControllerContext);

        if (model.CustomTaxRate != null && model.CustomTaxRate < 0)
            return new CustomBadRequest(localizer("TAX_RATE_CANNOT_BE_NEGATIVE"), this.ControllerContext);

        if (model.CategoryIds.Count == 0)
            return new CustomBadRequest(localizer("PRODUCT_CATEGORY_INVALID"), this.ControllerContext);

        return null;
    }
    #endregion

    /// <summary>
    /// Delete item for shop (Xoá item cho cửa hàng, xoá tấc cả biến thể đi kèm)
    /// </summary>
    /// <param name="shopId"></param>
    /// <param name="itemsType"></param>
    /// <param name="itemCodes"></param>
    /// <returns>The result DeleteProduct for shop</returns>
    // DELETE: api/partner/ItemsPartner/DeleteProduct
    [HttpDelete("DeleteItems/{shopId}")]
    [MultiPolicysAuthorizeAttribute(Policys = RolePrefix.Partner, Rules = "")]
    public async Task<IActionResult> DeleteItems(string shopId, [FromQuery] TypeItems itemsType, [FromBody] List<string> itemCodes)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                    this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId)
                return ResponseBadRequest(new CustomBadRequest(itemsType == TypeItems.Product ? localizer("PRODUCT_NOT_YOURS") : localizer("SERVICE_NOT_YOURS"), this.ControllerContext));

            foreach (var itemCode in itemCodes)
            {
                List<Items> itemsList = _itemsRepository.FindByItemsCode(itemCode);
                if (itemsList == null || !itemsList.Any())
                    return ResponseBadRequest(new CustomBadRequest(itemsType == TypeItems.Product ? localizer("PRODUCT_NOT_YOURS") : localizer("SERVICE_NOT_YOURS"), this.ControllerContext));

                foreach (var items in itemsList)
                {
                    await _itemsFlow.DeleteItems(items.ItemsId);
                }
            }

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/DeleteItems/{shopId}",
                Message = $"Partner delete Items",
                Exception = null,
                DataObject = itemCodes
            });

            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true, Message = itemsType == TypeItems.Product ? localizer("PRODUCT_DELETE_SUCCESS") : localizer("SERVICE_DELETE_SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/ItemsPartner/DeleteItems/{shopId}",
                Message = $"Error Partner delete Items",
                Exception = null,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ItemsPartner/DeleteProduct", ex);
        }
    }

    // Thêm hàm RemoveDiacritics vào trong class ItemsPartnerController
    public static string RemoveDiacritics(string text)
    {
        if (string.IsNullOrEmpty(text)) return "";
        var normalizedString = text.Normalize(System.Text.NormalizationForm.FormD);
        var stringBuilder = new System.Text.StringBuilder();

        foreach (var c in normalizedString)
        {
            var unicodeCategory = System.Globalization.CharUnicodeInfo.GetUnicodeCategory(c);
            if (unicodeCategory != System.Globalization.UnicodeCategory.NonSpacingMark)
            {
                stringBuilder.Append(c);
            }
        }
        return stringBuilder.ToString().Normalize(System.Text.NormalizationForm.FormC);
    }

    /// <summary>
    /// Làm sạch HTML content từ text editor, loại bỏ các khoảng trắng không cần thiết
    /// </summary>
    /// <param name="htmlContent">Nội dung HTML từ text editor</param>
    /// <returns>Nội dung đã được làm sạch</returns>
    private static string CleanHtmlContent(string htmlContent)
    {
        if (string.IsNullOrEmpty(htmlContent))
            return htmlContent;

        // Loại bỏ tất cả các khoảng trắng không ngắt dòng (&nbsp;) liên tiếp
        var cleaned = Regex.Replace(htmlContent, @"&nbsp;(\s*&nbsp;)*", "&nbsp;");

        // Loại bỏ các khoảng trắng thường liên tiếp
        cleaned = Regex.Replace(cleaned, @"\s+", " ");

        // Loại bỏ các thẻ <br> liên tiếp
        cleaned = Regex.Replace(cleaned, @"<br\s*/?>\s*<br\s*/?>", "<br>");

        // Loại bỏ các thẻ <p> rỗng hoặc chỉ chứa khoảng trắng
        cleaned = Regex.Replace(cleaned, @"<p>\s*</p>", "");
        cleaned = Regex.Replace(cleaned, @"<p>\s*&nbsp;\s*</p>", "");

        // Loại bỏ các thẻ <p> chỉ chứa <br>
        cleaned = Regex.Replace(cleaned, @"<p>\s*<br\s*/?>\s*</p>", "");

        // Loại bỏ các thẻ <p> chỉ chứa khoảng trắng và <br>
        cleaned = Regex.Replace(cleaned, @"<p>\s*&nbsp;\s*<br\s*/?>\s*</p>", "");

        // Trim khoảng trắng đầu cuối
        cleaned = cleaned.Trim();

        // Nếu sau khi làm sạch chỉ còn lại thẻ HTML rỗng, trả về chuỗi rỗng
        if (cleaned == "<p></p>" || cleaned == "<p>&nbsp;</p>" || cleaned == "<br>" ||
            cleaned == "<p><br></p>" || cleaned == "<p>&nbsp;<br></p>")
            return "";

        return cleaned;
    }
}
