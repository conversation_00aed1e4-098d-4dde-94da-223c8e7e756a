using System.ComponentModel.DataAnnotations;
using App.Base.Middleware;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MongoDB.Bson;

namespace App.ECommerce.Controllers.API
{
    [ApiController]
    [Produces("application/json")]
    [Route(RoutePrefix.API_PARTNER)]
    [ApiExplorerSettings(GroupName = "partner-v1")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
    public class PartnerFunctionController : BaseController
    {
        private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(PartnerFunctionController));
        private readonly IPartnerFunctionRepository _partnerFunctionRepository;
        private readonly IPartnerRepository _partnerRepository;
        private readonly IPartner_Balance_LogRepository _partnerBalanceLogRepository;

        public PartnerFunctionController(
            IStringLocalizer localizer,
            IPartnerFunctionRepository partnerFunctionRepository,
            IPartnerRepository partnerRepository,
            IPartner_Balance_LogRepository partnerBalanceLogRepository) : base(localizer)
        {
            _partnerFunctionRepository = partnerFunctionRepository;
            _partnerRepository = partnerRepository;
            _partnerBalanceLogRepository = partnerBalanceLogRepository;
        }

        /// <summary>
        /// Mua gói chức năng
        /// </summary>
        /// <param name="dto">Thông tin gói chức năng cần mua (PackageId, PaymentMethod, InvoiceNumber)</param>
        /// <returns>Trả về thông tin lịch sử mua gói nếu thành công, hoặc mã lỗi nếu thất bại</returns>
        [HttpPost("purchase-package")]
        public async Task<IActionResult> PurchasePackage([FromBody] PurchasePackageDto dto)
        {
            try
            {
                string partnerId = GetUserIdAuth();

                if (string.IsNullOrEmpty(partnerId))
                {
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
                }

                var partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null || !string.IsNullOrEmpty(partner.ParentId))
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));
                }

                var functionPackage = await _partnerFunctionRepository.GetPackageByIdAsync(dto.PackageId);

                if (functionPackage == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PACKAGE_NOT_FOUND"), this.ControllerContext));

                var existingPackage = await _partnerFunctionRepository.CheckExistingPackageAsync(partnerId, dto.PackageId);

                if (existingPackage != null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PACKAGE_ACTIVATED"), this.ControllerContext));

                if (partner.Balance < functionPackage.Price)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("INSUFFICIENT_BALANCE"), this.ControllerContext));
                }

                var packageHistory = new PartnerPackageHistory
                {
                    Id = Guid.NewGuid(),
                    PartnerId = partnerId,
                    PackageId = dto.PackageId,
                    StartDate = DateTime.UtcNow,
                    EndDate = DateTime.UtcNow.AddDays(functionPackage.DurationDays),
                    Price = functionPackage.Price,
                    PaymentMethod = dto.PaymentMethod,
                    InvoiceNumber = dto.InvoiceNumber,
                    Status = TypeStatus.Actived,
                    CreatedBy = partnerId,
                    CreatedDate = DateTime.UtcNow
                };

                packageHistory = await _partnerFunctionRepository.InsertPartnerPackageHistoryAsync(packageHistory);

                partner.Balance -= functionPackage.Price;
                _partnerRepository.UpdatePartnerBalance(partnerId, partner.Balance);

                var objLog = new Partner_Balance_Log
                {
                    PartnerId = partnerId,
                    RefType = PartnerBalanceRefEnum.BuyPackage,
                    RefId = packageHistory.Id.ToString(),
                    Type = PartnerBalanceEnum.Adjustment,
                    Amount = functionPackage.Price,
                    BalanceAfterTransaction = partner.Balance,
                    Message = $"Mua gói {functionPackage.Name} (ID: {dto.PackageId})",
                    CreatedBy = partnerId,
                    Status = PartnerBalanceStatus.Success
                };

                await _partnerBalanceLogRepository.Create(objLog);

                var response = new
                {
                    PackageHistoryId = packageHistory.Id,
                    PackageName = functionPackage.Name,
                    Price = packageHistory.Price,
                    StartDate = packageHistory.StartDate,
                    EndDate = packageHistory.EndDate,
                    Status = packageHistory.Status
                };

                return ResponseData(new { Timestamp = DateTimes.Now(), Message = localizer("PURCHASE_SUCCESSFULLY"), response });
            }
            catch (Exception ex)
            {
                _log4net.Error("Lỗi khi mua gói chức năng", ex);
                return StatusCode(500, new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer("INTERNAL_SERVER_ERROR"),
                    Data = (object)null
                });
            }
        }

        [HttpPost("upgrade-package")]
        public async Task<IActionResult> UpgradePackage([FromBody] PurchasePackageDto dto)
        {
            try
            {
                string partnerId = GetUserIdAuth();
                if (string.IsNullOrEmpty(partnerId))
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

                var partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null || !string.IsNullOrEmpty(partner.ParentId))
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));

                var existingPackage = await _partnerFunctionRepository.CheckExistingPackageAsync(partnerId, dto.PackageId);
                if (existingPackage != null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PACKAGE_ACTIVATED"), this.ControllerContext));

                var newPackage = await _partnerFunctionRepository.GetPackageByIdAsync(dto.PackageId);
                if (newPackage == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PACKAGE_NOT_FOUND"), this.ControllerContext));

                var packageHistory = await _partnerFunctionRepository.GetPackageHistoryAsync(partnerId);
                if (packageHistory == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("NO_ACTIVE_PACKAGE"), this.ControllerContext));

                var oldPackage = await _partnerFunctionRepository.GetPackageByIdAsync(packageHistory.PackageId);
                if (oldPackage == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("OLD_PACKAGE_NOT_FOUND"), this.ControllerContext));

                // Kiểm tra nếu gói mới có level thấp hơn hoặc bằng gói cũ thì không cho phép
                if (newPackage.Level <= oldPackage.Level)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PACKAGE_DOWNGRADE_NOT_ALLOWED"), this.ControllerContext));

                // Vô hiệu hóa gói hiện tại
                // packageHistory.EndDate = DateTime.UtcNow;
                packageHistory.Status = TypeStatus.InActived;
                await _partnerFunctionRepository.UpdatePartnerPackageHistoryAsync(packageHistory);

                // Tạo lịch sử gói mới
                var newPackageHistory = new PartnerPackageHistory
                {
                    Id = Guid.NewGuid(),
                    PartnerId = partnerId,
                    PackageId = dto.PackageId,
                    StartDate = DateTime.UtcNow,
                    EndDate = DateTime.UtcNow.AddDays(newPackage.DurationDays),
                    Price = newPackage.Price,
                    PaymentMethod = dto.PaymentMethod,
                    InvoiceNumber = dto.InvoiceNumber,
                    Status = TypeStatus.Actived,
                    CreatedBy = partnerId,
                    CreatedDate = DateTime.UtcNow
                };
                await _partnerFunctionRepository.InsertPartnerPackageHistoryAsync(newPackageHistory);

                partner.Balance -= newPackage.Price;
                _partnerRepository.UpdatePartnerBalance(partnerId, partner.Balance);

                // Ghi log giao dịch
                var objLog = new Partner_Balance_Log
                {
                    PartnerId = partnerId,
                    RefType = PartnerBalanceRefEnum.InitialBalance,
                    RefId = newPackageHistory.Id.ToString(),
                    Type = PartnerBalanceEnum.Adjustment,
                    Amount = Math.Abs(newPackage.Price),
                    BalanceAfterTransaction = partner.Balance,
                    Message = $"Nâng cấp từ gói {oldPackage.Name} (ID: {packageHistory.PackageId}) sang gói {newPackage.Name} (ID: {dto.PackageId})",
                    CreatedBy = partnerId,
                    Status = PartnerBalanceStatus.Success
                };
                await _partnerBalanceLogRepository.Create(objLog);

                // Chuẩn bị phản hồi
                var response = new
                {
                    PackageHistoryId = newPackageHistory.Id,
                    PackageName = newPackage.Name,
                    Price = newPackageHistory.Price,
                    StartDate = newPackageHistory.StartDate,
                    EndDate = newPackageHistory.EndDate,
                    Status = newPackageHistory.Status
                };

                return ResponseData(new { Timestamp = DateTimes.Now(), Message = localizer("UPGRADE_SUCCESSFULLY"), response });
            }
            catch (Exception ex)
            {
                _log4net.Error("Lỗi khi nâng cấp gói chức năng", ex);
                return StatusCode(500, new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer("INTERNAL_SERVER_ERROR"),
                    Data = (object)null
                });
            }
        }

        /// <summary>
        /// Lấy danh sách chức năng của gói đang hoạt động
        /// </summary>
        /// <returns>Trả về gói đang hoạt động cùng các chức năng liên quan, hoặc danh sách rỗng nếu không có gói nào</returns>
        [HttpGet("active-package-functions")]
        public async Task<IActionResult> GetActivePackageFunctions()
        {
            try
            {
                string partnerId = GetUserIdAuth();
                if (string.IsNullOrEmpty(partnerId))
                {
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
                }

                var partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));
                }

                List<Function> functions;

                var packageHistory = await _partnerFunctionRepository.GetPackageHistoryAsync(string.IsNullOrEmpty(partner.ParentId) ? partnerId : partner.ParentId);

                if (packageHistory == null)
                {
                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Message = localizer("SUCCESS"),
                    });
                }
                var activePackage = await _partnerFunctionRepository.GetPackageByIdAsync(packageHistory.PackageId);

                if (activePackage == null)
                {
                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Message = localizer("SUCCESS"),
                    });
                }

                functions = await _partnerFunctionRepository.GetFunctionsByPackageIdAsync(activePackage.PackageId);

                if (!string.IsNullOrEmpty(partner.ParentId))
                {
                    var partnerRoleFunctions = await _partnerFunctionRepository.GetFunctionsByEmployeeRolesAsync(partnerId);
                    functions = functions.Select(f =>
                    {
                        var prf = partnerRoleFunctions.FirstOrDefault(pr => pr.FunctionId == f.FunctionId);
                        f.PermissionStrings = prf?.Permissions?.Select(p => p.ToString()).ToList() ?? new List<string>();
                        return f;
                    }).ToList();
                }

                var functionTree = BuildFunctionTree(functions);
                var response = new
                {
                    PackageId = activePackage.PackageId,
                    PackageName = activePackage?.Name,
                    PackageCode = activePackage?.Code,
                    StartDate = packageHistory.StartDate,
                    EndDate = packageHistory.EndDate,
                    Functions = functionTree
                };

                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer("SUCCESS"),
                    Data = response
                });
            }
            catch (Exception ex)
            {
                _log4net.Error("Lỗi khi lấy danh sách chức năng theo gói đang dùng", ex);
                return StatusCode(500, new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer("INTERNAL_SERVER_ERROR"),
                    Data = (object)null
                });
            }
        }

        /// <summary>
        /// Lấy lịch sử mua gói của đối tác
        /// </summary>
        /// <param name="skip">Số bản ghi bỏ qua</param>
        /// <param name="limit">Số bản ghi tối đa trả về</param>
        /// <returns>Trả về danh sách lịch sử mua gói của đối tác với phân trang</returns>
        [HttpGet("package-history")]
        public async Task<IActionResult> GetPackageHistory([FromQuery] int skip = 0, [FromQuery] int limit = 99)
        {
            try
            {
                string partnerId = GetUserIdAuth();
                if (string.IsNullOrEmpty(partnerId))
                {
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
                }

                var partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null || !string.IsNullOrEmpty(partner.ParentId))
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));
                }

                Paging paging = new Paging()
                {
                    PageIndex = skip / (limit == 0 ? 1 : limit),
                    PageSize = limit,
                    NameType = TypeSortName.Created,
                    SortType = TypeSort.desc
                };

                PagingResult<PartnerPackageHistory> packageHistoriesResult = await _partnerFunctionRepository.GetPackageHistoriesAsync(partnerId, paging);

                var response = packageHistoriesResult.Result.Select(async ph =>
                {
                    var package = await _partnerFunctionRepository.GetPackageByIdAsync(ph.PackageId);
                    return new
                    {
                        PackageHistoryId = ph.Id,
                        PackageName = package?.Name,
                        PackageCode = package?.Code,
                        Price = ph.Price,
                        StartDate = ph.StartDate,
                        EndDate = ph.EndDate,
                        Status = ph.Status,
                        PaymentMethod = ph.PaymentMethod,
                        InvoiceNumber = ph.InvoiceNumber,
                        CreatedDate = ph.CreatedDate
                    };
                }).Select(t => t.Result).ToList();

                return ResponseData(new
                {
                    data = response,
                    skip,
                    limit,
                    total = packageHistoriesResult.Total
                });
            }
            catch (Exception ex)
            {
                _log4net.Error("Lỗi khi lấy lịch sử mua gói", ex);
                return StatusCode(500, new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer("INTERNAL_SERVER_ERROR"),
                    Data = (object)null
                });
            }
        }

        /// <summary>
        /// Lấy danh sách các gói chức năng có sẵn để xem và đăng ký
        /// </summary>
        /// <returns>Trả về danh sách các gói chức năng có sẵn cùng chi tiết chức năng, hoặc danh sách rỗng nếu không có gói nào</returns>
        [HttpGet("available-packages")]
        public async Task<IActionResult> GetAvailablePackages()
        {
            try
            {
                string partnerId = GetUserIdAuth();
                if (string.IsNullOrEmpty(partnerId))
                {
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
                }

                var partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));
                }

                var packages = await _partnerFunctionRepository.GetAvailableFunctionPackagesAsync();

                var packageHistory = await _partnerFunctionRepository.GetPackageHistoryAsync(string.IsNullOrEmpty(partner.ParentId) ? partnerId : partner.ParentId);

                FunctionPackage activePackage = null;
                if (packageHistory != null)
                {
                    activePackage = await _partnerFunctionRepository.GetPackageByIdAsync(packageHistory.PackageId);
                }

                var responsePackages = packages.Select(p => new
                {
                    Id = p.PackageId,
                    Code = p.Code,
                    Name = p.Name,
                    Level = p.Level,
                    Description = p.Description,
                    Detail = p.Detail,
                    Price = p.Price,
                    DurationDays = p.DurationDays,
                    IsActive = activePackage != null && activePackage.PackageId == p.PackageId,
                }).ToList();

                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer("SUCCESS"),
                    Data = responsePackages
                });
            }
            catch (Exception ex)
            {
                _log4net.Error("Lỗi khi lấy danh sách gói chức năng có sẵn", ex);
                return StatusCode(500, new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer("INTERNAL_SERVER_ERROR"),
                    Data = (object)null
                });
            }
        }

        private List<FunctionTreeDto> BuildFunctionTree(List<Function> functions)
        {
            var functionDtos = functions.Select(f => new FunctionTreeDto
            {
                FunctionId = f.FunctionId,
                FunctionCode = f.Code,
                FunctionName = f.Name,
                URL = f.URL,
                Icon = f.Icon,
                OrderNumber = f.OrderNumber,
                Description = f.Description,
                Permissions = f.PermissionStrings,
                ParentId = f.ParentId,
                Children = new List<FunctionTreeDto>()
            }).ToList();

            var lookup = functionDtos.ToDictionary(f => f.FunctionId, f => f);

            var tree = new List<FunctionTreeDto>();

            foreach (var func in functionDtos)
            {
                if (string.IsNullOrEmpty(func.ParentId))
                {
                    tree.Add(func);
                }
                else if (lookup.TryGetValue(func.ParentId, out var parent))
                {
                    parent.Children.Add(func);
                }
            }

            tree = tree.OrderBy(f => f.OrderNumber).ToList();
            foreach (var node in tree)
            {
                node.Children = node.Children.OrderBy(c => c.OrderNumber).ToList();
            }

            return tree;
        }

        /// <summary>
        /// Kiểm tra quyền truy cập cho URL và permission cụ thể
        /// </summary>
        /// <param name="dto">Thông tin URL và permission cần kiểm tra</param>
        /// <returns>Trả về true nếu có quyền, false nếu không có quyền</returns>
        /// <summary>
        [HttpPost("check-permission")]
        public async Task<IActionResult> CheckPermission([FromBody] CheckPermissionDto dto)
        {
            try
            {
                string partnerId = GetUserIdAuth();
                if (string.IsNullOrEmpty(partnerId))
                {
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
                }

                var partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));
                }

                // Danh sách các URL chỉ dành cho đại lý
                var agencyOnlyUrls = new List<string>
                {
                    "/dashboard/settings/auth",
                    "/dashboard/settings/service-package-management",
                    "/dashboard/settings/management-role",
                    "/dashboard/settings/employee-role",
                    "/dashboard/settings/add-role",
                    "/dashboard/settings/update-role",
                    "/dashboard/package"
                };

                bool isAgency = string.IsNullOrEmpty(partner.ParentId);

                // Nếu là đại lý và URL nằm trong agencyOnlyUrls, cho phép truy cập ngay lập tức
                if (isAgency && agencyOnlyUrls.Contains(dto.Url, StringComparer.OrdinalIgnoreCase))
                {
                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Message = localizer("PERMISSION_GRANTED"),
                        Data = new { HasPermission = true, IsAgency = true }
                    });
                }

                // Nếu URL chỉ dành cho đại lý và người dùng là nhân viên (có ParentId)
                if (agencyOnlyUrls.Contains(dto.Url, StringComparer.OrdinalIgnoreCase) && !isAgency)
                {
                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Message = localizer("PERMISSION_DENIED"),
                        Data = new { HasPermission = false, IsAgency = false }
                    });
                }

                List<Function> functions;

                var packageHistory = await _partnerFunctionRepository.GetPackageHistoryAsync(isAgency ? partnerId : partner.ParentId);

                FunctionPackage activePackage = null;
                if (packageHistory != null)
                {
                    activePackage = await _partnerFunctionRepository.GetPackageByIdAsync(packageHistory.PackageId);
                }

                if (activePackage == null)
                {
                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Message = localizer("NO_ACTIVE_PACKAGES"),
                        Data = new { HasPermission = false, IsAgency = isAgency }
                    });
                }

                functions = await _partnerFunctionRepository.GetFunctionsByPackageIdAsync(activePackage.PackageId);

                if (!isAgency)
                {
                    var partnerRoleFunctions = await _partnerFunctionRepository.GetFunctionsByEmployeeRolesAsync(partnerId);
                    functions = functions.Select(f =>
                    {
                        var prf = partnerRoleFunctions.FirstOrDefault(pr => pr.FunctionId == f.FunctionId);
                        f.PermissionStrings = prf?.Permissions?.Select(p => FunctionConst.MapToPermissionString(p)).ToList() ?? new List<string>();
                        return f;
                    }).ToList();
                }

                var matchingFunction = functions.FirstOrDefault(f => f.URL.Equals(dto.Url, StringComparison.OrdinalIgnoreCase));

                if (matchingFunction == null)
                {
                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Message = localizer("FUNCTION_NOT_FOUND"),
                        Data = new { HasPermission = false, IsAgency = isAgency }
                    });
                }

                bool hasPermission = matchingFunction.PermissionStrings.Contains(dto.Permission, StringComparer.OrdinalIgnoreCase);

                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer(hasPermission ? "PERMISSION_GRANTED" : "PERMISSION_DENIED"),
                    Data = new { HasPermission = hasPermission, IsAgency = isAgency }
                });
            }
            catch (Exception ex)
            {
                _log4net.Error("Lỗi khi kiểm tra quyền truy cập", ex);
                return StatusCode(500, new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer("INTERNAL_SERVER_ERROR"),
                    Data = new { HasPermission = false, IsAgency = false }
                });
            }
        }

        /// <summary>
        /// Lấy tất cả quyền truy cập cho đối tác hoặc nhân viên
        /// </summary>
        /// <returns>Trả về danh sách tất cả URL và quyền liên quan, cùng trạng thái isAgency</returns>
        [HttpGet("get-all-permissions")]
        public async Task<IActionResult> GetAllPermissions()
        {
            try
            {
                string partnerId = GetUserIdAuth();
                if (string.IsNullOrEmpty(partnerId))
                {
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
                }

                var partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                {
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));
                }

                bool isAgency = string.IsNullOrEmpty(partner.ParentId);
                string targetPartnerId = isAgency ? partnerId : partner.ParentId;

                var agencyOnlyUrls = new List<string>
        {
            "/dashboard/settings/auth",
            "/dashboard/settings/service-package-management",
            "/dashboard/settings/management-role",
            "/dashboard/settings/employee-role",
            "/dashboard/settings/add-role",
            "/dashboard/settings/update-role",
            "/dashboard/package"
        };

                List<Function> functions;

                if (isAgency)
                {
                    var packageHistory = await _partnerFunctionRepository.GetPackageHistoryAsync(partnerId);
                    FunctionPackage activePackage = packageHistory != null ? await _partnerFunctionRepository.GetPackageByIdAsync(packageHistory.PackageId) : null;
                    functions = activePackage != null
                        ? await _partnerFunctionRepository.GetFunctionsByPackageIdAsync(activePackage.PackageId)
                        : await _partnerFunctionRepository.GetAllFunctionsAsync();
                }
                else
                {
                    var packageHistory = await _partnerFunctionRepository.GetPackageHistoryAsync(targetPartnerId);
                    FunctionPackage activePackage = packageHistory != null ? await _partnerFunctionRepository.GetPackageByIdAsync(packageHistory.PackageId) : null;
                    functions = activePackage != null ? await _partnerFunctionRepository.GetFunctionsByPackageIdAsync(activePackage.PackageId) : new List<Function>();

                    var partnerRoleFunctions = await _partnerFunctionRepository.GetFunctionsByEmployeeRolesAsync(partnerId);
                    functions = functions.Select(f =>
                    {
                        var prf = partnerRoleFunctions.FirstOrDefault(pr => pr.FunctionId == f.FunctionId);
                        f.PermissionStrings = prf?.Permissions?.Select(p => FunctionConst.MapToPermissionString(p)).ToList() ?? new List<string>();
                        return f;
                    }).Where(f => f.PermissionStrings.Any()).ToList();
                }

                var permissions = new List<object>();

                permissions.AddRange(functions
                    .Where(f => !string.IsNullOrEmpty(f.URL))
                    .Select(f => new
                    {
                        url = f.URL,
                        permissions = f.PermissionStrings ?? new List<string>()
                    })
                    .DistinctBy(p => p.url));

                if (isAgency)
                {
                    permissions.AddRange(agencyOnlyUrls.Select(url => new
                    {
                        url,
                        permissions = new List<string> { "VIEW", "ADD", "EDIT", "DELETE" }
                    }));
                }

                var response = new
                {
                    permissions,
                    isAgency
                };

                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer("SUCCESS"),
                    Data = response
                });
            }
            catch (Exception ex)
            {
                _log4net.Error("Lỗi khi lấy tất cả quyền truy cập", ex);
                return StatusCode(500, new
                {
                    Timestamp = DateTimes.Now(),
                    Message = localizer("INTERNAL_SERVER_ERROR"),
                    Data = new { permissions = new List<object>(), isAgency = false }
                });
            }
        }
    }
}