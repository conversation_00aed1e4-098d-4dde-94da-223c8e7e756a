using App.Base.Middleware;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Setting;
using App.ECommerce.Units;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class GameShopController : BaseController
{
    private readonly IStringLocalizer _localizer;
    private readonly IGamificationFlow _gamificationFlow;
    private readonly ILog _log = LogManager.GetLogger(typeof(GameShopController));

    public GameShopController(IStringLocalizer localizer, IGamificationFlow gamificationFlow) : base(localizer)
    {
        _localizer = localizer;
        _gamificationFlow = gamificationFlow;
    }

    [HttpGet]
    public async Task<IActionResult> GetGameShop()
    {
        try
        {
            var games = await _gamificationFlow.GetAllGamesAsync();
            return ResponseData(new { Timestamp = DateTimes.Now(), Result =  });
        }
        catch (System.Exception ex)
        {
            _log.Error($"Error in GetAllGames: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }
}