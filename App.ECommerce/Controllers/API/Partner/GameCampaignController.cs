using App.Base.Middleware;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Resource.Dtos.GamificationDtos;
using App.ECommerce.Setting;
using App.ECommerce.Units;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

using System.ComponentModel.DataAnnotations;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class GameCampaignController : BaseController
{
    private readonly IStringLocalizer _localizer;
    private readonly IGamificationFlow _gamificationFlow;
    private readonly ILog _log = LogManager.GetLogger(typeof(GameCampaignController));

    public GameCampaignController(IStringLocalizer localizer, IGamificationFlow gamificationFlow) : base(localizer)
    {
        _localizer = localizer;
        _gamificationFlow = gamificationFlow;
    }

    [HttpGet]
    public async Task<IActionResult> GetList([FromQuery] string shopId)
    {
        try
        {
            var data = await _gamificationFlow.GetListCampaign(shopId);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = data });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in GetListCampaign for shopId {shopId}: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(string id)
    {
        try
        {
            var data = await _gamificationFlow.GetCampaign(id);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = data });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in GetCampaign {id}: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromForm] GameCampaignDto gameCampaign)
    {
        try
        {
            var data = await _gamificationFlow.CreateCampaign(gameCampaign);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = data });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in CreateCampaign for shopId {gameCampaign.ShopId}: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpPut("{id}/activate")]
    public async Task<IActionResult> Activate(string id, [FromQuery][Required] string shopId)
    {
        try
        {
            await _gamificationFlow.ActiveCampaign(shopId, id);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in Activate for shopId {shopId}, campaignId {id}: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

}