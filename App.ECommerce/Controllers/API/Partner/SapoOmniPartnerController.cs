using App.Base.Middleware;
using App.ECommerce.Setting;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Units;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Resource.Model;
using System.Net.Http;
using System.Text;
using System.Text.Json.Nodes;
using Newtonsoft.Json;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class SapoOmniPartnerController : BaseController
{
    private readonly IStringLocalizer _localizer;
    private readonly ISapoOmniFlow _sapoOmniFlow;
    private readonly ILog _log = LogManager.GetLogger(typeof(SapoOmniPartnerController));
    private readonly IBaseFlow _baseFlow;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;

    public SapoOmniPartnerController(
        IStringLocalizer localizer, 
        ISapoOmniFlow sapoOmniFlow, 
        IBaseFlow baseFlow,
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration
    ) : base(localizer)
    {
        _localizer = localizer;
        _sapoOmniFlow = sapoOmniFlow;
        _baseFlow = baseFlow;
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
    }

    /// <summary>
    /// Lấy thông tin cấu hình SapoOmni theo domain
    /// </summary>
    /// <param name="shopId">Shop ID</param>
    /// <param name="domainApi">Domain API của SapoOmni</param>
    /// <returns>Thông tin cấu hình</returns>
    [HttpGet]
    public async Task<IActionResult> GetSapoOmniConfig(string shopId, string domainApi)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) 
                return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var config = await _sapoOmniFlow.GetConfigByDomainApiAsync(domainApi, shopId);
            return ResponseData(new { 
                Timestamp = DateTimes.Now(), 
                Result = config 
            });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in GetSapoOmniConfig: {ex.Message}", ex);
            return ResponseData(new { 
                Timestamp = DateTimes.Now(), 
                Result = false, 
                Message = ex.Message 
            });
        }
    }

    /// <summary>
    /// Tạo kết nối mới với SapoOmni
    /// </summary>
    /// <param name="dto">Thông tin kết nối SapoOmni</param>
    /// <returns>Kết quả tạo kết nối</returns>
    [HttpPost]
    public async Task<IActionResult> CreateSapoOmniConfig([FromBody] SapoOmniConfigDto dto)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), dto.ShopId);
            if (!baseDto.IsSuccess) 
                return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            // Kiểm tra kết nối và tạo webhook
            var webhookIds = await CreateWebHooksAsync(dto.DomainApi, dto.AccessToken);
            if (webhookIds == null || webhookIds.Count == 0)
            {
                return ResponseData(new { 
                    Timestamp = DateTimes.Now(), 
                    Result = false, 
                    Message = "Không thể tạo webhook hoặc kết nối thất bại" 
                });
            }

            var saved = await _sapoOmniFlow.SaveConfigAsync(dto);
            
            return ResponseData(new { 
                Timestamp = DateTimes.Now(), 
                Result = saved,
                Message = saved ? "Tạo kết nối SapoOmni thành công" : "Tạo kết nối SapoOmni thất bại"
            });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in CreateSapoOmniConfig: {ex.Message}", ex);
            return ResponseData(new { 
                Timestamp = DateTimes.Now(), 
                Result = false, 
                Message = ex.Message 
            });
        }
    }

    /// <summary>
    /// Cập nhật cấu hình SapoOmni
    /// </summary>
    /// <param name="dto">Thông tin cập nhật</param>
    /// <returns>Kết quả cập nhật</returns>
    [HttpPut]
    public async Task<IActionResult> UpdateSapoOmniConfig([FromBody] UpdateSapoOmniConfigDto dto)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), dto.ShopId);
            if (!baseDto.IsSuccess) 
                return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var webhookIds = await CreateWebHooksAsync(dto.DomainApi, dto.AccessToken);
            
            if (webhookIds != null && webhookIds.Count > 1)
            {
                // Tìm item tồn tại theo ID và ShopId
                var itemExist = await _sapoOmniFlow.GetConfigsByShopIdAsync( dto.ShopId);
                
                if (itemExist != null && 
                    itemExist.DomainApi.Equals(dto.DomainApi, StringComparison.OrdinalIgnoreCase))
                {
                    // Cập nhật thông tin
                    var result = await _sapoOmniFlow.UpdateConfigAsync(dto);
                    
                    if (result)
                    {
                        return ResponseData(new { 
                            Timestamp = DateTimes.Now(), 
                            Result = true,
                            Message = "Cập nhật kết nối SapoOmni thành công"
                        });
                    }
                }
            }

            // Trường hợp thất bại
            return ResponseData(new { 
                Timestamp = DateTimes.Now(), 
                Result = false, 
                Message = "Cập nhật kết nối SapoOmni thất bại" 
            });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in UpdateSapoOmniConfig: {ex.Message}", ex);
            return ResponseData(new { 
                Timestamp = DateTimes.Now(), 
                Result = false, 
                Message = ex.Message 
            });
        }
    }

    /// <summary>
    /// Xóa cấu hình SapoOmni
    /// </summary>
    /// <param name="shopId">Shop ID</param>
    /// <param name="domainApi">Domain API của SapoOmni</param>
    /// <returns>Kết quả xóa</returns>
    [HttpDelete]
    public async Task<IActionResult> DeleteSapoOmniConfig(string shopId, string domainApi)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) 
                return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var deleted = await _sapoOmniFlow.DeleteConfigAsync(shopId, domainApi);
            
            return ResponseData(new { 
                Timestamp = DateTimes.Now(), 
                Result = deleted,
                Message = deleted ? "Xóa cấu hình SapoOmni thành công" : "Xóa cấu hình SapoOmni thất bại"
            });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in DeleteSapoOmniConfig: {ex.Message}", ex);
            return ResponseData(new { 
                Timestamp = DateTimes.Now(), 
                Result = false, 
                Message = ex.Message 
            });
        }
    }

    /// <summary>
    /// Test kết nối SapoOmni
    /// </summary>
    /// <param name="dto">Thông tin test kết nối</param>
    /// <returns>Kết quả test kết nối</returns>
    [HttpPost("test-connection")]
    [AllowAnonymous]
    public async Task<IActionResult> TestSapoOmniConnection([FromBody] TestSapoOmniConnectionDto dto)
    {
        try
        {

            var webhookIds = await CreateWebHooksAsync(dto.DomainApi, dto.AccessToken);

            var testResult = await TestConnectionAsync(dto.DomainApi, dto.AccessToken);
            
            return ResponseData(new { 
                Timestamp = DateTimes.Now(), 
                Result = testResult.IsSuccess,
                Message = testResult.Message,
                Data = testResult.Data
            });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in TestSapoOmniConnection: {ex.Message}", ex);
            return ResponseData(new { 
                Timestamp = DateTimes.Now(), 
                Result = false, 
                Message = ex.Message 
            });
        }
    }

    #region Private Methods

    /// <summary>
    /// Tạo webhook cho SapoOmni
    /// </summary>
    private async Task<List<long>> CreateWebHooksAsync(string domainApi, string accessToken)
    {
        try
        {
            var result = new List<long>();

            var webhookUrl =  GetCurrentWebhookUrl();
            var apiUrlTemplate = _configuration["SapoOmiConfig:ApiUrl"];
            var permissions = _configuration.GetSection("SapoOmiConfig:Permissions").Get<List<string>>();

            if (string.IsNullOrEmpty(apiUrlTemplate) || string.IsNullOrEmpty(webhookUrl))
            {
                _log.Error("SapoOmiConfig missing required settings: WebhookUrl or ApiUrl");
                return new List<long>();
            }

            if (permissions == null || permissions.Count == 0)
            {
                _log.Warn("No permissions configured in SapoOmiConfig:Permissions");
                return new List<long> ();
            }

            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30);

            var apiUrl = string.Format(apiUrlTemplate, domainApi);
            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("X-Sapo-Access-Token", accessToken);

            _log.Info($"Creating webhooks for domain: {domainApi} with {permissions.Count} permissions");

            foreach (var permission in permissions)
            {
                try
                {
                    var webhookData = new
                    {
                        webhook = new
                        {
                            topic = permission,
                            address = webhookUrl,
                            format = "json",
                            api_version = "2023-01"
                        }
                    };

                    var jsonContent = JsonConvert.SerializeObject(webhookData);
                    var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                    _log.Info($"Creating webhook for permission: {permission}");
                    var response = await httpClient.PostAsync(apiUrl, content);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var jsonNode = JsonNode.Parse(responseContent);
                        if (jsonNode?["webhook"]?["id"] != null)
                        {
                            var webhookId = (long)jsonNode["webhook"]["id"];
                            result.Add(webhookId);
                            _log.Info($"Created webhook for permission {permission}: ID {webhookId}");
                        }
                    }
                    else
                    {
                        _log.Warn($"Failed to create webhook for permission {permission}: {response.StatusCode} - {responseContent}");

                        if (response.StatusCode == System.Net.HttpStatusCode.UnprocessableEntity &&
                            responseContent.Contains("already exists"))
                        {
                            var existingWebhookId = await FindExistingWebhook(httpClient, domainApi, accessToken, permission, webhookUrl);
                            if (existingWebhookId.HasValue)
                            {
                                result.Add(existingWebhookId.Value);
                                _log.Info($"Found existing webhook for permission {permission}: ID {existingWebhookId.Value}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _log.Error($"Error creating webhook for permission {permission}: {ex.Message}", ex);
                }
            }

            var message = result.Count > 0
                ? $"Successfully created/found {result.Count} webhooks out of {permissions.Count} permissions"
                : "No webhooks created";

            _log.Info($"Webhook creation completed: {message}");
            return new List<long>();
        }
        catch (Exception ex)
        {
            _log.Error($"Error creating webhooks: {ex.Message}", ex);
            return new List<long>();
        }
    }

    /// <summary>
    /// Find existing webhook by topic and address
    /// </summary>
    private async Task<long?> FindExistingWebhook(HttpClient httpClient, string domainApi, string accessToken, string topic, string webhookUrl)
    {
        try
        {
            var listUrl = $"https://{domainApi}/admin/webhooks.json";
            var response = await httpClient.GetAsync(listUrl);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var jsonNode = JsonNode.Parse(content);
                var webhooks = jsonNode?["webhooks"]?.AsArray();

                if (webhooks != null)
                {
                    foreach (var webhook in webhooks)
                    {
                        var webhookTopic = webhook?["topic"]?.ToString();
                        var webhookAddress = webhook?["address"]?.ToString();
                        var webhookId = webhook?["id"]?.AsValue();

                        if (webhookTopic == topic && webhookAddress == webhookUrl && webhookId != null)
                        {
                            return (long)webhookId;
                        }
                    }
                }
            }
            else
            {
                _log.Warn($"Failed to list existing webhooks: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _log.Error($"Error finding existing webhook: {ex.Message}", ex);
        }

        return null;
    }

    /// <summary>
    /// Test kết nối với SapoOmni
    /// </summary>
    private async Task<(bool IsSuccess, string Message, object Data)> TestConnectionAsync(string domainApi, string accessToken)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            var testUrl = $"https://{domainApi}/admin/products.json";
            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("X-Sapo-Access-Token", accessToken);

            var response = await httpClient.GetAsync(testUrl);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var jsonNode = JsonNode.Parse(content);
                return (true, "Kết nối thành công", jsonNode?["shop"]);
            }
            else
            {
                return (false, $"Kết nối thất bại: {response.StatusCode}", content);
            }
        }
        catch (Exception ex)
        {
            return (false, $"Lỗi kết nối: {ex.Message}", null);
        }
    }
    private string GetCurrentWebhookUrl()
    {
        try
        {
            var request = HttpContext.Request;
            var scheme = request.Scheme;
            var host = request.Host.Value;

            var baseUrl = $"{scheme}://{host}";

            var webhookPath = "/api/webhook/ThirdPartySapoOmni";

            var webhookUrl = $"{baseUrl}{webhookPath}";

            _log.Info($"Generated webhook URL: {webhookUrl}");
            return webhookUrl;
        }
        catch (Exception ex)
        {
            _log.Error($"Error generating webhook URL: {ex.Message}", ex);

            var fallbackUrl = _configuration["SapoOmiConfig:WebhookUrl"];
            if (!string.IsNullOrEmpty(fallbackUrl))
            {
                _log.Warn($"Using fallback webhook URL: {fallbackUrl}");
                return fallbackUrl;
            }

            throw new InvalidOperationException("Cannot determine webhook URL");
        }
    }



    #endregion
}