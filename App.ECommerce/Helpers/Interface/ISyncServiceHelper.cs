using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using App.ECommerce.Repository.Entities;
using App.ECommerce.Units.Enums;

namespace App.ECommerce.Helpers.Interface;

public interface ISyncServiceHelper
{
    /// <summary>
    /// Tìm category theo external ID với switch case cho từng service
    /// </summary>
    Task<Category> FindCategoryByExternalId(string shopId, string externalId, SyncServiceEnum syncService);

    /// <summary>
    /// Tìm item theo external ID với switch case cho từng service
    /// </summary>
    Items FindItemByExternalId(string shopId, string externalId, SyncServiceEnum syncService);

    /// <summary>
    /// Tìm order theo external ID với switch case cho từng service
    /// </summary>
    Order FindOrderByExternalId(string shopId, string externalId, SyncServiceEnum syncService);
}