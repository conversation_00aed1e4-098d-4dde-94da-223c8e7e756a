using App.ECommerce.Helpers.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Units.Enums;

namespace App.ECommerce.Helpers;

public class SyncServiceHelper : ISyncServiceHelper
{
    protected readonly ICategoryRepository _categoryRepository;
    protected readonly IItemsRepository _itemsRepository;
    protected readonly IOrderRepository _orderRepository;

    public SyncServiceHelper(
        ICategoryRepository categoryRepository,
        IItemsRepository itemsRepository,
        IOrderRepository orderRepository
    )
    {
        _categoryRepository = categoryRepository;
        _itemsRepository = itemsRepository;
        _orderRepository = orderRepository;
    }

    /// <summary>
    /// Tìm category theo external ID với switch case cho từng service
    /// </summary>
    public async Task<Category> FindCategoryByExternalId(string shopId, string externalId, SyncServiceEnum syncService)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _categoryRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.NhanhVN),
            SyncServiceEnum.Sapo => await _categoryRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.Sapo),
            SyncServiceEnum.KiotViet => await _categoryRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.KiotViet),
            SyncServiceEnum.Odoo => await _categoryRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.Odoo),
            _ => throw new ArgumentException($"Không hỗ trợ sync service: {syncService}")
        };
    }

    /// <summary>
    /// Tìm item theo external ID với switch case cho từng service
    /// </summary>
    public Items FindItemByExternalId(string shopId, string externalId, SyncServiceEnum syncService)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => _itemsRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.NhanhVN),
            SyncServiceEnum.Sapo => _itemsRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.Sapo),
            SyncServiceEnum.KiotViet => _itemsRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.KiotViet),
            SyncServiceEnum.Odoo => _itemsRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.Odoo),
            _ => throw new ArgumentException($"Không hỗ trợ sync service: {syncService}")
        };
    }

    /// <summary>
    /// Tìm order theo external ID với switch case cho từng service
    /// </summary>
    public Order FindOrderByExternalId(string shopId, string externalId, SyncServiceEnum syncService)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => _orderRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.NhanhVN),
            SyncServiceEnum.Sapo => _orderRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.Sapo),
            SyncServiceEnum.KiotViet => _orderRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.KiotViet),
            SyncServiceEnum.Odoo => _orderRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.Odoo),
            _ => throw new ArgumentException($"Không hỗ trợ sync service: {syncService}")
        };
    }
}
