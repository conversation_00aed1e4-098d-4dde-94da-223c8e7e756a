using App.ECommerce.Helpers.Interface;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.SapoOmniDtos;
using App.ECommerce.Units.Abstractions.Entities;

using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

using System;
using System.Threading.Tasks;

namespace App.ECommerce.Helpers;

public class SapoHelper : ISapoHelper
{
    private readonly ISapoOmniFlow _sapoOmniFlow;
    private readonly ILogger<SapoHelper> _logger;

    public SapoHelper(ISapoOmniFlow sapoOmniFlow, ILogger<SapoHelper> logger)
    {
        _sapoOmniFlow = sapoOmniFlow;
        _logger = logger;
    }

    public async Task<Result<SyncServiceConfig>> SaveSapoConfig(SyncServiceConfigDto dto)
    {
        var sapoConfigDto = new SapoOmniConfigDto
        {
            ShopId = dto.ShopId,
            DomainApi = dto.DomainApi,
            AccessToken = dto.AccessToken,
            ClientId = dto.ClientId,
            ClientSecret = dto.ClientSecret,

        };
        var success = await _sapoOmniFlow.SaveConfigAsync(sapoConfigDto);
        if (success)
        {
            var config = await GetSapoConfig(dto.ShopId);
            return Result<SyncServiceConfig>.Success(config);
        }
        return Result<SyncServiceConfig>.Failure("Failed to save Sapo config.");
    }

    public async Task<SyncServiceConfig> GetSapoConfig(string shopId)
    {
        var config = await _sapoOmniFlow.GetConfigsByShopIdAsync(shopId);
        if (config == null) return null;

        return new SyncServiceConfig
        {
            ShopId = config.ShopId,
            AdditionalConfig = config.DomainApi,
            AccessCode = config.AccessToken,
            Status = config.Status,
        };
    }

    public async Task<Result<bool>> DeleteSapoConfig(string shopId)
    {
        var config = await _sapoOmniFlow.GetConfigsByShopIdAsync(shopId);
        if (config == null || string.IsNullOrEmpty(config.DomainApi))
        {
            return Result<bool>.Failure("Config not found.");
        }
        var success = await _sapoOmniFlow.DeleteConfigAsync(shopId, config.DomainApi);
        return success ? Result<bool>.Success(true) : Result<bool>.Failure("Failed to delete config.");
    }

    public async Task<Result<SyncServiceConfig>> UpdateSapoAccessCode(string shopId, string accessCode)
    {
        var config = await GetSapoConfig(shopId);
        if (config == null || string.IsNullOrEmpty(config.AdditionalConfig))
        {
            return Result<SyncServiceConfig>.Failure("Config not found.");
        }
        var success = await _sapoOmniFlow.UpdateAccessTokenAsync(config.AdditionalConfig, shopId, accessCode);
        if (success)
        {
            var updatedConfig = await GetSapoConfig(shopId);
            return Result<SyncServiceConfig>.Success(updatedConfig);
        }
        return Result<SyncServiceConfig>.Failure("Failed to update access code.");
    }

    public async Task<Result<bool>> SyncSapoProductFromWebhook(object productData, string shopId) => Result<bool>.Failure("Sapo product sync chưa được implement");
    public async Task<Result<bool>> SyncSapoOrderFromWebhook(object orderData, string shopId) => Result<bool>.Failure("Sapo order sync chưa được implement");
    public async Task<Result<bool>> SyncSapoCustomerFromWebhook(object customerData, string shopId) => Result<bool>.Failure("Sapo customer sync chưa được implement");
    public async Task<Result<bool>> DeleteSapoProductsFromWebhook(object productIds) => Result<bool>.Failure("Sapo delete products chưa được implement");
    public async Task<Result<bool>> DeleteSapoOrderFromWebhook(object orderData, string shopId) => Result<bool>.Failure("Sapo delete order chưa được implement");
    public async Task<Result<bool>> UpdateOrderToSapo(Order order, string shopId) => Result<bool>.Failure("Sapo update order chưa được implement");


}
