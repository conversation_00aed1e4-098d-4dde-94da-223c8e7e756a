name: Deploy .NET Testing

on:
  push:
    branches:
      - "develop"
      - "test_*"
  workflow_dispatch:

env:
  app_solution: "${{secrets.APP_SOLUTION}}"
  app_image: "${{secrets.DOCKER_HUB_IMAGE}}"
  app_docker_name: "${{secrets.DOCKER_HUB_USERNAME}}"
  app_docker_pass: "${{secrets.DOCKER_HUB_PASSWORD}}"
  app_docker_container: "${{secrets.DOCKER_HUB_CONTAINER}}"
  app_remote_host: "${{secrets.TEST_REMOTE_HOST}}"
  app_remote_user: "${{secrets.TEST_REMOTE_USER}}"
  app_remote_password: "${{secrets.TEST_REMOTE_PASSWORD}}"
  app_remote_service: "${{secrets.STAG_REMOTE_SERVICE}}"
  app_remote_dir: "${{secrets.STAG_REMOTE_DIR}}"
  app_remote_build_dir: "${{secrets.STAG_REMOTE_BUILD_DIR}}"

jobs:
  build:
    runs-on: [self-hosted, dev]
    steps:
      - uses: actions/checkout@v4
      - name: Extract version from tag
        run: |
          echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_ENV
      - name: Build docker Image
        run: |
          echo $PWD
          jq '.ASPNETCORE_ENVIRONMENT = "Testing"' env.json > temp.json && mv temp.json env.json
          docker build --platform=linux/amd64 -t retail-backend .
          
          # Step 1: Stop and remove backend container
          echo "Stopping and removing backend container..."
          docker stop backend || true
          docker rm backend || true
          
          # Step 2: Run new backend container
          echo "Starting new backend container..."
          docker run -d --name backend -p 5077:5077 --restart on-failure:5 retail-backend
          
          # Step 3: Manual health check for 60 seconds
          echo "Starting manual health check for backend container..."
          start_time=$(date +%s)
          health_check_passed=false
          
          while true; do
            current_time=$(date +%s)
            elapsed=$((current_time - start_time))
            
            # Check if 360 seconds timeout
            if [ $elapsed -gt 360 ]; then
              echo "Health check timeout after 360 seconds"
              echo "Final attempt to check API..."
              curl_result=$(curl -X POST -s -o /dev/null -w "%{http_code}" http://localhost:5077/minimumversion || echo "000")
              echo "Final curl result: $curl_result"
              echo "DEPLOYMENT_STATUS=failed" >> $GITHUB_ENV
              break
            fi
            
            # Check if container is still running
            if ! docker ps --format '{{.Names}}' | grep -q "^backend$"; then
              echo "Backend container is not running anymore!"
              echo "Container logs:"
              docker logs backend --tail 20 || true
              break
            fi
            
            # Manual health check using curl
            echo "Checking health endpoint... (${elapsed}s elapsed)"
            http_code=$(curl -X POST -s -o /dev/null -w "%{http_code}" http://localhost:5077/minimumversion || echo "000")
            
            if [ "$http_code" = "200" ]; then
              echo "Backend container is healthy! (HTTP 200)"
              health_check_passed=true
              break
            else
              echo "Health check failed - HTTP code: $http_code"
            fi
            
            # Show progress every 15 seconds
            if [ $((elapsed % 15)) -eq 0 ] && [ $elapsed -gt 0 ]; then
              echo "=== Progress update at ${elapsed}s ==="
              echo "Container status: $(docker ps --format '{{.Status}}' --filter name=backend)"
              echo "Recent logs:"
              docker logs backend --tail 3 2>/dev/null || echo "No logs available"
              echo "================================"
            fi
            
            # Wait before next check
            sleep 15
          done
          
          # Step 4: Based on health check result
          if [ "$health_check_passed" = "true" ]; then
            echo "Backend healthy - proceeding with backend2 deployment..."
            
            # Stop and remove backend2
            echo "Stopping and removing backend2 container..."
            docker stop backend2 || true
            docker rm backend2 || true
            
            # Run new backend2 container
            echo "Starting new backend2 container..."
            docker run -d --name backend2 -p 5078:5077 --restart on-failure:5 retail-backend
            
            # Manual health check for backend2
            echo "Manual health check for backend2..."
            sleep 20  # Wait for startup
            
            backend2_attempts=0
            backend2_healthy=false
            
            while [ $backend2_attempts -lt 8 ]; do  # 8 attempts * 5s = 40s max
              backend2_attempts=$((backend2_attempts + 1))
              backend2_code=$(curl -X POST -s -o /dev/null -w "%{http_code}" http://localhost:5078/minimumversion || echo "000")
              
              if [ "$backend2_code" = "200" ]; then
                echo "Backend2 is healthy! (HTTP 200)"
                backend2_healthy=true
                break
              else
                echo "Backend2 check attempt $backend2_attempts failed - HTTP code: $backend2_code"
                sleep 15
              fi
            done
            
            if [ "$backend2_healthy" = "true" ]; then
              echo "deployment_status=success" >> $GITHUB_OUTPUT
            else
              echo "deployment_status=partial" >> $GITHUB_OUTPUT
            fi
            
          else
            echo "Backend health check failed - deployment aborted"
            echo "Container logs:"
            docker logs backend --tail 50 || true
            echo "Container status:"
            docker ps --filter name=backend || true

            echo "deployment_status=failed" >> $GITHUB_OUTPUT
          fi

          docker builder prune -a -f
          docker image prune -a -f
        working-directory: ./App.ECommerce

  send-telegram-noti:
    needs: build
    runs-on: [self-hosted, dev]
    if: always()
    steps:
      - name: get timestamp
        run: echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
      
      - name: Send success notification
        if: needs.build.outputs.deployment_status == 'success'
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-**********' \
          --data-urlencode 'text=[${{ env.timestamp }}] 🚀 BE DEV Success - ${{ github.event.head_commit.message }}'

      - name: Send failure notification
        if: needs.build.outputs.deployment_status == 'failed'
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-**********' \
          --data-urlencode 'text=[${{ env.timestamp }}] 🚨 BE DEV Failed - ${{ github.event.head_commit.message }}'

      - name: Send build failure notification
        if: needs.build.result == 'failure' && needs.build.outputs.deployment_status != 'failed'
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-**********' \
          --data-urlencode 'text=[${{ env.timestamp }}] 💥 BE DEV Build Failed - ${{ github.event.head_commit.message }}'