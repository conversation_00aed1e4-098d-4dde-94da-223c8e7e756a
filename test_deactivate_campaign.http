### Test API tắt chiến dịch của shop
PUT {{baseUrl}}/api/partner/game-campaign/deactivate?shopId={{shopId}}
Authorization: Bearer {{token}}
Content-Type: application/json

### Test API lấy danh sách chiến dịch để kiểm tra trạng thái
GET {{baseUrl}}/api/partner/game-campaign?shopId={{shopId}}
Authorization: Bearer {{token}}

### Test API kích hoạt lại chiến dịch (để test)
PUT {{baseUrl}}/api/partner/game-campaign/{{campaignId}}/activate?shopId={{shopId}}
Authorization: Bearer {{token}}

### Variables
# @baseUrl = https://your-api-domain.com
# @shopId = your-shop-id
# @campaignId = your-campaign-id
# @token = your-jwt-token
