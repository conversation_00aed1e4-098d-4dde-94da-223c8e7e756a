### Test API tắt chiến dịch của shop (set IsActived = false)
PUT {{baseUrl}}/api/partner/game-campaign/deactivate?shopId={{shopId}}
Authorization: Bearer {{token}}
Content-Type: application/json

### Test API lấy danh sách chiến dịch để kiểm tra trạng thái (Active sẽ là false)
GET {{baseUrl}}/api/partner/game-campaign?shopId={{shopId}}
Authorization: Bearer {{token}}

### Test API kích hoạt lại chiến dịch (set IsActived = true)
PUT {{baseUrl}}/api/partner/game-campaign/{{campaignId}}/activate?shopId={{shopId}}
Authorization: Bearer {{token}}

### Test API lấy danh sách chiến dịch sau khi activate (Active sẽ là true)
GET {{baseUrl}}/api/partner/game-campaign?shopId={{shopId}}
Authorization: Bearer {{token}}

### Variables
# @baseUrl = https://your-api-domain.com
# @shopId = your-shop-id
# @campaignId = your-campaign-id
# @token = your-jwt-token
